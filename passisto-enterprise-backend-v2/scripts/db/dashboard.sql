-- PostgreS<PERSON> Script to Create Materialized Views for Dashboard Statistics

-- -----------------------------------------------------------
-- 1. mv_company_overview_stats: Aggregates overall company statistics
--    Counts total users, active users, groups, forms, emails, integrations.
--    Updated to remove direct usage fields from Company.
-- -----------------------------------------------------------
CREATE MATERIALIZED VIEW "mv_company_overview_stats" AS
SELECT
    "c"."id" as "company_id",
    "c"."name" as "company_name",
    COUNT(DISTINCT "u"."id") as "total_users",
    COUNT(DISTINCT CASE WHEN "u"."isActive" = true THEN "u"."id" END) as "active_users",
    COUNT(DISTINCT "g"."id") as "total_groups",
    COUNT(DISTINCT "f"."id") as "total_forms",
    COUNT(DISTINCT "e"."id") as "total_emails",
    COUNT(DISTINCT "i"."id") as "total_integrations",
    -- Removed "c"."emailAgentsUsed" and "c"."aiInterviewHoursUsed" as they are now in CompanyUsage
    "c"."createdAt",
    "c"."updatedAt"
FROM public."Company" "c"
LEFT JOIN public."User" "u" ON "c"."id" = "u"."companyId"
LEFT JOIN public."Group" "g" ON "c"."id" = "g"."companyId"
LEFT JOIN public."Form" "f" ON "c"."id" = "f"."companyId"
LEFT JOIN public."Email" "e" ON "c"."id" = "e"."companyId"
LEFT JOIN public."Integration" "i" ON "c"."id" = "i"."companyId"
GROUP BY "c"."id", "c"."name", "c"."createdAt", "c"."updatedAt";

-- UNIQUE INDEX for mv_company_overview_stats
CREATE UNIQUE INDEX "mv_company_overview_stats_unique_company_id" ON "mv_company_overview_stats"("company_id");


-- -----------------------------------------------------------
-- 2. mv_group_distribution: Summarizes group membership and activity
-- -----------------------------------------------------------
CREATE MATERIALIZED VIEW "mv_group_distribution" AS
SELECT
    "g"."id" as "group_id",
    "g"."name" as "group_name",
    "g"."companyId" as "company_id",
    COUNT(DISTINCT "ug"."userId") as "member_count",
    COUNT(DISTINCT "gi"."integrationId") as "integration_count",
    COUNT(DISTINCT "gp"."id") as "permission_count",
    CASE WHEN COUNT(DISTINCT "ug"."userId") > 0 THEN 'active' ELSE 'inactive' END as "status",
    "g"."createdAt",
    "g"."updatedAt"
FROM public."Group" "g"
LEFT JOIN public."UserGroup" "ug" ON "g"."id" = "ug"."groupId"
LEFT JOIN public."GroupIntegration" "gi" ON "g"."id" = "gi"."groupId"
LEFT JOIN public."GroupPermission" "gp" ON "g"."id" = "gp"."groupId"
GROUP BY "g"."id", "g"."name", "g"."companyId", "g"."createdAt", "g"."updatedAt";

-- UNIQUE INDEX for mv_group_distribution
CREATE UNIQUE INDEX "mv_group_distribution_unique_group_id" ON "mv_group_distribution"("group_id");


-- -----------------------------------------------------------
-- 3. mv_user_activity_stats: Tracks user-specific activity metrics
-- -----------------------------------------------------------
CREATE MATERIALIZED VIEW "mv_user_activity_stats" AS
SELECT
    "u"."id" as "user_id",
    "u"."companyId" as "company_id",
    "u"."firstName",
    "u"."lastName",
    "u"."email",
    "u"."isActive",
    COUNT(DISTINCT "ug"."groupId") as "group_count",
    COUNT(DISTINCT "w"."id") as "workflow_count",
    COUNT(DISTINCT "nr"."id") as "assigned_tasks",
    COUNT(DISTINCT CASE WHEN "nr"."status" = 'SUCCESS' THEN "nr"."id" END) as "completed_tasks",
    "u"."createdAt",
    "u"."updatedAt"
FROM public."User" "u"
LEFT JOIN public."UserGroup" "ug" ON "u"."id" = "ug"."userId"
LEFT JOIN workflow."Workflow" "w" ON "u"."id" = "w"."userId"
LEFT JOIN workflow."NodeRun" "nr" ON "u"."id" = "nr"."assigneeId"
GROUP BY "u"."id", "u"."companyId", "u"."firstName", "u"."lastName", "u"."email", "u"."isActive", "u"."createdAt", "u"."updatedAt";

-- UNIQUE INDEX for mv_user_activity_stats
CREATE UNIQUE INDEX "mv_user_activity_stats_unique_user_id" ON "mv_user_activity_stats"("user_id");


-- -----------------------------------------------------------
-- 4. mv_integration_status_summary: Summarizes integration status
-- -----------------------------------------------------------
CREATE MATERIALIZED VIEW "mv_integration_status_summary" AS
SELECT
    "i"."companyId" as "company_id",
    "i"."providerType",
    "i"."status",
    COUNT(*) as "count",
    COUNT(DISTINCT "gi"."groupId") as "assigned_groups",
    MIN("i"."createdAt") as "first_created",
    MAX("i"."updatedAt") as "last_updated"
FROM public."Integration" "i"
LEFT JOIN public."GroupIntegration" "gi" ON "i"."id" = "gi"."integrationId"
GROUP BY "i"."companyId", "i"."providerType", "i"."status";

-- UNIQUE INDEX for mv_integration_status_summary
CREATE UNIQUE INDEX "mv_integration_status_summary_unique_key" ON "mv_integration_status_summary"("company_id", "providerType", "status");


-- -----------------------------------------------------------
-- 5. mv_workflow_execution_stats: Gathers statistics on workflow runs
-- -----------------------------------------------------------
CREATE MATERIALIZED VIEW "mv_workflow_execution_stats" AS
SELECT
    "w"."userId",
    "u"."companyId" as "company_id",
    "w"."status" as "workflow_status",
    COUNT(DISTINCT "w"."id") as "workflow_count",
    COUNT(DISTINCT "wr"."id") as "total_runs",
    COUNT(DISTINCT CASE WHEN "wr"."status" = 'SUCCESS' THEN "wr"."id" END) as "successful_runs",
    COUNT(DISTINCT CASE WHEN "wr"."status" = 'FAILED' THEN "wr"."id" END) as "failed_runs",
    COUNT(DISTINCT CASE WHEN "wr"."status" = 'WAITING_FOR_USER' THEN "wr"."id" END) as "pending_runs",
    AVG(EXTRACT(EPOCH FROM ("wr"."finishedAt" - "wr"."startedAt"))/60) as "avg_execution_time_minutes"
FROM workflow."Workflow" "w"
JOIN public."User" "u" ON "w"."userId" = "u"."id"
LEFT JOIN workflow."WorkflowRun" "wr" ON "w"."id" = "wr"."workflowId"
GROUP BY "w"."userId", "u"."companyId", "w"."status";

-- UNIQUE INDEX for mv_workflow_execution_stats
CREATE UNIQUE INDEX "mv_workflow_execution_stats_unique_key" ON "mv_workflow_execution_stats"("userId", "company_id", "workflow_status");


-- -----------------------------------------------------------
-- 6. mv_node_task_stats: Provides statistics on node task assignments and completions
-- -----------------------------------------------------------
CREATE MATERIALIZED VIEW "mv_node_task_stats" AS
SELECT
    "nr"."assigneeId" as "user_id",
    "u"."companyId" as "company_id",
    "nr"."status",
    COUNT(*) as "task_count",
    COUNT(DISTINCT "nr"."workflowRunId") as "workflow_runs_involved",
    AVG(EXTRACT(EPOCH FROM ("nr"."finishedAt" - "nr"."startedAt"))/60) as "avg_completion_time_minutes",
    COUNT(DISTINCT "f"."id") as "files_attached"
FROM workflow."NodeRun" "nr"
JOIN public."User" "u" ON "nr"."assigneeId" = "u"."id"
LEFT JOIN workflow."File" "f" ON "nr"."id" = "f"."nodeRunId"
WHERE "nr"."assigneeId" IS NOT NULL
GROUP BY "nr"."assigneeId", "u"."companyId", "nr"."status";

-- UNIQUE INDEX for mv_node_task_stats
CREATE UNIQUE INDEX "mv_node_task_stats_unique_key" ON "mv_node_task_stats"("user_id", "company_id", "status");


-- -----------------------------------------------------------
-- 7. mv_monthly_growth_stats: Tracks monthly growth across different entities
-- -----------------------------------------------------------
CREATE MATERIALIZED VIEW "mv_monthly_growth_stats" AS
SELECT
    "company_id",
    date_trunc('month', "created_at") as "month", -- This will be a DATE/TIMESTAMP type
    'users' as "metric_type", -- This metric_type is not directly used in your API, but good for generalization
    COUNT(*) as "count"
FROM (
    SELECT "companyId" as "company_id", "createdAt" as "created_at" FROM public."User"
    UNION ALL
    SELECT "companyId" as "company_id", "createdAt" as "created_at" FROM public."Group"
    UNION ALL
    SELECT "companyId" as "company_id", "createdAt" as "created_at" FROM public."Form"
    UNION ALL
    SELECT "companyId" as "company_id", "createdAt" as "created_at" FROM public."Email"
    UNION ALL
    SELECT "companyId" as "company_id", "createdAt" as "created_at" FROM public."Integration"
) "growth_data"
GROUP BY "company_id", date_trunc('month', "created_at"), "metric_type"
ORDER BY "company_id", "month"; -- Order by actual month date for correct chronological sort

-- UNIQUE INDEX for mv_monthly_growth_stats
CREATE UNIQUE INDEX "mv_monthly_growth_stats_unique_key" ON "mv_monthly_growth_stats"("company_id", "month", "metric_type");


-- -----------------------------------------------------------
-- 8. mv_ai_tools_usage_stats: Summarizes AI tool usage
--    Updated based on provided Prisma schema, using CompanyUsage for usage metrics.
-- -----------------------------------------------------------
CREATE MATERIALIZED VIEW "mv_ai_tools_usage_stats" AS
SELECT
    "c"."id" as "company_id",
    "c"."name" as "company_name",

    -- Interview Statistics:
    -- The schema does not contain an "Interview" model.
    -- These counts will remain 0 unless an Interview model is added and joined.
    0 as "total_interviews",
    0 as "completed_interviews",
    0 as "pending_interviews",

    -- Email Statistics:
    COUNT(DISTINCT "e"."id") as "total_emails", -- Counts configured emails
    COALESCE(SUM(CASE WHEN "cu_email"."key" = 'emailAgentsUsed' THEN "cu_email"."value" ELSE 0 END), 0) as "emails_sent", -- Get from CompanyUsage
    -- No direct 'EmailOpenLog' model in schema. This count will remain 0.
    0 as "emails_opened",

    -- Form Statistics:
    COUNT(DISTINCT "f"."id") as "total_forms", -- Counts created forms
    COUNT(DISTINCT "fr"."id") FILTER (WHERE "fr"."formId" IS NOT NULL) as "form_submissions", -- Counts FormResponse records
    -- No direct 'FormView' model in schema for conversion rate. This will remain 0.0.
    0.0 as "conversion_rate",

    -- Workflow Statistics (assumed to be part of AI tools in this context)
    COUNT(DISTINCT "w"."id") as "total_workflows",
    COUNT(DISTINCT "wr"."id") as "workflow_runs",
    COUNT(DISTINCT CASE WHEN "wr"."status" = 'SUCCESS' THEN "wr"."id" END) as "successful_workflows"

FROM public."Company" "c"
LEFT JOIN public."Email" "e" ON "c"."id" = "e"."companyId"
LEFT JOIN public."Form" "f" ON "c"."id" = "f"."companyId"
LEFT JOIN public."User" "u" ON "c"."id" = "u"."companyId"
LEFT JOIN workflow."Workflow" "w" ON "u"."id" = "w"."userId"
LEFT JOIN workflow."WorkflowRun" "wr" ON "w"."id" = "wr"."workflowId"
LEFT JOIN public."FormResponse" "fr" ON "f"."id" = "fr"."formId" AND "c"."id" = "f"."companyId" -- Join for form submissions
LEFT JOIN public."CompanyUsage" "cu_email" ON "c"."id" = "cu_email"."companyId" AND "cu_email"."key" = 'emailAgentsUsed'
-- No joins for Interview, EmailSendLog, EmailOpenLog, FormView as models are not in schema
GROUP BY "c"."id", "c"."name";


-- UNIQUE INDEX for mv_ai_tools_usage_stats
CREATE UNIQUE INDEX "mv_ai_tools_usage_stats_unique_company_id" ON "mv_ai_tools_usage_stats"("company_id");


-- -----------------------------------------------------------
-- General Indexes for Better Performance (these are non-unique and still useful)
-- Create general indexes on the company_id column for efficient filtering
-- -----------------------------------------------------------
CREATE INDEX "idx_mv_company_overview_stats_company_id" ON "mv_company_overview_stats"("company_id");
CREATE INDEX "idx_mv_group_distribution_company_id" ON "mv_group_distribution"("company_id");
CREATE INDEX "idx_mv_user_activity_stats_company_id" ON "mv_user_activity_stats"("company_id");
CREATE INDEX "idx_mv_integration_status_company_id" ON "mv_integration_status_summary"("company_id");
CREATE INDEX "idx_mv_workflow_execution_company_id" ON "mv_workflow_execution_stats"("company_id");
CREATE INDEX "idx_mv_node_task_stats_company_id" ON "mv_node_task_stats"("company_id");
CREATE INDEX "idx_mv_monthly_growth_company_id" ON "mv_monthly_growth_stats"("company_id");
CREATE INDEX "idx_mv_ai_tools_usage_company_id" ON "mv_ai_tools_usage_stats"("company_id");


-- -----------------------------------------------------------
-- Refresh Function
-- This function refreshes all dashboard materialized views concurrently.
-- Call this function periodically (e.g., via a cron job) or
-- after significant data changes to keep the dashboard data up-to-date.
-- -----------------------------------------------------------
CREATE OR REPLACE FUNCTION "refresh_dashboard_materialized_views"()
RETURNS void AS $$
BEGIN
    -- Refresh each materialized view concurrently to allow reads during refresh
    REFRESH MATERIALIZED VIEW CONCURRENTLY "mv_company_overview_stats";
    REFRESH MATERIALIZED VIEW CONCURRENTLY "mv_group_distribution";
    REFRESH MATERIALIZED VIEW CONCURRENTLY "mv_user_activity_stats";
    REFRESH MATERIALIZED VIEW CONCURRENTLY "mv_integration_status_summary";
    REFRESH MATERIALIZED VIEW CONCURRENTLY "mv_workflow_execution_stats";
    REFRESH MATERIALIZED VIEW CONCURRENTLY "mv_node_task_stats";
    REFRESH MATERIALIZED VIEW CONCURRENTLY "mv_monthly_growth_stats";
    REFRESH MATERIALIZED VIEW CONCURRENTLY "mv_ai_tools_usage_stats";
END;
$$ LANGUAGE plpgsql;


-- -----------------------------------------------------------
-- Optional: Scheduler Function (for external scheduling via cron, cloud scheduler etc.)
-- This function simply calls the refresh function. It's illustrative.
-- In a real production environment, you would trigger the refresh_dashboard_materialized_views()
-- directly or via your backend service as part of a scheduled job.
-- -----------------------------------------------------------
CREATE OR REPLACE FUNCTION "schedule_dashboard_refresh"()
RETURNS void AS $$
BEGIN
    PERFORM "refresh_dashboard_materialized_views"();
END;
$$ LANGUAGE plpgsql;
