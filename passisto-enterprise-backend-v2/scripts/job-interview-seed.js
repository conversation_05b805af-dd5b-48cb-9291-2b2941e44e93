const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

/**
 * This seed script creates a job interview workflow scenario.
 * It creates:
 * 1. A workflow that processes job interview recordings
 * 2. The workflow includes speech-to-text, AI analysis, HR review, and email notification
 *
 * Note: This script works with existing users in the system.
 */
async function main() {
  // Fetch user by email to prevent constraint violations
  const userEmail = '<EMAIL>';

  try {
    console.log('Starting job interview workflow seed...');
    console.log('Looking for user with email:', userEmail);

    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email: userEmail }
    });

    if (!user) {
      throw new Error(`User with email ${userEmail} not found. Please ensure the user exists in the database.`);
    }

    console.log(`Found user: ${user.firstName} ${user.lastName} (${user.email})`);
    const userId = user.id;

    // Note: This script works with existing users in the system

    // Create a workflow for job interview processing
    console.log('Creating job interview workflow...');
    const workflow = await prisma.workflow.create({
      data: {
        name: 'Job Interview Analysis Workflow',
        description: 'Process job interview recordings, generate AI analysis, and collaborate on candidate evaluation',
        status: 'active',
        userId: userId,
        nodes: [
          {
            "id": "start",
            "data": {
              "label": "Start Interview Process",
              "isActive": false,
              "isSkipped": false,
              "isWaiting": false,
              "isCompleted": false
            },
            "type": "start",
            "dragging": false,
            "measured": {
              "width": 241,
              "height": 44
            },
            "position": {
              "x": 604.1225462342776,
              "y": 331.7073260089698
            }
          },
          {
            "id": "speech-to-text",
            "data": {
              "label": "Transcribe Interview Recording",
              "service": "simulated",
              "demoMode": true,
              "isActive": false,
              "hasUpload": true,
              "isSkipped": false,
              "isWaiting": false,
              "sourceType": "audio",
              "description": "Convert the interview audio to text for analysis",
              "isCompleted": false
            },
            "type": "speech-to-text",
            "measured": {
              "width": 302,
              "height": 108
            },
            "position": {
              "x": 600,
              "y": 450
            }
          },
          {
            "id": "email-transcription-complete",
            "data": {
              "to": "<EMAIL>",
              "body": "The interview recording has been successfully transcribed and is ready for AI analysis. The workflow will proceed automatically.\\n\\ntranscription : \\n{speech-to-text.transcription}",
              "label": "Notify HR of Transcription",
              "subject": "Interview Transcription Complete",
              "isActive": false,
              "isSkipped": false,
              "isWaiting": false,
              "isCompleted": false,
              "selectedOutputs": [
                {
                  "key": "transcription",
                  "label": "Transcribe Interview Recording - transcription",
                  "nodeId": "speech-to-text"
                }
              ]
            },
            "type": "email",
            "measured": {
              "width": 268,
              "height": 148
            },
            "position": {
              "x": 600,
              "y": 650
            }
          },
          {
            "id": "ask-ai-analysis",
            "data": {
              "label": "Generate Interview Analysis",
              "model": "openrouter/cypher-alpha:free",
              "prompt": "Analyze the following job interview transcript and provide a comprehensive report including:\\n\\n1. Candidate strengths and weaknesses\\n2. Technical skills assessment\\n3. Communication skills evaluation\\n4. Cultural fit analysis\\n5. Overall recommendation (Hire, Consider, or Reject)\\n\\nFormat the report in a professional structure with clear sections and bullet points where appropriate.\\n\\nTranscription : {speech-to-text.transcription}",
              "isActive": false,
              "isSkipped": false,
              "isWaiting": false,
              "isCompleted": false,
              "selectedOutputs": [
                {
                  "key": "transcription",
                  "label": "Transcribe Interview Recording - transcription",
                  "nodeId": "speech-to-text"
                }
              ]
            },
            "type": "ask-ai",
            "measured": {
              "width": 364,
              "height": 152
            },
            "position": {
              "x": 600,
              "y": 850
            }
          },
          {
            "id": "task-hr-review",
            "data": {
              "label": "HR Manager Review",
              "assignee": "851a34a7-d8a0-4802-b958-8ab75aee483a",
              "isActive": false,
              "isSkipped": false,
              "isWaiting": false,
              "formFields": [
                {
                  "id": "feedback",
                  "type": "textarea",
                  "label": "HR Feedback",
                  "required": true,
                  "placeholder": "Enter your feedback on the interview analysis"
                },
                {
                  "id": "rating",
                  "type": "select",
                  "label": "Candidate Rating",
                  "options": [
                    {
                      "label": "Excellent",
                      "value": "excellent"
                    },
                    {
                      "label": "Good",
                      "value": "good"
                    },
                    {
                      "label": "Average",
                      "value": "average"
                    },
                    {
                      "label": "Below Average",
                      "value": "below_average"
                    },
                    {
                      "label": "Poor",
                      "value": "poor"
                    }
                  ],
                  "required": true
                }
              ],
              "description": "Review the AI-generated interview analysis and provide your feedback",
              "isCompleted": false,
              "selectedOutputs": [
                {
                  "key": "result",
                  "label": "Generate Interview Analysis - result",
                  "nodeId": "ask-ai-analysis"
                }
              ],
              "requiresUserInput": true
            },
            "type": "task",
            "measured": {
              "width": 410,
              "height": 136
            },
            "position": {
              "x": 600,
              "y": 1050
            }
          },
          {
            "id": "decision-candidate-quality",
            "data": {
              "label": "Evaluate Candidate Quality",
              "output": {
                "path": "yes"
              },
              "isActive": false,
              "isSkipped": false,
              "isWaiting": false,
              "conditions": [
                {
                  "id": "cond-1751679647414",
                  "type": "condition",
                  "value": "'excellent'",
                  "nodeId": "task-hr-review",
                  "varName": "rating",
                  "operator": "===",
                  "variable": "task-hr-review|rating",
                  "expression": "inputs['task-hr-review'].rating === 'excellent'"
                }
              ],
              "isCompleted": false,
              "selectedOutputs": []
            },
            "type": "decision",
            "measured": {
              "width": 220,
              "height": 100
            },
            "position": {
              "x": 600,
              "y": 1250
            }
          },
          {
            "id": "task-hiring-manager",
            "data": {
              "label": "Hiring Manager Review",
              "assignee": "851a34a7-d8a0-4802-b958-8ab75aee483a",
              "isActive": false,
              "isSkipped": false,
              "isWaiting": false,
              "formFields": [
                {
                  "id": "technical_assessment",
                  "type": "textarea",
                  "label": "Technical Assessment",
                  "required": true,
                  "placeholder": "Provide your assessment of the candidate's technical skills"
                },
                {
                  "id": "final_decision",
                  "type": "select",
                  "label": "Final Decision",
                  "options": [
                    {
                      "label": "Hire",
                      "value": "hire"
                    },
                    {
                      "label": "Reject",
                      "value": "reject"
                    }
                  ],
                  "required": true
                },
                {
                  "id": "additional_interview",
                  "type": "checkbox",
                  "label": "Additional Interview",
                  "required": false,
                  "placeholder": ""
                }
              ],
              "description": "Review the promising candidate's interview analysis and HR feedback",
              "isCompleted": false,
              "requiresUserInput": true
            },
            "type": "task",
            "measured": {
              "width": 408,
              "height": 116
            },
            "position": {
              "x": 189.2620720742722,
              "y": 1445.422457842345
            }
          },
          {
            "id": "email-rejection",
            "data": {
              "to": "<EMAIL>",
              "body": "This candidate did not meet our requirements based on the interview assessment. Please prepare a rejection letter with appropriate feedback.",
              "label": "Send Rejection Notification",
              "subject": "Candidate Rejection Notification",
              "isActive": false,
              "isSkipped": false,
              "isWaiting": false,
              "isCompleted": false
            },
            "type": "email",
            "measured": {
              "width": 272,
              "height": 128
            },
            "position": {
              "x": 1100,
              "y": 1450
            }
          },
          {
            "id": "decision-final-hire",
            "data": {
              "label": "Is Candidate Hired?",
              "output": {
                "path": "yes"
              },
              "isActive": false,
              "condition": "inputs['task-hiring-manager'].final_decision === 'hire'",
              "isSkipped": false,
              "isWaiting": false,
              "conditions": [
                {
                  "id": "cond-1751679793663",
                  "type": "condition",
                  "value": "'hire'",
                  "nodeId": "task-hiring-manager",
                  "varName": "final_decision",
                  "operator": "===",
                  "variable": "task-hiring-manager|final_decision",
                  "expression": "inputs['task-hiring-manager'].final_decision === 'hire'"
                }
              ],
              "isCompleted": false
            },
            "type": "decision",
            "dragging": false,
            "measured": {
              "width": 220,
              "height": 100
            },
            "position": {
              "x": 23.79359345372998,
              "y": 1597.654094401794
            }
          },
          {
            "id": "decision-additional-interview",
            "data": {
              "label": "Need Additional Interview?",
              "output": {
                "path": "no"
              },
              "isActive": false,
              "condition": "inputs['task-hiring-manager'].final_decision === 'hire'",
              "isSkipped": false,
              "isWaiting": false,
              "conditions": [
                {
                  "id": "cond-1751679770169",
                  "type": "condition",
                  "value": "true",
                  "nodeId": "task-hiring-manager",
                  "varName": "additional_interview",
                  "operator": "===",
                  "variable": "task-hiring-manager|additional_interview",
                  "expression": "inputs['task-hiring-manager'].additional_interview === true"
                }
              ],
              "isCompleted": false
            },
            "type": "decision",
            "dragging": false,
            "measured": {
              "width": 220,
              "height": 100
            },
            "position": {
              "x": 444.6967084536001,
              "y": 1693.300209187685
            }
          },
          {
            "id": "email-offer",
            "data": {
              "to": "<EMAIL>",
              "body": "The hiring manager has approved this candidate for hire. Please prepare an offer letter based on the interview feedback and assessment.",
              "label": "Send Offer Preparation Email",
              "subject": "Prepare Offer for Candidate",
              "isActive": false,
              "isSkipped": false,
              "isWaiting": false,
              "isCompleted": false
            },
            "type": "email",
            "measured": {
              "width": 286,
              "height": 128
            },
            "position": {
              "x": -100,
              "y": 1850
            }
          },
          {
            "id": "email-additional-interview",
            "data": {
              "to": "<EMAIL>",
              "body": "The hiring manager has requested an additional interview with this candidate. Please schedule another round and notify the team.",
              "label": "Request Additional Interview",
              "subject": "Schedule Additional Interview",
              "isActive": false,
              "isSkipped": false,
              "isWaiting": false,
              "isCompleted": false
            },
            "type": "email",
            "measured": {
              "width": 284,
              "height": 128
            },
            "position": {
              "x": 200,
              "y": 1850
            }
          },
          {
            "id": "email-team-notification",
            "data": {
              "to": "<EMAIL>",
              "body": "The job interview process for this candidate has been completed. Please review the attached analysis and decision summary.",
              "label": "Notify Team of Outcome",
              "subject": "Job Interview Process Complete",
              "isActive": false,
              "isSkipped": false,
              "isWaiting": false,
              "isCompleted": false
            },
            "type": "email",
            "measured": {
              "width": 265,
              "height": 128
            },
            "position": {
              "x": 600,
              "y": 2050
            }
          },
          {
            "id": "end",
            "data": {
              "label": "End Process",
              "isActive": false,
              "isSkipped": false,
              "isWaiting": false,
              "isCompleted": false
            },
            "type": "end",
            "measured": {
              "width": 161,
              "height": 44
            },
            "position": {
              "x": 600,
              "y": 2250
            }
          }
        ],
        edges: [
          {
            "id": "edge-speech-email",
            "style": {
              "strokeWidth": 2
            },
            "source": "speech-to-text",
            "target": "email-transcription-complete",
            "selected": false
          },
          {
            "id": "edge-email-ai",
            "style": {
              "strokeWidth": 2
            },
            "source": "email-transcription-complete",
            "target": "ask-ai-analysis",
            "selected": false
          },
          {
            "id": "edge-ai-hr",
            "style": {
              "strokeWidth": 2
            },
            "source": "ask-ai-analysis",
            "target": "task-hr-review",
            "selected": false
          },
          {
            "id": "edge-hr-decision",
            "style": {
              "strokeWidth": 2
            },
            "source": "task-hr-review",
            "target": "decision-candidate-quality",
            "selected": false
          },
          {
            "id": "edge-decision-yes",
            "style": {
              "strokeWidth": 2
            },
            "source": "decision-candidate-quality",
            "target": "task-hiring-manager",
            "selected": false,
            "sourceHandle": "yes"
          },
          {
            "id": "edge-decision-no",
            "style": {
              "strokeWidth": 2
            },
            "source": "decision-candidate-quality",
            "target": "email-rejection",
            "selected": false,
            "sourceHandle": "no"
          },
          {
            "id": "edge-hiring-decision-hire",
            "style": {
              "strokeWidth": 2
            },
            "source": "task-hiring-manager",
            "target": "decision-final-hire",
            "selected": false
          },
          {
            "id": "edge-hiring-decision-additional",
            "style": {
              "strokeWidth": 2
            },
            "source": "task-hiring-manager",
            "target": "decision-additional-interview",
            "selected": false
          },
          {
            "id": "edge-decision-hire-yes",
            "style": {
              "strokeWidth": 2
            },
            "source": "decision-final-hire",
            "target": "email-offer",
            "selected": false,
            "sourceHandle": "yes"
          },
          {
            "id": "edge-decision-hire-no",
            "style": {
              "strokeWidth": 2
            },
            "source": "decision-final-hire",
            "target": "email-rejection",
            "selected": false,
            "sourceHandle": "no"
          },
          {
            "id": "edge-decision-additional-yes",
            "style": {
              "strokeWidth": 2
            },
            "source": "decision-additional-interview",
            "target": "email-additional-interview",
            "selected": false,
            "sourceHandle": "yes"
          },
          {
            "id": "edge-offer-team",
            "style": {
              "strokeWidth": 2
            },
            "source": "email-offer",
            "target": "email-team-notification",
            "selected": false
          },
          {
            "id": "edge-additional-team",
            "style": {
              "strokeWidth": 2
            },
            "source": "email-additional-interview",
            "target": "email-team-notification",
            "selected": false
          },
          {
            "id": "edge-rejection-team",
            "style": {
              "strokeWidth": 2
            },
            "source": "email-rejection",
            "target": "email-team-notification",
            "selected": false
          },
          {
            "id": "edge-team-end",
            "style": {
              "strokeWidth": 2
            },
            "source": "email-team-notification",
            "target": "end",
            "selected": false
          },
          {
            "id": "xy-edge__start-speech-to-text",
            "style": {
              "strokeWidth": 2
            },
            "source": "start",
            "target": "speech-to-text",
            "selected": false
          }
        ],
        viewport: {
          "x": -174.0259483801427,
          "y": -154.3673271663772,
          "zoom": 0.7775724295308883
        },
      }
    });

    // Create a workflow run (optional - uncomment if you want to create a running instance)
    /*
    console.log('Creating workflow run...');
    const workflowRun = await prisma.workflowRun.create({
      data: {
        workflowId: workflow.id,
        status: 'RUNNING',
        startedAt: new Date(),
        createdAt: new Date()
      }
    });

    // Create node runs for each node in the workflow
    console.log('Creating node runs...');

    // Start node (completed)
    await prisma.nodeRun.create({
      data: {
        workflowRunId: workflowRun.id,
        nodeId: 'start',
        status: 'SUCCESS',
        startedAt: new Date(Date.now() - 60000), // 1 minute ago
        finishedAt: new Date(Date.now() - 55000), // 55 seconds ago
        output: { result: 'Workflow started successfully' }
      }
    });

    // Speech-to-text node (waiting for file upload)
    await prisma.nodeRun.create({
      data: {
        workflowRunId: workflowRun.id,
        nodeId: 'speech-to-text',
        status: 'WAITING_FOR_USER',
        startedAt: new Date(Date.now() - 55000), // 55 seconds ago
        output: {
          status: 'Waiting for interview recording upload'
        }
      }
    });

    // Update workflow run status to WAITING_FOR_USER
    await prisma.workflowRun.update({
      where: { id: workflowRun.id },
      data: { status: 'WAITING_FOR_USER' }
    });
    */

    console.log('Job interview workflow seed completed successfully!');
    console.log('\n=== CREATED RESOURCES ===');
    console.log('Workflow ID:', workflow.id);
    console.log('\n=== WORKFLOW DETAILS ===');
    console.log('Workflow Name: Job Interview Analysis Workflow');
    console.log('Workflow Creator:', `${user.firstName} ${user.lastName} (${user.email})`);
    console.log('\n=== NEXT STEPS ===');
    console.log('1. Use existing users in your system');
    console.log('2. Upload interview recordings to start the workflow');
    console.log('3. The workflow will process recordings through AI analysis');

  } catch (error) {
    console.error('Error seeding database:', error);
    if (error.message.includes('User with email')) {
      console.error('\nTip: You can find existing user emails by querying your database:');
      console.error('SELECT id, "firstName", "lastName", email FROM "User" LIMIT 5;');
      console.error('Or update the userEmail variable in the script to match an existing user.');
    }
    throw error;
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
