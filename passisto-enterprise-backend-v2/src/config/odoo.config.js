const axios = require('axios');

// Configuration Odoo depuis les variables d'environnement
const odooConfig = {
  url: process.env.ODOO_URL,
  database: process.env.ODOO_DB,
  username: process.env.ODOO_USERNAME,
  password: process.env.ODOO_PASSWORD,
};

// Variables globales pour la connexion Odoo
let odooConnection = {
  uid: null,
  isConnected: false,
  connectionRetries: 3,
  retryDelay: 2000
};

// Fonction d'authentification Odoo
const authenticateOdoo = async (retryCount = 0) => {
  console.log('Odoo DB:', odooConfig.database);
  console.log('Odoo URL:', odooConfig.url);
  console.log('Odoo Username:', odooConfig.username);
  console.log('Odoo Password:', odooConfig.password);
  
  try {
    console.log(`[INFO] Attempting to authenticate with <PERSON>doo at ${odooConfig.url}`);
    
    const response = await axios.post(`${odooConfig.url}/jsonrpc`, {
      jsonrpc: '2.0',
      method: 'call',
      params: {
        service: 'common',
        method: 'authenticate',
        args: [odooConfig.database, odooConfig.username, odooConfig.password, {}]
      },
      id: Date.now()
    }, {
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      }
    });

    if (response.data.result) {
      odooConnection.uid = response.data.result;
      odooConnection.isConnected = true;
      console.log(`[INFO] Authenticated with Odoo (uid: ${odooConnection.uid})`);
      return true;
    } else {
      console.warn('[WARN] Odoo authentication failed: invalid credentials');
      return false;
    }
  } catch (error) {
    console.error('[ERROR] Odoo authentication failed:', error.response?.data || error.message);
    
    if (retryCount < odooConnection.connectionRetries) {
      console.log(`[INFO] Retrying Odoo authentication (${retryCount + 1}/${odooConnection.connectionRetries})...`);
      await new Promise(resolve => setTimeout(resolve, odooConnection.retryDelay));
      return authenticateOdoo(retryCount + 1);
    }
    
    return false;
  }
};

const connectOdoo = async () => {
  try {
    if (!odooConfig.url || !odooConfig.database || !odooConfig.username || !odooConfig.password) {
      throw new Error('Missing Odoo configuration. Please check ODOO_URL, ODOO_DB, ODOO_USERNAME, and ODOO_PASSWORD environment variables.');
    }
    
    const success = await authenticateOdoo();
    
    if (success) {
      console.log(`[INFO] Odoo connected successfully (uid: ${odooConnection.uid})`);
      return true;
    } else {
      console.error('[ERROR] Odoo authentication failed: invalid credentials');
      throw new Error('Odoo authentication failed');
    }
  } catch (error) {
    console.error('[ERROR] Odoo connection error:', error.message);
    throw error;
  }
};

const isOdooConnected = () => {
  return odooConnection.isConnected && odooConnection.uid !== null;
};

const ensureOdooConnection = async () => {
  if (!isOdooConnected()) {
    console.log('[INFO] Odoo not connected, attempting to reconnect...');
    await connectOdoo();
  }
  return odooConnection;
};

// const callOdooAPI = async (service, method, args = []) => {
//   await ensureOdooConnection();
  
//   try {
//     const response = await axios.post(`${odooConfig.url}/jsonrpc`, {
//       jsonrpc: '2.0',
//       method: 'call',
//       params: {
//         service,
//         method,
//         args: [odooConfig.database, odooConnection.uid, odooConfig.password, ...args]
//       },
//       id: Date.now()
//     }, {
//       timeout: 10000,
//       headers: {
//         'Content-Type': 'application/json',
//       }
//     });

//     return response.data.result;
//   } catch (error) {
//     console.error('[ERROR] Odoo API call failed:', error.response?.data || error.message);
//     throw new Error(`Odoo API call failed: ${error.message}`);
//   }
// };
const callOdooAPI = async (service, method, args = []) => {
  await ensureOdooConnection();

  try {
    const response = await axios.post(`${odooConfig.url}/jsonrpc`, {
      jsonrpc: '2.0',
      method: 'call',
      params: {
        service,
        method,
        args: [odooConfig.database, odooConnection.uid, odooConfig.password, ...args],
      },
      id: Date.now(),
    }, {
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.data || response.data.result === undefined) {
      console.error("[❌] Réponse Odoo invalide:", response.data);
      return null; // ou []
    }

    return response.data.result;
  } catch (error) {
    console.error('[ERROR] Odoo API call failed:', error.response?.data || error.message);
    throw new Error(`Odoo API call failed: ${error.message}`);
  }
};


module.exports = { 
  connectOdoo,
  isOdooConnected,
  ensureOdooConnection,
  callOdooAPI,
  odooConfig,
  getOdooConnection: () => odooConnection
};


