const dotenv = require("dotenv");
const { Pool } = require("pg");
const { PGVectorStore } = require("@langchain/community/vectorstores/pgvector");
const { AzureOpenAIEmbeddings } = require("@langchain/openai");

dotenv.config();

// Optimized connection pool configuration
let pool;
function getPool() {
  if (!pool) {
    pool = new Pool({
      connectionString: process.env.DATABASE_URL,
      ssl: process.env.PG_SSL === "true" ? { rejectUnauthorized: false } : undefined,
      // Optimized pool settings for vector search performance
      max: parseInt(process.env.PG_POOL_MAX || "20", 10), // Increased max connections
      min: parseInt(process.env.PG_POOL_MIN || "5", 10),  // Maintain minimum connections
      idleTimeoutMillis: parseInt(process.env.PG_IDLE_TIMEOUT || "30000", 10),
      connectionTimeoutMillis: parseInt(process.env.PG_CONNECTION_TIMEOUT || "5000", 10),
      // Enable keep-alive for better connection reuse
      keepAlive: true,
      keepAliveInitialDelayMillis: 10000,
    });

    // Add pool event listeners for monitoring
    pool.on('connect', () => {
      console.log('New database connection established');
    });

    pool.on('error', (err) => {
      console.error('Database pool error:', err);
    });
  }
  return pool;
}

// Reuse embeddings instance
let embeddings;
function getEmbeddings() {
  if (!embeddings) {
    embeddings = new AzureOpenAIEmbeddings({
      modelName: process.env.EMBEDDING_MODEL || "text-embedding-3-small",
      // Default to 1536 (embedding-3-small default). Override via EMBEDDING_DIMENSION if needed.
      dimensions: parseInt(process.env.EMBEDDING_DIMENSION || "1536", 10),
      azureOpenAIApiKey: process.env.AZURE_OPENAI_EMBEDDING_API_KEY || process.env.AZURE_OPENAI_API_KEY,
      azureOpenAIApiVersion: process.env.AZURE_OPENAI_EMBEDDING_API_VERSION || process.env.AZURE_OPENAI_API_VERSION,
      azureOpenAIApiInstanceName: process.env.AZUR_OPENAI_EMBEDDING_INSTANCE_NAME || process.env.AZURE_OPENAI_API_INSTANCE_NAME,
      azureOpenAIApiDeploymentName: process.env.AZUR_OPENAI_EMBEDDING_DEPLOYMENT_NAME || process.env.AZURE_OPENAI_API_DEPLOYMENT_NAME,
    });
  }
  return embeddings;
}



/**
 * Create and return a PGVectorStore that works with existing individual tables.
 * This avoids the need for data migration by working directly with the current schema.
 *
 * @param {string} indexName - Name of the individual table (collection)
 * @returns {Promise<PGVectorStore>} Initialized PGVectorStore instance
 */
const vectorStore = async (indexName) => {
  console.log(`Initializing vector store for collection: ${indexName}`);

  // Quote the table name to handle hyphens and special characters
  const quotedTableName = `"${indexName}"`;

  const store = await PGVectorStore.initialize(getEmbeddings(), {
    pool: getPool(),
    // Use the quoted collection name as the table name directly
    tableName: quotedTableName,
    // Use public schema by default
    schema: process.env.PGVECTOR_SCHEMA || "public",
    // Cosine distance matches our similarity usage
    distanceStrategy: "cosine",
    // Configure column mapping for existing schema
    columns: {
      idColumnName: "langchain_id",
      vectorColumnName: "embedding",
      contentColumnName: "content",
      metadataColumnName: "langchain_metadata",
    },
  });

  return store;
};

/**
 * Ensure HNSW index exists for a collection table for optimal search performance
 * @param {string} indexName - Name of the collection/table
 * @returns {Promise<void>}
 */
const ensureHnswIndex = async (indexName) => {
  const pool = getPool();
  const quotedTableName = `"${indexName}"`;
  const indexNameSafe = indexName.replace(/[^a-zA-Z0-9_]/g, '_');
  const hnswIndexName = `idx_${indexNameSafe}_embedding_hnsw`;

  try {
    const client = await pool.connect();
    try {
      // Check if HNSW index already exists
      const indexCheckQuery = `
        SELECT indexname
        FROM pg_indexes
        WHERE tablename = $1 AND indexname = $2
      `;
      const indexExists = await client.query(indexCheckQuery, [indexName, hnswIndexName]);

      if (indexExists.rows.length === 0) {
        console.log(`Creating HNSW index for collection: ${indexName}`);
        const createIndexQuery = `
          CREATE INDEX CONCURRENTLY "${hnswIndexName}"
          ON ${quotedTableName}
          USING hnsw (embedding vector_cosine_ops)
          WITH (m = 16, ef_construction = 64)
        `;
        await client.query(createIndexQuery);
        console.log(`HNSW index created successfully for ${indexName}`);
      } else {
        console.log(`HNSW index already exists for ${indexName}`);
      }
    } finally {
      client.release();
    }
  } catch (error) {
    console.error(`Error ensuring HNSW index for ${indexName}:`, error.message);
    // Don't throw error as this is an optimization, not a requirement
  }
};

module.exports = {
  vectorStore,
  ensureHnswIndex
};