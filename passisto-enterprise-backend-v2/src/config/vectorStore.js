const dotenv = require("dotenv");
const { vectorStore: getVectorStore, ensureHnswIndex } = require('./vectorStorePgVector');

dotenv.config();

const vectorStore = async (indexName) => {
  // Use the official LangChain PGVectorStore implementation
  const store = await getVectorStore(indexName);

  // Ensure HNSW index exists for optimal performance (non-blocking)
  ensureHnswIndex(indexName).catch(err => {
    console.warn(`Failed to ensure HNSW index for ${indexName}:`, err.message);
  });

  return store;
};

module.exports = vectorStore;
