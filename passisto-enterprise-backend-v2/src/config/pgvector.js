const {
  PGVectorStore,
} = require("@langchain/community/vectorstores/pgvector");
const { AzureOpenAIEmbeddings } = require("@langchain/openai");
const dotenv = require("dotenv");
const { Pool } = require("pg");

dotenv.config();

/**
 * PGVectorStore service using standard LangChain implementation
 * This follows the official LangChain documentation for pgvector
 */
class PgVectorService {
  constructor() {
    this.embeddings = null;
    this.pool = null;
    this.initializationPromise = null;
  }

  /**
   * Initialize the service with embeddings and database pool
   */
  async initialize() {
    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    this.initializationPromise = this._doInitialize();
    return this.initializationPromise;
  }

  async _doInitialize() {
    if (this.embeddings && this.pool) return;

    // Initialize Azure OpenAI embeddings
    this.embeddings = new AzureOpenAIEmbeddings({
      modelName: process.env.EMBEDDING_MODEL || "text-embedding-3-small",
      dimensions: parseInt(process.env.EMBEDDING_DIMENSION) || 1024,
      azureOpenAIApiKey: process.env.AZURE_OPENAI_EMBEDDING_API_KEY,
      azureOpenAIApiVersion: process.env.AZURE_OPENAI_EMBEDDING_API_VERSION,
      azureOpenAIApiInstanceName: process.env.AZUR_OPENAI_EMBEDDING_INSTANCE_NAME,
      azureOpenAIApiDeploymentName: process.env.AZUR_OPENAI_EMBEDDING_DEPLOYMENT_NAME,
    });

    // Initialize database connection pool
    this.pool = new Pool({
      host: process.env.DB_HOST,
      port: parseInt(process.env.DB_PORT) || 5432,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
    });

    console.log("PGVectorService initialized successfully");
  }

  /**
   * Create a PGVectorStore instance for a specific collection
   * @param {string} collectionName - Collection name (replaces indexName)
   * @param {string} tableName - Table name (default: "langchain_pg_embedding")
   * @returns {Promise<PGVectorStore>} PGVectorStore instance
   */
  async createVectorStore(collectionName, tableName = "langchain_pg_embedding") {
    await this.initialize();

    const config = {
      pool: this.pool,
      tableName: tableName,
      collectionName: collectionName,
      collectionTableName: "langchain_pg_collection",
      distanceStrategy: "cosine",
    };

    // Initialize the vector store (creates tables if they don't exist)
    const vectorStore = await PGVectorStore.initialize(this.embeddings, config);
    
    console.log(`Created PGVectorStore for collection: ${collectionName}`);
    return vectorStore;
  }

  /**
   * Create or get a PGVectorStore instance without initialization (for reuse with existing pool)
   * @param {string} collectionName - Collection name
   * @param {string} tableName - Table name (default: "langchain_pg_embedding")
   * @returns {Promise<PGVectorStore>} PGVectorStore instance
   */
  async getVectorStore(collectionName, tableName = "langchain_pg_embedding") {
    await this.initialize();

    const config = {
      pool: this.pool,
      tableName: tableName,
      collectionName: collectionName,
      collectionTableName: "langchain_pg_collection",
      distanceStrategy: "cosine",
    };

    return new PGVectorStore(this.embeddings, config);
  }

  /**
   * Add documents to a vector store collection
   * @param {string} collectionName - Collection name
   * @param {Array} documents - Array of LangChain Document objects with pageContent and metadata
   * @param {Object} options - Options including ids array
   * @returns {Promise<Array>} Array of document IDs
   */
  async addDocuments(collectionName, documents, options = {}) {
    const vectorStore = await this.createVectorStore(collectionName);
    return await vectorStore.addDocuments(documents, options);
  }

  /**
   * Search for similar documents
   * @param {string} collectionName - Collection name
   * @param {string} query - Search query
   * @param {number} k - Number of results (default: 4)
   * @param {Object} filter - Optional metadata filter
   * @returns {Promise<Array>} Array of similar documents
   */
  async similaritySearch(collectionName, query, k = 4, filter = {}) {
    const vectorStore = await this.getVectorStore(collectionName);
    return await vectorStore.similaritySearch(query, k, filter);
  }

  /**
   * Search for similar documents with scores
   * @param {string} collectionName - Collection name
   * @param {string} query - Search query
   * @param {number} k - Number of results (default: 4)
   * @param {Object} filter - Optional metadata filter
   * @returns {Promise<Array>} Array of [document, score] tuples
   */
  async similaritySearchWithScore(collectionName, query, k = 4, filter = {}) {
    const vectorStore = await this.getVectorStore(collectionName);
    return await vectorStore.similaritySearchWithScore(query, k, filter);
  }

  /**
   * Delete documents from a collection
   * @param {string} collectionName - Collection name
   * @param {Object} options - Delete options (e.g., { ids: [...] })
   * @returns {Promise<void>}
   */
  async deleteDocuments(collectionName, options = {}) {
    const vectorStore = await this.getVectorStore(collectionName);
    return await vectorStore.delete(options);
  }

  /**
   * Create a retriever for a collection
   * @param {string} collectionName - Collection name
   * @param {Object} config - Retriever configuration
   * @returns {Promise<Object>} LangChain retriever
   */
  async createRetriever(collectionName, config = {}) {
    const vectorStore = await this.getVectorStore(collectionName);
    return vectorStore.asRetriever(config);
  }

  /**
   * Create HNSW index for better performance
   * @param {string} collectionName - Collection name
   * @param {Object} options - HNSW options
   * @returns {Promise<void>}
   */
  async createHnswIndex(collectionName, options = {}) {
    const defaultOptions = {
      dimensions: parseInt(process.env.EMBEDDING_DIMENSION) || 1024,
      efConstruction: 64,
      m: 16,
    };
    
    const vectorStore = await this.getVectorStore(collectionName);
    return await vectorStore.createHnswIndex({ ...defaultOptions, ...options });
  }

  /**
   * Get collection information
   * @param {string} collectionName - Collection name
   * @returns {Promise<Object>} Collection info
   */
  async getCollectionInfo(collectionName) {
    await this.initialize();
    
    // Query the collection table to get collection info
    const query = `
      SELECT name, cmetadata, uuid 
      FROM langchain_pg_collection 
      WHERE name = $1
    `;
    
    const result = await this.pool.query(query, [collectionName]);
    
    if (result.rows.length === 0) {
      return null;
    }

    const collection = result.rows[0];
    
    // Get document count
    const countQuery = `
      SELECT COUNT(*) as count 
      FROM langchain_pg_embedding 
      WHERE collection_id = $1
    `;
    
    const countResult = await this.pool.query(countQuery, [collection.uuid]);
    
    return {
      name: collection.name,
      metadata: collection.cmetadata,
      documentCount: parseInt(countResult.rows[0].count),
      id: collection.uuid
    };
  }

  /**
   * List all collections
   * @returns {Promise<Array>} Array of collection names
   */
  async listCollections() {
    await this.initialize();
    
    const query = `SELECT name FROM langchain_pg_collection ORDER BY name`;
    const result = await this.pool.query(query);
    
    return result.rows.map(row => row.name);
  }

  /**
   * Delete a collection and all its documents
   * @param {string} collectionName - Collection name
   * @returns {Promise<boolean>} Success status
   */
  async deleteCollection(collectionName) {
    const vectorStore = await this.getVectorStore(collectionName);
    
    try {
      // Delete all documents in the collection
      await vectorStore.delete({ filter: {} });
      console.log(`Deleted collection: ${collectionName}`);
      return true;
    } catch (error) {
      console.error(`Error deleting collection ${collectionName}:`, error);
      return false;
    }
  }

  /**
   * Close the database connection pool
   * @returns {Promise<void>}
   */
  async close() {
    if (this.pool) {
      await this.pool.end();
      this.pool = null;
      console.log("Database connection pool closed");
    }
  }
}

// Export singleton instance
const pgVectorService = new PgVectorService();

module.exports = pgVectorService;