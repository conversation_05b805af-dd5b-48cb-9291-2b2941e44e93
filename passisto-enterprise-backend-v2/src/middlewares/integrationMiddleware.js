const { JiraIntegrationSchema } = require("./schema/jira.schema");
const { FtpIntegrationSchema } = require("./schema/ftp.schema");
const { WebIntegrationSchema } = require("./schema/web.schema");

const prisma = require("@/config/db");
const { getIndexSize } = require('@/utils/helpers')
const pgVectorService = require('@/config/pgvector');


const SCHEMAS = {
  jira: JiraIntegrationSchema,
  ftp: FtpIntegrationSchema,
  web: WebIntegrationSchema,
};

async function validateIntegration(req, res, next){
  const { providerType } = req.body;

  if (!providerType || !(providerType in SCHEMAS)) {
    return res.status(400).json({ error: "Invalid or missing providerType" });
  }

  const schema = SCHEMAS[providerType];

  try {
    req.body = schema.parse(req.body);
    next();
  } catch (err) {
    return res.status(400).json({ error: err.errors });
  }
};


async function verifyStorageQuota(req, res, next) {
  try {
    const { companyId } = req.user;

    if (!companyId) {
      return res.status(400).json({ error: "Company ID is missing" });
    }

    // Step 1: Fetch all integrations belonging to the company
    const integrations = await prisma.integration.findMany({
      where: { companyId }
    });

    let totalSizeInGb = 0;
    
    if (integrations.length) {
      // Step 2: Calculate size based on collection document counts
      for (const integration of integrations) {
        try {
          const collectionInfo = await pgVectorService.getCollectionInfo(integration.collectionName);
          const documentCount = collectionInfo.documentCount || 0;
          // Approximate size calculation: each document ~1KB + vector (1024 * 4 bytes)
          const sizeInBytes = documentCount * (1024 + 1024 * 4);
          totalSizeInGb += sizeInBytes / (1024 * 1024 * 1024);
        } catch (error) {
          console.warn(`Could not get collection info for ${integration.collectionName}:`, error.message);
          // Continue with other integrations
        }
      }
    }

    // Step 3: Fetch current usage from CompanyUsage
    const currentUsage = await prisma.companyUsage.findUnique({
      where: { companyId_key: { companyId, key: "storageGbUsed" } },
    });

    // Step 4: Update the CompanyUsage table
    if (!currentUsage || currentUsage.value !== totalSizeInGb)
    {
        await prisma.companyUsage.upsert({
        where: { companyId_key: { companyId, key: "storageGbUsed" } },
        update: { value: totalSizeInGb },
        create: { companyId, key: "storageGbUsed", value: totalSizeInGb },
      });
    }

    // Step 5: Fetch the storage limit from the subscription plan
    const subscriptionFeature = await prisma.subscriptionFeature.findFirst({
      where: {
        subscription: { companies: { some: { id: companyId } } },
        key: "storageLimitGb",
      },
    });

    if (!subscriptionFeature) {
      return res.status(400).json({ error: "Storage limit not defined for the subscription" });
    }

    const storageLimitGb = subscriptionFeature.value;
    totalSizeInGb = currentUsage.value
    // Step 6: Verify if the usage exceeds the limit
    if (totalSizeInGb >= storageLimitGb) {
      return res.status(402).json({
        error: "Storage quota exceeded. Cannot create a new integration.",
        limit: storageLimitGb
      });
    }

    // Proceed to the next middleware
    next();
  } catch (error) {
    console.error("Error in verifyStorageQuota middleware:", error);
    res.status(500).json({ error: "Internal server error" });
  }
}

module.exports = { validateIntegration, verifyStorageQuota };