const prisma = require("../config/db");

module.exports = function checkFeatureQuota(featureKey, usageKey) {
  return async (req, res, next) => {
    const { companyId } = req.user;
    try {
      const company = await prisma.company.findUnique({
        where: { id: companyId },
        include: {
          subscription: { include: { features: true } },
          usage: true,
        },
      });

      if (!company || !company.subscription) {
        return res.status(403).json({
          success: false,
          message: "No active subscription found",
        });
      }

      const feature = company.subscription.features.find(f => f.key === featureKey);
      if (!feature) {
        return res.status(400).json({
          success: false,
          message: "Feature not found in subscription",
        });
      }

      const limit = parseInt(feature.value);
      const used = company.usage.find(u => u.key === usageKey)?.value || 0;

      if (used >= limit) {
        return res.status(429).json({
          success: false,
          message: "Usage limit exceeded for this feature",
          limit,
        });
      }

      next();
    } catch (error) {
      return res.status(500).json({
        success: false,
        message: "Failed to check usage limit",
      });
    }
  };
};