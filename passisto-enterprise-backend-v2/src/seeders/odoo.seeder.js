const prisma = require("../config/db");
const bcrypt = require("bcrypt");
const { v4: uuidv4 } = require("uuid");
const { clerkClient } = require("@clerk/express");
const { faker } = require("@faker-js/faker");
const { seedFakeIntegrations } = require("./integration.seeder");

// Sample company data - multiple companies
const companyTemplates = [
  {
    name: "Passisto",
    description: "Enterprise AI Solutions",
  },
  {
    name: "TechCorp Solutions",
    description: "Digital Transformation & Cloud Services",
  },
];

// Define roles
const roles = ["ADMIN", "MANAGER", "MEMBER", "GUEST", "PUBLIC"];

// Define permission categories
const permissionCategories = [
  {
    name: "User & Group Management",
    permissions: [
      "CAN_MANAGE_USERS",
      "CAN_CREATE_USER",
      "CAN_UPDATE_USER",
      "CAN_DELETE_USER",
      "CAN_VIEW_USER",
      "CAN_TOGGLE_USER_STATUS",
      "CAN_MANAGE_GROUPS",
      "CAN_CREATE_GROUP",
      "CAN_UPDATE_GROUP",
      "CAN_DELETE_GROUP",
      "CAN_VIEW_GROUP",
      "CAN_ASSIGN_USER_TO_GROUP",
      "CAN_REMOVE_USER_FROM_GROUP",
    ],
  },
  {
    name: "Form Builder",
    permissions: [
      "CAN_CREATE_FORM",
      "CAN_UPDATE_FORM",
      "CAN_DELETE_FORM",
      "CAN_VIEW_FORM",
      "CAN_PUBLISH_FORM",
      "CAN_EXPORT_FORM_DATA",
      "CAN_VIEW_RESPONSES",
    ],
  },
  {
    name: "Data Provider Management",
    permissions: [
      // ALL
      "CAN_MANAGE_INTEGRATIONS",

      // General
      "CAN_VIEW_INTEGRATION",
      "CAN_ASSIGN_INTEGRATION_TO_GROUP",
      "CAN_REMOVE_INTEGRATION_FROM_GROUP",

      // FTP
      "CAN_CREATE_FTP",
      "CAN_UPDATE_FTP",
      "CAN_DELETE_FTP",

      // Jira
      "CAN_CREATE_JIRA",
      "CAN_UPDATE_JIRA",
      "CAN_DELETE_JIRA",

      // Web
      "CAN_CREATE_WEB",
      "CAN_UPDATE_WEB",
      "CAN_DELETE_WEB",
    ],
  },
  {
    name: "Email Builder",
    permissions: [
      "CAN_ENHANCE_EMAIL_BUILDER_DESCRIPTION",
      "CAN_GENERATE_EMAIL_BUILDER_TEMPLATE",
      "CAN_VIEW_EMAIL_BUILDER_TEMPLATE",
      "CAN_EDIT_EMAIL_BUILDER",
      "CAN_DELETE_EMAIL_BUILDER",
      "CAN_SEND_EMAIL_BUILDER",
      "CAN_MANAGE_EMAIL_BUILDER",
    ],
  },
  {
    name: "AI Interview Agent",
    permissions: [
      "CAN_CREATE_INTERVIEW",
      "CAN_UPDATE_INTERVIEW",
      "CAN_DELETE_INTERVIEW",
      "CAN_VIEW_INTERVIEW",
      "CAN_SEND_INTERVIEW_TO_CANDIDATE",
      // "CAN_VIEW_INTERVIEW_RESULTS",
      // "CAN_EXPORT_INTERVIEW_DATA",
      // "CAN_CREATE_FEEDBACK",
      "CAN_VIEW_FEEDBACK",
      "CAN_MANAGE_INTERVIEW_AGENT",
    ],
  },
  {
    name: "Billing & Subscription",
    permissions: [
      "CAN_VIEW_PLANS",
      "CAN_VIEW_SUBSCRIPTION_STATUS",
      "CAN_VIEW_SUBSCRIPTION_DETAILS",
      "CAN_CREATE_CHECKOUT_SESSION",
      "CAN_CREATE_PORTAL_SESSION",
      // "CAN_MANAGE_BILLING",
    ],
  },
  {
    name: "Chatbot & Search",
    permissions: ["CAN_ASK_CHATBOT", "CAN_SEARCH"],
  },
];

// Define standard groups that will be created for each company
const standardGroups = [
  {
    name: "Company",
    description: "Default company-wide group for all employees",
  },
  {
    name: "Engineering",
    description: "Engineering and Development team",
  },
  {
    name: "Marketing",
    description: "Marketing and Communications team",
  },
  {
    name: "Sales",
    description: "Sales and Business Development team",
  },
  {
    name: "Finance",
    description: "Finance and Accounting department",
  },
];

// Feature definitions for subscriptions
const FEATURE_DEFINITIONS = {
  maxEmailAgents: {
    id: "email-builder",
    name: "Email Builder",
    unit: "emails/month",
    description: "AI-powered email templates",
  },
  maxFormAgents: {
    id: "form-builder",
    name: "Form Builder",
    unit: "forms/month",
    description: "AI-powered form creation",
  },
  maxAiInterviewHours: {
    id: "ai-interviewer",
    name: "AI Interviewer",
    unit: "minutes/month",
    description: "Smart candidate screening",
    convert: (v) => Math.round(parseFloat(v) * 60), // convert hours to minutes
  },
  maxSearchQueries: {
    id: "search",
    name: "Search Queries",
    unit: "queries/month",
    description: "AI-powered search",
  },
  maxChatMessages: {
    id: "chat",
    name: "Chat Messages",
    unit: "messages/month",
    description: "AI chat messages",
  },
  maxUsers: {
    id: "users",
    name: "Users",
    unit: "users",
    description: "Maximum users allowed",
  },
  storageLimitGb: {
    id: "storage",
    name: "Storage",
    unit: "GB",
    description: "Total storage limit",
  },
  fileStorageLimitGb: {
    id: "file-storage",
    name: "File Storage",
    unit: "GB/file",
    description: "Max file size per upload",
  },
};

// Subscription plans with their features
const subscriptionPlans = [
  {
    name: "Starter",
    stripeProductId: "prod_starter_123",
    features: {
      maxEmailAgents: 50,
      maxFormAgents: 25,
      maxAiInterviewHours: 10,
      maxSearchQueries: 1000,
      maxChatMessages: 500,
      maxUsers: 5,
      storageLimitGb: 10,
      fileStorageLimitGb: 0.1,
    },
  },
  {
    name: "Professional",
    stripeProductId: "prod_professional_456",
    features: {
      maxEmailAgents: 200,
      maxFormAgents: 100,
      maxAiInterviewHours: 50,
      maxSearchQueries: 5000,
      maxChatMessages: 2000,
      maxUsers: 25,
      storageLimitGb: 100,
      fileStorageLimitGb: 1,
    },
  },
  {
    name: "Enterprise",
    stripeProductId: "prod_enterprise_789",
    features: {
      maxEmailAgents: 1000,
      maxFormAgents: 500,
      maxAiInterviewHours: 200,
      maxSearchQueries: 25000,
      maxChatMessages: 10000,
      maxUsers: 100,
      storageLimitGb: 500,
      fileStorageLimitGb: 5,
    },
  },
];

// Generate users for a specific company
const generateUsersForCompany = (companyName) => {
  const users = [];

  if (companyName === "Passisto") {
    // Admin users for Passisto
    users.push(
      {
        firstName: "Yassine",
        lastName: "AFRACHE",
        email: "<EMAIL>",
        password: "Passisto@123",
        roleNames: ["ADMIN"], // Changed to array of role names
      }
    );
  } else {
    // Generate users for other companies
    // Admin user (1)
    const adminFirstName = faker.person.firstName();
    const adminLastName = faker.person.lastName();
    users.push({
      firstName: adminFirstName,
      lastName: adminLastName,
      email: faker.internet
        .email({ firstName: adminFirstName, lastName: adminLastName, provider: "techcorp.com" })
        .toLowerCase(),
      password: "TechCorp@123",
      roleNames: ["ADMIN"], // Changed to array
    });

    // Manager (1)
    const managerFirstName = faker.person.firstName();
    const managerLastName = faker.person.lastName();
    users.push({
      firstName: managerFirstName,
      lastName: managerLastName,
      email: faker.internet
        .email({ firstName: managerFirstName, lastName: managerLastName, provider: "techcorp.com" })
        .toLowerCase(),
      password: "TechCorp@123",
      roleNames: ["MANAGER"], // Changed to array
    });
  }

  // Add some random users for all companies
  for (let i = 0; i < 5; i++) {
    const firstName = faker.person.firstName();
    const lastName = faker.person.lastName();
    const domain = companyName === "Passisto" ? "passisto.com" : "techcorp.com";
    const availableRoles = ["MEMBER", "GUEST"];
    const randomRole = availableRoles[Math.floor(Math.random() * availableRoles.length)];

    // Sometimes assign multiple roles for testing
    const roleNames = Math.random() > 0.7 ? [randomRole, "PUBLIC"] : [randomRole];

    users.push({
      firstName,
      lastName,
      email: faker.internet
        .email({ firstName, lastName, provider: domain })
        .toLowerCase(),
      password: faker.internet.password({
        length: 12,
        memorable: true,
        pattern: /[A-Za-z0-9!@#$%^&*]/,
      }),
      roleNames, // Now using array of role names
    });
  }

  return users;
};

// Helper function to shuffle an array
const shuffleArray = (array) => {
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [array[i], array[j]] = [array[j], array[i]];
  }
  return array;
};

// Helper function to get random items from an array
const getRandomItems = (array, min, max) => {
  const count = Math.floor(Math.random() * (max - min + 1)) + min;
  return shuffleArray([...array]).slice(0, count);
};

// Convert feature key to usage key
const getUsageKey = (featureKey) => {
  const usageKeyMap = {
    maxEmailAgents: "emailAgentsUsed",
    maxFormAgents: "formAgentsUsed",
    maxAiInterviewHours: "aiInterviewHoursUsed",
    maxSearchQueries: "searchQueriesUsed",
    maxChatMessages: "chatMessagesUsed",
    maxUsers: "usersUsed",
    storageLimitGb: "storageGbUsed",
    fileStorageLimitGb: "fileStorageGbUsed",
  };
  return usageKeyMap[featureKey] || `${featureKey}Used`;
};

async function seedSubscriptions() {
  console.log("Seeding subscriptions...");
  try {
    const createdSubscriptions = [];

    for (const planTemplate of subscriptionPlans) {
      // Check if subscription already exists
      const existingSubscription = await prisma.subscription.findUnique({
        where: { stripeProductId: planTemplate.stripeProductId },
      });

      if (existingSubscription) {
        console.log(`Subscription ${planTemplate.name} already exists`);
        createdSubscriptions.push(existingSubscription);
        continue;
      }

      // Create subscription
      const subscription = await prisma.subscription.create({
        data: {
          name: planTemplate.name,
          stripeProductId: planTemplate.stripeProductId,
        },
      });

      console.log(`Created subscription: ${subscription.name} (${subscription.id})`);

      // Create subscription features
      for (const [featureKey, featureValue] of Object.entries(planTemplate.features)) {
        await prisma.subscriptionFeature.create({
          data: {
            subscriptionId: subscription.id,
            key: featureKey,
            value: featureValue,
          },
        });
        console.log(`  Added feature ${featureKey}: ${featureValue}`);
      }

      createdSubscriptions.push(subscription);
    }

    return createdSubscriptions;
  } catch (error) {
    console.error("Error seeding subscriptions:", error);
    throw error;
  }
}

async function seedRolesAndPermissions() {
  console.log("Seeding roles and permissions...");
  try {
    // Create roles
    const createdRoles = [];
    for (const roleName of roles) {
      const existingRole = await prisma.role.findUnique({
        where: { name: roleName },
      });
      
      if (existingRole) {
        console.log(`Role ${roleName} already exists`);
        createdRoles.push(existingRole);
        continue;
      }

      const role = await prisma.role.create({
        data: { name: roleName },
      });
      console.log(`Created role: ${role.name} (${role.id})`);
      createdRoles.push(role);
    }

    // Create permissions
    const createdPermissions = [];
    for (const category of permissionCategories) {
      for (const permissionName of category.permissions) {
        // Check if permission already exists
        let permission = await prisma.permission.findUnique({
          where: { action: permissionName },
        });

        // If not, create it
        if (!permission) {
          permission = await prisma.permission.create({
            data: {
              action: permissionName,
              category: category.name,
            },
          });
          console.log(
            `Created permission: ${permission.action} (${permission.id})`
          );
        } else {
          console.log(`Permission already exists: ${permission.action}`);
        }

        createdPermissions.push(permission);
      }
    }

    // Enhanced role permissions with different scopes
    const rolePermissionMappings = {
      ADMIN: {
        permissions: createdPermissions, // All permissions
        scopeType: "GLOBAL",
        scopeId: "global"
      },
      MANAGER: {
        permissions: createdPermissions.filter(p => 
          !p.action.includes("DELETE") && !p.action.includes("MANAGE_BILLING")
        ), // Most permissions except destructive ones
        scopeType: "GLOBAL",
        scopeId: null // Will be set per company
      },
      MEMBER: {
        permissions: createdPermissions.filter(p => 
          p.action.includes("VIEW") || 
          p.action.includes("CREATE_FORM") || 
          p.action.includes("CREATE_INTERVIEW") ||
          p.action.includes("ASK_CHATBOT") ||
          p.action.includes("SEARCH")
        ), // View and basic create permissions
        scopeType: "TEAM",
        scopeId: null // Will be set per group
      },
      GUEST: {
        permissions: createdPermissions.filter(p => 
          p.action.includes("VIEW") && 
          !p.action.includes("MANAGE")
        ), // Only view permissions
        scopeType: "PROJECT",
        scopeId: null
      },
      PUBLIC: {
        permissions: createdPermissions.filter(p => 
          p.action === "CAN_VIEW_PLANS" ||
          p.action === "CAN_ASK_CHATBOT" ||
          p.action === "CAN_SEARCH"
        ), // Very limited permissions
        scopeType: "GLOBAL",
        scopeId: "global"
      }
    };

    // Clear existing role permissions to avoid duplicates
    await prisma.rolePermission.deleteMany({});

    // Assign permissions to roles with different scopes
    for (const [roleName, config] of Object.entries(rolePermissionMappings)) {
      const role = createdRoles.find(r => r.name === roleName);
      if (!role) continue;

      for (const permission of config.permissions) {
        await prisma.rolePermission.create({
          data: {
            roleId: role.id,
            permissionId: permission.id,
            scopeType: config.scopeType,
            scopeId: config.scopeId || "default",
          },
        });
      }
      console.log(`Assigned ${config.permissions.length} permissions to ${roleName} role with ${config.scopeType} scope`);
    }

    return { roles: createdRoles, permissions: createdPermissions };
  } catch (error) {
    console.error("Error seeding roles and permissions:", error);
    throw error;
  }
}

async function seedCompanies(subscriptions) {
  console.log("Seeding companies...");
  try {
    const createdCompanies = [];

    for (let i = 0; i < companyTemplates.length; i++) {
      const companyTemplate = companyTemplates[i];
      
      // Check if company already exists
      const existingCompany = await prisma.company.findUnique({
        where: { name: companyTemplate.name },
      });

      if (existingCompany) {
        console.log(`Company ${companyTemplate.name} already exists`);
        createdCompanies.push(existingCompany);
        continue;
      }

      // Assign a subscription plan (cycle through available plans)
      const subscriptionPlan = subscriptions[i % subscriptions.length];

      // Create company with subscription
      const company = await prisma.company.create({
        data: {
          name: companyTemplate.name,
          description: companyTemplate.description,
          stripeCustomerId: `cus_${faker.string.alphanumeric(14)}`,
          stripeSubscriptionId: `sub_${faker.string.alphanumeric(14)}`,
          subscriptionStatus: "active",
          subscriptionId: subscriptionPlan.id,
        },
      });

      console.log(`Created company: ${company.name} (${company.id}) with ${subscriptionPlan.name} plan`);

      // Create company usage records
      const subscriptionFeatures = await prisma.subscriptionFeature.findMany({
        where: { subscriptionId: subscriptionPlan.id },
      });

      for (const feature of subscriptionFeatures) {
        const usageKey = getUsageKey(feature.key);
        // Generate random usage (between 0 and 80% of the limit)
        const maxUsage = feature.value * 0.8;
        const currentUsage = Math.floor(Math.random() * maxUsage);

        await prisma.companyUsage.create({
          data: {
            companyId: company.id,
            key: usageKey,
            value: currentUsage,
          },
        });

        console.log(`  Added usage ${usageKey}: ${currentUsage}/${feature.value}`);
      }

      createdCompanies.push(company);
    }

    return createdCompanies;
  } catch (error) {
    console.error("Error seeding companies:", error);
    throw error;
  }
}

async function seedUsers(companies, createdRoles) {
  console.log("Seeding users...");
  try {
    const allCreatedUsers = [];

    for (const company of companies) {
      console.log(`Seeding users for company: ${company.name}`);
      const createdUsers = [];
      const users = generateUsersForCompany(company.name);

      for (const userData of users) {
        try {
          console.log(`Processing user: ${userData.email}`);

          // Check if user already exists
          const existingUser = await prisma.user.findUnique({
            where: { email: userData.email },
          });

          if (existingUser) {
            console.log(`User ${userData.email} already exists`);
            createdUsers.push(existingUser);
            continue;
          }

          // Step 1: Create invitation first
          console.log(`Creating invitation for ${userData.email}`);
          const existingInvitation = await prisma.invitation.findUnique({
            where: { email: userData.email },
          });

          let invitation;
          if (!existingInvitation) {
            invitation = await prisma.invitation.create({
              data: {
                email: userData.email,
                companyId: company.id,
              },
            });
            console.log(`Created invitation with ID: ${invitation.id}`);
          } else {
            invitation = existingInvitation;
            console.log(`Invitation already exists for ${userData.email}`);
          }

          // Step 2: Create user in Clerk
          console.log(`Creating user in Clerk: ${userData.email}`);
          let clerkUser;
          try {
            // Create new user in Clerk
            clerkUser = await clerkClient.users.createUser({
              emailAddress: [userData.email],
              password: userData.password,
              firstName: userData.firstName,
              lastName: userData.lastName,
            });
            console.log(`Created new user in Clerk with ID: ${clerkUser.id}`);
          } catch (clerkError) {
            console.error(`Error with Clerk API: ${clerkError.message}`);
            // If we can't create in Clerk, generate a mock ID for development
            clerkUser = { id: `clerk_${uuidv4()}` };
            console.log(`Using mock Clerk ID: ${clerkUser.id}`);
          }

          // Step 3: Hash password for our database
          const hashedPassword = await bcrypt.hash(userData.password, 10);

          // Step 4: Find roles - FIXED: Now handles multiple roles
          const userRoles = [];
          for (const roleName of userData.roleNames) {
            const role = createdRoles.find(r => r.name === roleName);
            if (role) {
              userRoles.push(role);
            } else {
              console.warn(`Role ${roleName} not found`);
            }
          }

          if (userRoles.length === 0) {
            console.warn(`No valid roles found for user ${userData.email}, skipping`);
            continue;
          }

          // Step 5: Create user in our database with proper many-to-many relationship
          const user = await prisma.user.create({
            data: {
              clerkId: clerkUser.id,
              email: userData.email,
              firstName: userData.firstName,
              lastName: userData.lastName,
              password: hashedPassword,
              companyId: company.id,
              roles: {
                connect: userRoles.map(role => ({ id: role.id })), // FIXED: Proper many-to-many connection
              },
            },
          });

          console.log(
            `Created user: ${user.email} (${user.id}) with roles [${userRoles.map(r => r.name).join(', ')}] for company ${company.name}`
          );

          // Step 6: Update Clerk user metadata
          try {
            await clerkClient.users.updateUserMetadata(clerkUser.id, {
              publicMetadata: {
                companyId: company.id,
                userId: user.id,
              },
            });
            console.log(`Updated Clerk metadata for user ${user.email}`);
          } catch (metadataError) {
            console.error(
              `Error updating Clerk metadata: ${metadataError.message}`
            );
          }

          createdUsers.push(user);
        } catch (userError) {
          console.error(`Error processing user ${userData.email}:`, userError);
        }
      }

      console.log(`Created ${createdUsers.length} users for ${company.name}`);
      allCreatedUsers.push({ company, users: createdUsers });
    }

    return allCreatedUsers;
  } catch (error) {
    console.error("Error seeding users:", error);
    throw error;
  }
}


async function seedUserOverrides(companyUsers, permissions) {
  console.log("Seeding user overrides...");
  try {
    for (const { company, users } of companyUsers) {
      console.log(`Creating user overrides for company: ${company.name}`);
      
      // Create overrides for some users (not all)
      const usersToOverride = users.filter(() => Math.random() > 0.6); // ~40% of users get overrides
      
      for (const user of usersToOverride) {
        // Check if user already has override
        const existingOverride = await prisma.userOverride.findUnique({
          where: { userId: user.id },
        });

        if (existingOverride) {
          console.log(`User ${user.email} already has override`);
          continue;
        }

        // Create user override
        const userOverride = await prisma.userOverride.create({
          data: {
            userId: user.id,
          },
        });

        console.log(`Created override for user ${user.email}`);

        // Add some EXTRA permissions (permissions they gain)
        const extraPermissions = getRandomItems(permissions, 1, 3);
        const scopeTypes = ["GLOBAL", "SELF", "TEAM", "PROJECT"];
        
        for (const permission of extraPermissions) {
          const randomScopeType = scopeTypes[Math.floor(Math.random() * scopeTypes.length)];
          let scopeId;
          
          switch (randomScopeType) {
            case "SELF":
              scopeId = "self";
              break;
            case "GLOBAL":
              scopeId = company.id;
              break;
            case "TEAM":
              scopeId = `team_${faker.string.alphanumeric(8)}`;
              break;
            case "PROJECT":
              scopeId = `project${faker.string.alphanumeric(8)}`;
              break;
            default:
              scopeId = "GLOBAL";
          }

          await prisma.permissionOverride.create({
            data: {
              permissionId: permission.id,
              scopeType: randomScopeType,
              scopeId: scopeId,
              extraOverrideId: userOverride.id, // This makes it an extra permission
            },
          });

          console.log(`  Added EXTRA permission ${permission.action} with ${randomScopeType} scope (${scopeId})`);
        }

        // Add some REVOKED permissions (permissions they lose) - but only sometimes
        if (Math.random() > 0.5) { // 50% chance to have revoked permissions
          const revokedPermissions = getRandomItems(permissions, 1, 2);
          
          for (const permission of revokedPermissions) {
            const randomScopeType = scopeTypes[Math.floor(Math.random() * scopeTypes.length)];
            let scopeId;
            
            switch (randomScopeType) {
              case "SELF":
                scopeId = "self";
                break;
              case "GLOBAL":
                scopeId = company.id;
                break;
              case "TEAM":
                scopeId = `team_${faker.string.alphanumeric(8)}`;
                break;
              case "PROJECT":
                scopeId = `PROJECT_${faker.string.alphanumeric(8)}`;
                break;
              default:
                scopeId = "GLOBAL";
            }

            await prisma.permissionOverride.create({
              data: {
                permissionId: permission.id,
                scopeType: randomScopeType,
                scopeId: scopeId,
                revokedOverrideId: userOverride.id, // This makes it a revoked permission
              },
            });

            console.log(`  Added REVOKED permission ${permission.action} with ${randomScopeType} scope (${scopeId})`);
          }
        }
      }
    }
  } catch (error) {
    console.error("Error seeding user overrides:", error);
    throw error;
  }
}


async function seedGroups(companyUsers, permissions) {
  console.log("Seeding groups...");
  try {
    const allCreatedGroups = [];

    for (const { company, users } of companyUsers) {
      console.log(`Creating groups for company: ${company.name}`);
      const createdGroups = [];

      for (const groupTemplate of standardGroups) {
        const existingGroup = await prisma.group.findFirst({
          where: {
            name: groupTemplate.name,
            companyId: company.id,
          },
        });

        let group;
        if (existingGroup) {
          console.log(`Group ${groupTemplate.name} already exists for company ${company.name}`);
          group = existingGroup;
        } else {
          group = await prisma.group.create({
            data: {
              name: groupTemplate.name,
              description: groupTemplate.description,
              companyId: company.id,
            },
          });
          console.log(`Created group ${group.name} for company ${company.name}`);
        }

        createdGroups.push(group);

        const existingUserGroups = await prisma.userGroup.findMany({
          where: { groupId: group.id },
        });

        if (existingUserGroups.length === 0) {
          if (groupTemplate.name === "Company") {
            for (const user of users) {
              await prisma.userGroup.create({
                data: {
                  userId: user.id,
                  groupId: group.id,
                },
              });
              console.log(`Added user ${user.email} to Company group`);
            }
          } else {
            const maxUsers = Math.min(users.length, 5);
            const minUsers = Math.min(2, users.length);
            const randomUsers = getRandomItems(users, minUsers, maxUsers);
            for (const user of randomUsers) {
              await prisma.userGroup.create({
                data: {
                  userId: user.id,
                  groupId: group.id,
                },
              });
              console.log(`Added user ${user.email} to ${group.name} group`);
            }
          }
        }

        const existingGroupPermissions = await prisma.groupPermission.findMany({
          where: { groupId: group.id },
        });

        if (existingGroupPermissions.length === 0 && groupTemplate.name !== "Company") {
          let groupPermissions;
          let scopeType;
          let scopeId;

          switch (groupTemplate.name) {
            case "Engineering":
              groupPermissions = permissions.filter(p =>
                p.action.includes("INTEGRATION") ||
                p.action.includes("FORM") ||
                p.action.includes("WEB") ||
                p.action.includes("FTP") ||
                p.action.includes("CREATE") ||
                p.action.includes("UPDATE")
              );
              scopeType = "TEAM";
              scopeId = group.id;
              break;
            case "Marketing":
              groupPermissions = permissions.filter(p =>
                p.action.includes("EMAIL") ||
                p.action.includes("FORM") ||
                p.action.includes("VIEW") ||
                p.action.includes("EXPORT")
              );
              scopeType = "TEAM";
              scopeId = group.id;
              break;
            case "Sales":
              groupPermissions = permissions.filter(p =>
                p.action.includes("INTERVIEW") ||
                p.action.includes("EMAIL") ||
                p.action.includes("VIEW") ||
                p.action.includes("FEEDBACK")
              );
              scopeType = "TEAM";
              scopeId = group.id;
              break;
            case "Finance":
              groupPermissions = permissions.filter(p =>
                p.action.includes("BILLING") ||
                p.action.includes("SUBSCRIPTION") ||
                p.action.includes("PLAN") ||
                p.action.includes("VIEW")
              );
              scopeType = "GLOBAL";
              scopeId = company.id;
              break;
            default:
              groupPermissions = getRandomItems(permissions, 2, 5);
          }

          // Assign permissions (optional, not originally included)
          for (const permission of groupPermissions) {
            await prisma.groupPermission.create({
              data: {
                groupId: group.id,
                permissionId: permission.id,
                scopeType,
                scopeId,
              },
            });
          }
        }
      }

      allCreatedGroups.push({ company, groups: createdGroups });
    }

    return allCreatedGroups;
  } catch (error) {
    console.error("Error seeding groups:", error);
    throw error;
  }
}

async function seedCompaniesAndUsers() {
  try {
    console.log("Starting comprehensive seeding process...");

    // Seed subscriptions first
    const subscriptions = await seedSubscriptions();

    // Seed roles and permissions first
    const { roles: createdRoles, permissions: createdPermissions } =
      await seedRolesAndPermissions();

    // Seed companies with subscriptions
    const companies = await seedCompanies(subscriptions);

    // Seed users for each company with proper role relationships
    const companyUsers = await seedUsers(companies, createdRoles);

    // Create user overrides for testing override functionality
    await seedUserOverrides(companyUsers, createdPermissions);

    // Create groups for each company and add users to groups
    const companyGroups = await seedGroups(companyUsers, createdPermissions);

    // Seed Integrations for all companies
    await seedFakeIntegrations();
    console.log("Fake integrations seeded successfully");

    console.log("Comprehensive seeding completed successfully!");
    console.log("\n=== SEEDING SUMMARY ===");
    console.log(`✅ Created ${subscriptions.length} subscription plans`);
    console.log(`✅ Created ${createdRoles.length} roles with permissions`);
    console.log(`✅ Created ${companies.length} companies`);
    
    let totalUsers = 0;
    let totalGroups = 0;
    companyUsers.forEach(({ company, users }) => {
      console.log(`✅ Company "${company.name}": ${users.length} users created`);
      totalUsers += users.length;
    });
    
    companyGroups.forEach(({ company, groups }) => {
      console.log(`✅ Company "${company.name}": ${groups.length} groups created`);
      totalGroups += groups.length;
    });
    
    console.log(`✅ Total: ${totalUsers} users, ${totalGroups} groups`);
    console.log(`✅ User overrides created with various scopes for testing`);
    console.log(`✅ Role permissions assigned with GLOBAL, COMPANY, TEAM scopes`);
    console.log(`✅ Group permissions assigned with appropriate scopes`);
    console.log("=== END SUMMARY ===\n");

    return { companies, companyUsers, companyGroups, subscriptions };
  } catch (error) {
    console.error("Error seeding companies and users:", error);
    throw error;
  }
}

// Run the seed function if this file is executed directly
if (require.main === module) {
  seedCompaniesAndUsers()
    .then(() => {
      console.log("Companies and users seeded successfully");
      process.exit(0);
    })
    .catch((error) => {
      console.error("Seeding failed:", error);
      process.exit(1);
    });
}

module.exports = {
  seedCompaniesAndUsers,
  seedRolesAndPermissions,
  seedSubscriptions,
  roles,
  permissionCategories,
  FEATURE_DEFINITIONS,
};