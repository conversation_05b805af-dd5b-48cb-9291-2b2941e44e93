const prisma = require("../config/db");
const { createIndexAndFeedDocuments, generateSampleDocuments } = require("../utils/integration-helper");
const { v4: uuidv4 } = require("uuid");
const opensearch = require("@/config/opensearch");


function generateRandomString(length) {
  let result = '';
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  const charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
}

const fakeIntegrations = [
  {
    name: "Jira Integration",
    providerType: "jira",
    status: "loading",
    updateTime: 5,
    firstLoad: true,
    celeryTaskId: uuidv4(),
  },
  {
    name: "FTP Integration",
    providerType: "ftp",
    status: "completed",
    updateTime: 10,
    firstLoad: false,
    celeryTaskId: uuidv4(),
  },
  {
    name: "Web Integration",
    providerType: "web",
    status: "refreshing",
    updateTime: 15,
    firstLoad: true,
    celeryTaskId: uuidv4(),
  },
];

async function attachIndicesToGroups(company) {
  console.log("Attaching indices to groups...");
  try {
    const groups = await prisma.group.findMany({
      where: { companyId: company.id },
    });

    const integrations = await prisma.integration.findMany({
      where: { companyId: company.id },
    });

    for (const group of groups) {
      console.log(`Attaching indices to group: ${group.name}`);

      for (const integration of integrations) {
        await prisma.groupIntegration.create({
          data: {
            groupId: group.id,
            integrationId: integration.id,
          },
        });

        console.log(
          `Attached integration ${integration.name} to group ${group.name}`
        );
      }
    }

    console.log("Indices attached to groups successfully");
  } catch (error) {
    console.error("Error attaching indices to groups:", error);
    throw error;
  }
}

async function seedFakeIntegrations() {
  console.log("Seeding fake integrations...");
  try {
    const companies = await prisma.company.findMany();

    for (const company of companies) {
      console.log(`Creating fake integrations for company: ${company.name} (${company.id})`);

      for (const integrationTemplate of fakeIntegrations) {
        const existingIntegration = await prisma.integration.findFirst({
          where: {
            name: integrationTemplate.name,
            companyId: company.id,
          },
        });

        // If the integration exists, check if the associated OpenSearch index exists.
        if (existingIntegration) {
          console.log(`Integration ${integrationTemplate.name} already exists for company ${company.name}`);
          const indexResponse = await opensearch.indices.exists({
            index: existingIntegration.opensearchIndexId,
          });
          if (indexResponse.statusCode === 404) {
            console.log(`Index ${existingIntegration.opensearchIndexId} does not exist in OpenSearch. Recreating index...`);
            const documents = generateSampleDocuments(integrationTemplate.providerType);
            await createIndexAndFeedDocuments(existingIntegration.opensearchIndexId, documents);
          } else {
            console.log(`Index ${existingIntegration.opensearchIndexId} exists in OpenSearch.`);
          }
          continue;
        }

        // Create new integration record and associated index if it doesn't exist.
        const opensearchIndexId = `${integrationTemplate.providerType}-${uuidv4()}`;
        const indexName = opensearchIndexId;

        const documents = generateSampleDocuments(integrationTemplate.providerType);
        await createIndexAndFeedDocuments(indexName, documents);

        const integration = await prisma.integration.create({
          data: {
            name: integrationTemplate.name,
            providerType: integrationTemplate.providerType,
            status: integrationTemplate.status,
            updateTime: integrationTemplate.updateTime,
            firstLoad: integrationTemplate.firstLoad,
            createdBy: "system",
            companyId: company.id,
            opensearchIndexId: opensearchIndexId,
            celeryTaskId: integrationTemplate.celeryTaskId // This must be provided
          },
        });

        console.log(`Created integration ${integration.name} for company ${company.name}`);

        // Add specific credentials for Jira, Web, and FTP integrations
        if (integrationTemplate.providerType === "jira") {
          await prisma.jiraIntegration.create({
            data: {
              id: integration.id,
              domain: `jira-${generateRandomString(5)}.example.com`,
              project: `Project-${generateRandomString(3)}`,
              email: `user-${generateRandomString(5)}@example.com`,
              encryptedToken: generateRandomString(20), // Mock encrypted token
            },
          });
          console.log(`Added Jira credentials for integration ${integration.name}`);
        } else if (integrationTemplate.providerType === "web") {
          await prisma.webIntegration.create({
            data: {
              id: integration.id,
              url: `https://web-${generateRandomString(5)}.example.com`,
            },
          });
          console.log(`Added Web credentials for integration ${integration.name}`);
        } else if (integrationTemplate.providerType === "ftp") {
          await prisma.ftpIntegration.create({
            data: {
              id: integration.id,
              server: `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
              port: Math.floor(Math.random() * (65535 - 20 + 1)) + 20,
              username: `user_${generateRandomString(5)}`,
              encryptedPassword: generateRandomString(20), // Mock encrypted password
              isSecure: Math.random() < 0.5,
            },
          });
          console.log(`Added FTP credentials for integration ${integration.name}`);
        }
      }

      // Attach indices to groups
      await attachIndicesToGroups(company);
    }

    console.log("Fake integrations seeded successfully");
    return true;
  } catch (error) {
    console.error("Error seeding fake integrations:", error);
    throw error;
  }
}

module.exports = { seedFakeIntegrations };
