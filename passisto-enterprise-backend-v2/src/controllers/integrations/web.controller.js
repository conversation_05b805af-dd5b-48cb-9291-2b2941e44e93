const prisma = require('@/config/db');
const { checkWebDuplicate, checkWebUrlReachable } = require('@/services/integrations/web.service');
const { v4: uuidv4 } = require('uuid');
const axios = require('axios');
const pgVectorService = require('@/config/pgvector'); // Updated to use pgvector
const crypto = require('crypto');

/**
 * Generate a safe table name that respects PostgreSQL's 63-character limit
 * @param {string} prefix - The prefix for the table name (e.g., 'web')
 * @param {string} companyId - The company ID
 * @param {string} uuid - The UUID for uniqueness
 * @returns {string} A safe table name
 */
function generateSafeTableName(prefix, companyId, uuid) {
  // PostgreSQL identifier limit is 63 characters
  const MAX_LENGTH = 63;

  // Create a short hash of the full identifier for uniqueness
  const fullName = `${prefix}-${companyId}-${uuid}`;
  const hash = crypto.createHash('sha256').update(fullName).digest('hex').substring(0, 8);

  // Take first 8 chars of companyId and first 8 chars of uuid
  const shortCompanyId = companyId.substring(0, 8);
  const shortUuid = uuid.substring(0, 8);

  // Format: prefix-shortCompanyId-shortUuid-hash (e.g., web-1fa04585-770775b2-a1b2c3d4)
  const safeName = `${prefix}-${shortCompanyId}-${shortUuid}-${hash}`;

  // Ensure it doesn't exceed the limit
  return safeName.length > MAX_LENGTH ? safeName.substring(0, MAX_LENGTH) : safeName;
}

async function createWebIntegration(req, res) {
  const {
    name,
    providerType,
    url,
    updateTime,
  } = req.body;
  const { companyId, userId } = req.user;

  const isDuplicate = await checkWebDuplicate({ url, companyId });
  if (isDuplicate) {
    return res.status(400).json({ error: 'Web integration already exists for this company.' });
  }

  const isReachable = await checkWebUrlReachable(url);
  if (!isReachable) {
    return res.status(400).json({ error: 'URL is not reachable or invalid.' });
  }

  const uuid = uuidv4();
  const collectionName = generateSafeTableName('web', companyId, uuid); // Generate safe collection name

  try {
    console.log(`[INFO] [${new Date().toISOString()}] Sending Web task to Scrapyd for URL ${url}`);

    const scrapydResponse = await axios.post(`${process.env.SCRAPYD_SERVER}/schedule.json`, null, {
      headers: { 'Content-Type': 'multipart/form-data' },
      auth: {
        username: process.env.SCRAPYD_USERNAME,
        password: process.env.SCRAPYD_PASSWORD
      },
      params: {
        project: process.env.SCRAPYD_PROJECT,
        spider: process.env.SCRAPYD_SPIDER,
        index_name: collectionName,
        website_url: url,
      }
    });

    const jobId = scrapydResponse?.data?.jobid;
    if (!jobId) {
      console.error(`[ERROR] Scrapyd did not return a job ID`);
      return res.status(500).json({ error: 'Failed to trigger Scrapyd job' });
    }

    const userEmail = await prisma.user.findUnique({
      where: {
        id: userId, // Use the correct field name for the `User` model's ID
      },
      select: {
        email: true, // Select only the email field
      },
    });
    
    // Create integration with collection name (LangChain will handle vector store creation)
    const integration = await prisma.Integration.create({
      data: {
        name,
        providerType,
        updateTime,
        collectionName, // Store collection name for LangChain
        celeryTaskId: jobId,
        createdBy: userEmail.email,
        company: {
          connect: { id: companyId },
        },
        web: {
          create: {
            url,
          },
        },
      },
      include: {
        web: true,
      },
    });

    console.log(`[INFO] [${new Date().toISOString()}] Web integration created and Scrapyd job ${jobId} triggered for company ${companyId}`);

    return res.status(201).json({
      message: 'Integration created and Scrapyd job dispatched',
      data: integration,
      jobId,
    });
  } catch (error) {
    console.error(`[ERROR] [${new Date().toISOString()}] Failed to create Web integration:`, error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Get Web Integrations for a specific company
 */
async function getWebIntegrations(req, res) {
  const { companyId, userId } = req.user;

  try {
    const integrations = await prisma.Integration.findMany({
      where: {
        companyId,
        providerType: 'web',
      },
      include: { 
        web: true,
      },
    });

    return res.status(200).json(integrations);
  } catch (error) {
    console.error(`[ERROR] [${new Date().toISOString()}] Failed to get Web integrations:`, error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}


async function deleteWebIntegration(req, res) {
  const { id } = req.params;
  try {
    // Fetch the integration to get the Scrapy job ID and collection name
    const integration = await prisma.Integration.findUnique({
      where: { id },
      include: { 
        web: true,
      },
    });

    if (!integration) {
      return res.status(404).json({ error: 'Integration not found' });
    }

    const { collectionName, celeryTaskId } = integration;

    // Remove the LangChain collection
    if (collectionName) {
      console.log(`[INFO] [${new Date().toISOString()}] Deleting collection ${collectionName}`);
      try {
          const success = await pgVectorService.deleteCollection(collectionName);
          if (success) {
            console.log(`[INFO] [${new Date().toISOString()}] Collection ${collectionName} deleted successfully`);
          } else {
            console.warn(`[WARN] [${new Date().toISOString()}] Failed to delete collection ${collectionName}`);
          }
      } catch(error) {
        console.warn(`[WARN] [${new Date().toISOString()}] Collection ${collectionName} doesn't exist:`, error.message);
      }
    }

    const project = process.env.SCRAPYD_PROJECT;
    if (celeryTaskId) {
      console.log(`[INFO] [${new Date().toISOString()}] Canceling Scrapy job ${celeryTaskId} for project ${project}`);
      const cancelResponse = await axios.post(`${process.env.SCRAPYD_SERVER}/cancel.json`, null, {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        auth: {
          username: process.env.SCRAPYD_USERNAME,
          password: process.env.SCRAPYD_PASSWORD,
        },
        params: {
          project,
          job: celeryTaskId,
        },
      });
      if (cancelResponse?.data?.status !== 'ok') {
        console.warn(`[WARN] [${new Date().toISOString()}] Failed to cancel Scrapy job ${celeryTaskId}`);
      } else {
        console.log(`[INFO] [${new Date().toISOString()}] Scrapy job ${celeryTaskId} canceled successfully`);
      }
    }
    // Delete the integration and its associated web integration
    await prisma.$transaction(async (prisma) => {
      await prisma.GroupIntegration.deleteMany({
        where: { integrationId: id },
      });
      await prisma.WebIntegration.delete({
        where: { id },
      });
      await prisma.Integration.delete({
        where: { id },
      });
    });
    console.log(`[INFO] [${new Date().toISOString()}] Deleted Web integration with ID ${id}`);
    return res.status(200).json({ message: 'Integration deleted successfully' });
  } catch (error) {
    console.error(`[ERROR] [${new Date().toISOString()}] Failed to delete Web integration:`, error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}


const updateWebIntegration = async (req, res) => {
  const { id } = req.params;
  const { companyId, userId } = req.user;

  const {
    name,
    providerType,
    url,
    updateTime,
  } = req.body;

  try {
    const existing = await prisma.Integration.findUnique({
      where: { id },
      include: { 
        web: true,
      }
    });

    if (!existing || existing.companyId !== companyId) {
      return res.status(404).json({ message: 'Integration not found or unauthorized' });
    }

    const isReachable = await checkWebUrlReachable(url);
    if (!isReachable) {
      return res.status(400).json({ error: 'URL is not reachable or invalid.' });
    }

    console.log(`[INFO] [${new Date().toISOString()}] Sending Web task to Scrapyd for URL ${url}`);

    const scrapydResponse = await axios.post(`${process.env.SCRAPYD_SERVER}/schedule.json`, null, {
      headers: { 'Content-Type': 'multipart/form-data' },
      auth: {
        username: process.env.SCRAPYD_USERNAME,
        password: process.env.SCRAPYD_PASSWORD,
      },
      params: {
        project: process.env.SCRAPYD_PROJECT,
        spider: process.env.SCRAPYD_SPIDER,
        index_name: existing.collectionName, // Use collection name
        website_url: url,
      }
    });

    const jobId = scrapydResponse?.data?.jobid;
    if (!jobId) {
      console.error(`[ERROR] Scrapyd did not return a job ID`);
      return res.status(500).json({ error: 'Failed to trigger Scrapyd job' });
    }
    const updatedIntegration = await prisma.Integration.update({
      where: { id },
      data: {
        name,
        providerType,
        updateTime,
        celeryTaskId: jobId, // Store Scrapyd job ID
        firstLoad: true,
        status: 'loading'
      }
    });
    await prisma.WebIntegration.update({
      where: { id },
      data: {
        url,
      }
    });

    console.log(`[INFO] Web integration updated (ID: ${id})`);
    res.json({ message: 'Web integration updated', updatedIntegration });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Error updating Web integration' });
  }
};

module.exports = {
  createWebIntegration,
  getWebIntegrations,
  deleteWebIntegration,
  updateWebIntegration,
};
