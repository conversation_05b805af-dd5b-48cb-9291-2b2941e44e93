const prisma = require('@/config/db');
const { redisDeleteTaskKey, encrypt, decrypt } = require('@/utils/helpers')
const { FTPceleryClient } = require("@/config/celery");
const pgVectorService = require('@/config/pgvector'); // Updated to use pgvector
const { v4: uuidv4 } = require('uuid');
const { checkFtpConnection, checkFtpDuplicate } = require('@/services/integrations/ftp.service');


async function createFtpIntegration(req, res) {
  const { companyId, userId } = req.user;
  const {
    name,
    providerType,
    server,
    port,
    username,
    password,
    updateTime,
    isSecure
  } = req.body;
  const collectionName = `ftp-${companyId}-${uuidv4()}`; // Generate unique collection name
  try {
    const isValid = await checkFtpConnection(server, port, username, password, isSecure );
    if (!isValid) {
      return res.status(400).json({ error: 'FTP credentials are invalid or connection failed.' });
    }
    const isDuplicate = await checkFtpDuplicate(server, username, companyId );
    if (isDuplicate) {
      return res.status(409).json({
        error: 'An FTP integration with the same host and username already exists for this company.',
      });
    }
    const encryptedPassword = encrypt(password);
    console.log(`[INFO] [${new Date().toISOString()}] Sending FTP task to Celery for ${server}`);
    
    const taskResult = await FTPceleryClient.sendTask(
      'celery.ftp_loader',
      [isSecure, server, port, username, encryptedPassword, collectionName], // Use collection name
      {}
    );
    const taskId = taskResult?.taskId;
    if (!taskId) {
      console.error(`[ERROR] Celery did not return a task ID`);
      return res.status(500).json({ error: 'Failed to trigger FTP loader task' });
    }

    const userEmail = await prisma.user.findUnique({
      where: {
        id: userId, // Use the correct field name for the `User` model's ID
      },
      select: {
        email: true, // Select only the email field
      },
    });

    // Create integration with collection name (LangChain will handle vector store creation)
    const integration = await prisma.Integration.create({
      data: {
        name,
        providerType,
        updateTime,
        collectionName, // Store collection name for LangChain
        celeryTaskId: taskId,
        createdBy: userEmail.email,
        company: {
          connect: { id: companyId },
        },  
        ftp: {
          create: {
            server,
            port,
            username,
            encryptedPassword,
            isSecure
          },
        },
      },
      include: { 
        ftp: true,
      },
    });
    console.log(`[INFO] [${new Date().toISOString()}] FTP integration created with ID ${integration.id} for company ${companyId}`);
    return res.status(201).json(integration);
  } catch (error) {
    console.error(`[ERROR] [${new Date().toISOString()}] Failed to create FTP integration:`, error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}


async function getFtpIntegrations(req, res) {
  const { companyId, userId } = req.user;
  try {
    const integrations = await prisma.Integration.findMany({
      where: {
        companyId,
        providerType: 'ftp',
      },
      include: { 
        ftp: true, 
       },
    });
    return res.status(200).json(integrations);
  } catch (error) {
    console.error(`[ERROR] FTP integration failed:`, error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}


async function deleteFtpIntegration(req, res) {
  const { id } = req.params;
  try{
    const integration = await prisma.Integration.findUnique({
      where: { id },
      include: { 
        ftp: true,
      },
    });
    if (!integration) {
      return res.status(404).json({ error: 'Integration not found' });
    }
    const { collectionName } = integration;
    // Remove the LangChain collection
    if (collectionName) {
        console.log(`[INFO] [${new Date().toISOString()}] Deleting collection ${collectionName}`);
        try{
            const success = await pgVectorService.deleteCollection(collectionName);
            if (success) {
              console.log(`[INFO] [${new Date().toISOString()}] Collection ${collectionName} deleted successfully`);
            } else {
              console.warn(`[WARN] [${new Date().toISOString()}] Failed to delete collection ${collectionName}`);
            }
        }catch(error){
          console.warn(`[WARN] [${new Date().toISOString()}] Collection ${collectionName} doesn't exist:`, error.message);
        }
      }

    await prisma.$transaction(async (prisma) => {
      await prisma.GroupIntegration.deleteMany({
        where: { integrationId: id },
      });
      await prisma.FtpIntegration.delete({
        where: { id },
      });
      await prisma.Integration.delete({
        where: { id },
      });
    });
    console.log(`[INFO] [${new Date().toISOString()}] Deleted FTP integration with ID ${id}`);
    return res.status(200).json({ message: 'Integration deleted successfully' });
    
  }catch (error) {
    console.error(`[ERROR] [${new Date().toISOString()}] Failed to delete FTP integration:`, error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}


const updateFtpIntegration = async (req, res) => {
  const { id } = req.params;
  const { companyId, userId } = req.user;

  const {
    name,
    providerType,
    server,
    port,
    username,
    password,
    isSecure,
    updateTime,
  } = req.body;

  try {
    const existing = await prisma.Integration.findUnique({
      where: { id },
      include: { 
        ftp: true,
      },
    });

    if (!existing || existing.companyId !== companyId) {
      return res.status(404).json({ message: 'Integration not found or unauthorized' });
    }

    const ftpTestPassword = password ? password : decrypt(existing.ftp.encryptedPassword);

    const exists = await checkFtpConnection(server, port, username, ftpTestPassword, isSecure);
    if (!exists) {
      return res.status(400).json({ error: 'FTP server does not exist or credentials are invalid.' });
    }

    console.log(`[INFO] [${new Date().toISOString()}] Sending FTP task to Celery for ${server}`);
    redisDeleteTaskKey(existing); // Clean old task metadata in Redis Celery backend

    const UpdateEncryptedPassword = password ? encrypt(password) : existing.ftp.encryptedPassword;
    const taskResult = await FTPceleryClient.sendTask(
      'celery.ftp_loader',
      [isSecure, server, port, username, UpdateEncryptedPassword, existing.collectionName], // Use collection name
      {}
    );

    const taskId = taskResult?.taskId;
    if (!taskId) {
      console.error(`[ERROR] Celery did not return a task ID`);
      return res.status(500).json({ error: 'Failed to trigger FTP loader task' });
    }

    // Perform the update transactionally
    await prisma.$transaction(async (prisma) => {
      await prisma.Integration.update({
        where: { id },
        data: {
          name,
          providerType,
          updateTime,
          celeryTaskId: taskId,
          firstLoad: true,
          status: 'loading',
        },
      });

      await prisma.FtpIntegration.update({
        where: { id: existing.ftp.id },
        data: {
          server,
          port,
          username,
          isSecure,
          encryptedPassword: UpdateEncryptedPassword, // Always update the password in the database
        },
      });
    });

    console.log(`[INFO] FTP integration updated (ID: ${id})`);
    res.json({ message: 'FTP integration updated' });
  } catch (err) {
    console.error(`[ERROR] [${new Date().toISOString()}] Failed to update FTP integration:`, err);
    res.status(500).json({ message: 'Error updating FTP integration' });
  }
};


async function retryFtpIntegration(req, res) {
  const { id } = req.params;
  try {
    const integration = await prisma.integration.findUnique({
      where: { id: id },
      include: { 
        ftp: true,
      },
    });
    
    if (!integration) {
      return res.status(404).json({ error: 'FTP integration not found' });
    }

    redisDeleteTaskKey(integration);  //clean old task metadata in redis celery backend

    const taskResult = await FTPceleryClient.sendTask(
      'celery.ftp_loader',
      [integration.ftp.isSecure, integration.ftp.server, integration.ftp.port, 
        integration.ftp.username, integration.ftp.encryptedPassword, 
        integration.collectionName ], // Use collection name
      {}
    );
    const taskId = taskResult?.taskId;
    if (!taskId) {
      console.error(`[ERROR] Celery did not return a task ID during FTP retry`);
      return res.status(500).json({ error: 'Failed to retry FTP loader task' });
    }
    await prisma.integration.update({
      where: { id: id },
      data: { celeryTaskId: taskId },
    });
    console.log(`[INFO] [${new Date().toISOString()}] Retried FTP task for integration ${id}, new task ID: ${taskId}`);
    return res.status(200).json({ message: 'Retry successful', taskId });
  } catch (error) {
    console.error(`[ERROR] [${new Date().toISOString()}] Failed to retry FTP integration:`, error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}


module.exports = {
  createFtpIntegration,
  getFtpIntegrations,
  deleteFtpIntegration,
  updateFtpIntegration,
  retryFtpIntegration
};
