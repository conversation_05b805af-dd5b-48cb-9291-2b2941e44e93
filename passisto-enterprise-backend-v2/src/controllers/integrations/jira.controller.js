const prisma = require("@/config/db");
const { JIRAceleryClient } = require("@/config/celery");
const { redisDeleteTaskKey, encrypt, decrypt } = require('@/utils/helpers')
const { checkJiraDuplicate, checkJiraProjectExists } = require('@/services/integrations/jira.service');
const { v4: uuidv4 } = require('uuid');
const pgVectorService = require('@/config/pgvector'); // Updated to use pgvector

async function createJiraIntegration(req, res) {
  const {
    name,
    providerType,
    domain,
    project,
    email,
    token,
    updateTime,
  } = req.body;
  const { companyId, userId } = req.user;
  const collectionName = `jira-${companyId}-${uuidv4()}`; // Generate unique collection name
  try {    
    const exists = await checkJiraProjectExists(domain, email, token, project);
    if (!exists) {
      return res.status(400).json({ error: 'Jira project does not exist or credentials are invalid.' });
    }

    const isDuplicate = await checkJiraDuplicate(domain, project, companyId);
    if (isDuplicate) {
      return res.status(409).json({
        error: 'A Jira integration with the same domain and project already exists for this company.',
      });
    }
    console.log(`[INFO] [${new Date().toISOString()}] Sending Jira task to Celery for project ${project}`);
    const encryptedToken = encrypt(token);
    
    const taskResult = await JIRAceleryClient.sendTask(
      'celery.jira_loader',
      [domain, email, encryptedToken, project, collectionName], // Use collection name
      {}
    );
    const taskId = taskResult?.taskId;
    if (!taskId) {
      console.error(`[ERROR] [${new Date().toISOString()}] Celery did not return a task ID`);
      return res.status(500).json({ error: 'Failed to trigger Jira loader task' });
    }

    const userEmail = await prisma.user.findUnique({
      where: {
        id: userId, // Use the correct field name for the `User` model's ID
      },
      select: {
        email: true, // Select only the email field
      },
    });

    // Create integration with collection name (LangChain will handle vector store creation)
    const integration = await prisma.Integration.create({
      data: {
        name,
        providerType,
        updateTime,
        collectionName, // Store collection name for LangChain
        celeryTaskId: taskId,
        createdBy: userEmail.email, 
        company: {
          connect: { id: companyId },
        },
        jira: {
          create: {
            domain,
            project,
            email,
            encryptedToken,
          },
        },
      },
      include: { 
        jira: true,
      },
    });
    console.log(`[INFO] [${new Date().toISOString()}] Jira integration successfully created and task ${taskId} sent for company ${companyId}`);
    return res.status(201).json({
      message: 'Integration created and task dispatched to Jira loader',
      data: integration,
      taskId,
    });

  } catch (error) {
    console.error(`[ERROR] [${new Date().toISOString()}] Jira integration failed:`, error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Get all Jira Integrations for a specific company
 */
async function getJiraIntegrations(req, res) {
  const { companyId, userId } = req.user;
  console.log(companyId)
  try {
    const integrations = await prisma.Integration.findMany({
      where: {
        companyId,
        providerType: 'jira',
      },
      include: { 
        jira: true
      },
    });

    return res.status(200).json(integrations);
  } catch (error) {
    console.error(`[ERROR] [${new Date().toISOString()}] Failed to get Jira integrations:`, error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Delete a Jira Integration
 */
async function deleteJiraIntegration(req, res) {
  const { id } = req.params;

  try {
    // Fetch the integration to get the collection name
    const integration = await prisma.Integration.findUnique({
      where: { id },
      include: { 
        jira: true,
      },
    });

    if (!integration) {
      return res.status(404).json({ error: 'Integration not found' });
    }

    const { collectionName } = integration;
    // Remove the LangChain collection
    if (collectionName) {
      console.log(`[INFO] [${new Date().toISOString()}] Deleting collection ${collectionName}`);
      try{
          const success = await pgVectorService.deleteCollection(collectionName);
          if (success) {
            console.log(`[INFO] [${new Date().toISOString()}] Collection ${collectionName} deleted successfully`);
          } else {
            console.warn(`[WARN] [${new Date().toISOString()}] Failed to delete collection ${collectionName}`);
          }
      }catch(error){
        console.warn(`[WARN] [${new Date().toISOString()}] Collection ${collectionName} doesn't exist:`, error.message);
      }
    } 

    // Delete the integration and its associated Jira integration
    await prisma.$transaction(async (prisma) => {
      await prisma.GroupIntegration.deleteMany({
        where: { integrationId: id },
      });
      await prisma.JiraIntegration.delete({
        where: { id },
      });
      await prisma.Integration.delete({
        where: { id },
      });
    });
    console.log(`[INFO] [${new Date().toISOString()}] Deleted Jira integration with ID ${id}`);
    return res.status(200).json({ message: 'Integration deleted successfully' });
  } catch (error) {
    console.error(`[ERROR] [${new Date().toISOString()}] Failed to delete integration:`, error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Update a Jira Integration
 */
const updateJiraIntegration = async (req, res) => {
  const { id } = req.params;
  const { companyId, userId } = req.user;
  const {
    name,
    providerType,
    domain,
    project,
    email,
    token,
    updateTime,
  } = req.body;

  try {
    const existing = await prisma.Integration.findUnique({
      where: { id },
      include: { 
        jira: true
      },
    });

    if (!existing || existing.companyId !== companyId) {
      return res.status(404).json({ message: 'Integration not found or unauthorized' });
    }

    const jiraTestToken = token ? token : decrypt(existing.jira.encryptedToken);
    const exists = await checkJiraProjectExists(domain, email, jiraTestToken, project);
    if (!exists) {
      return res.status(400).json({ error: 'Jira project does not exist or credentials are invalid.' });
    }

    console.log(`[INFO] [${new Date().toISOString()}] Sending Jira task to Celery for project ${project}`);
    redisDeleteTaskKey(existing); // Clean old task metadata in Redis backend
    
    const UpdateEncryptedToken  = token  ? encrypt(token) : existing.jira.encryptedToken;
    const taskResult = await JIRAceleryClient.sendTask(
      'celery.jira_loader',
      [domain, email, UpdateEncryptedToken, project, existing.collectionName], // Use collectionName
      {}
    );

    const taskId = taskResult?.taskId;
    if (!taskId) {
      console.error(`[ERROR] Celery did not return a task ID`);
      return res.status(500).json({ error: 'Failed to trigger Jira loader task' });
    }

    await prisma.$transaction(async (prisma) => {
      await prisma.Integration.update({
        where: { id },
        data: {
          name,
          providerType,
          updateTime,
          celeryTaskId: taskId,
          firstLoad: true,
          status: 'loading',
        },
      });

      await prisma.JiraIntegration.update({
        where: { id },
        data: {
          domain,
          project,
          email,
          encryptedToken: UpdateEncryptedToken
        },
      });
    });

    console.log(`[INFO] Jira integration updated (ID: ${id})`);
    res.json({ message: 'Jira integration updated' });
  } catch (err) {
    console.error(`[ERROR] [${new Date().toISOString()}] Failed to update Jira integration:`, err);
    res.status(500).json({ message: 'Error updating Jira integration' });
  }
};


/**
 * Retry Jira Integration Loader
 */
const retryJiraIntegration = async (req, res) => {
  const { id } = req.params;
  try {
    const integration = await prisma.Integration.findUnique({
      where: { id },
      include: { 
        jira: true
      }
    });

    if (!integration) {
      return res.status(404).json({ message: 'Integration not found' });
    }

    redisDeleteTaskKey(integration);  //clean old task metadata in redis celery backend

    // Trigger Celery loader again
    const taskResult = await JIRAceleryClient.sendTask(
      'celery.jira_loader',
      [
        integration.jira.domain, integration.jira.email, 
        integration.jira.encryptedToken, integration.jira.project, 
        integration.collectionName // Use collectionName
      ],
      {}
    );
    const newTaskId = taskResult?.taskId;
    if (!newTaskId) {
      console.error(`[ERROR] Celery did not return a task ID for retry`);
      return res.status(500).json({ error: 'Failed to trigger Jira loader retry task' });
    }
    // Update integration with the new task ID and status
    const updatedIntegration = await prisma.Integration.update({
      where: { id },
      data: {
        celeryTaskId: newTaskId,
        firstLoad: true,
        status: 'loading',
      },
      include: { jira: true }
    });
    console.log(`[INFO] Retried Jira loader task for integration ${id} with new task ID: ${newTaskId}`);
    return res.status(200).json({
      message: 'Jira loader retried successfully',
      data: updatedIntegration,
      taskId: newTaskId
    });
  } catch (error) {
    console.error(`[ERROR] Failed to retry Jira integration loader:`, error);
    return res.status(500).json({ error: 'Internal server error' });
  }
};


module.exports = {
  createJiraIntegration,
  getJiraIntegrations,
  deleteJiraIntegration,
  updateJiraIntegration,
  retryJiraIntegration
};


