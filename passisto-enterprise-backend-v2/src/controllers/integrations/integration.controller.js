const prisma = require("@/config/db");
const pgVectorService = require("@/config/pgvector");
const { getIndexSize } = require('@/utils/helpers')

async function getIntegrations(req, res) {
  try {
    const { companyId } = req.user; // Get companyId from the middleware
    const integrations = await prisma.Integration.findMany({
      where: { companyId },
      include: {
        
        ftp: {
          select: {
            id: true,
            isSecure: true,
            server: true,
            port: true,
            username: true,
          },
        },
        jira: {
          select: {
            id: true,
            domain: true,
            email: true,
            project: true,
          },
        },
        web: {
          select: {
            id: true,
            url: true,
          },
        },
      },
    });
    return res.json(integrations);
  } catch (error) {
    console.error(`[ERROR] [${new Date().toISOString()}] Failed to get integrations:`, error);
    return res.status(500).json({ message: 'Server error' });
  }
}


async function getIntegrationsByGroup(req, res) {
  try {
    const { userId, companyId } = req.user;

    const user = await prisma.user.findUnique({
      where: { id: userId, companyId: companyId },
      include: {
        roles: true,
      },
    });
    
    const isAdmin = user?.roles.some(role => role.name === "ADMIN");
    
    if (isAdmin) {
      // Admin gets all integrations for the company
      const integrations = await prisma.integration.findMany({
        where: {
          companyId,
        },
        select: {
          id: true,
          name: true,
          collectionName: true,
          providerType: true,
        }
      });
      return res.status(200).json(integrations);
    } 
    else {
      // Get integrations only in the groups the user is part of
      const integrations = await prisma.integration.findMany({
        where: {
          companyId,
          groups: {
            some: {
              group: {
                users: {
                  some: {
                    userId: userId,
                  },
                },
              },
            },
          },
        },
        select: {
          id: true,
          name: true,
          collectionName: true,
          providerType: true,
        }
      });
      return res.status(200).json(integrations);
    }
  } catch (error) {
    console.error(
      `[ERROR] [${new Date().toISOString()}] Failed to get integrations by group:`,
      error
    );
    return res.status(500).json({ message: "Server error" });
  }
}


async function getIntegrationMetrics(req, res) {
  try {
    const { companyId } = req.user; // Get companyId from the middleware
    const integrations = await prisma.Integration.findMany({
      where: { companyId },
      select: {
        status: true,
      },
    });
    const metrics = {
      total: integrations.length,
      active: integrations.filter(i => i.status === 'loading' || i.status === 'refreshing').length,
      completed: integrations.filter(i => i.status === 'completed' || i.status === 'not_updated').length,
      failed: integrations.filter(i => i.status === 'failed').length,
    };
    return res.json(metrics);
  } catch (error) {
    console.error(`[ERROR] [${new Date().toISOString()}] Failed to get integration metrics:`, error);
    return res.status(500).json({ message: 'Server error' });
  }
}

async function getIntegrationById(req, res) {
  const { id } = req.params;
  try {
    const integration = await prisma.Integration.findUnique({
      where: { id },
      include: {
        ftp: {
          select: {
            id: true,
            server: true,
            port: true,
            username: true,
            isSecure: true,
          },
        },
        jira: {
          select: {
            id: true,
            domain: true,
            email: true,
            project: true,
          },
        },
        web: {
          select: {
            id: true,
            url: true,
          },
        },
      },
    });

    if (!integration) {
      return res.status(404).json({ message: "Integration not found" });
    }
    return res.status(200).json(integration);
  } catch (error) {
    console.error(`[ERROR] [${new Date().toISOString()}] Failed to get integration by ID:`, error);
    return res.status(500).json({ message: "Internal server error" });
  }
}


async function attachIntegrationToGroup(req, res) {
  const { companyId } = req.user; // Get companyId from the middleware
  const { integrationId, groupId } = req.body;
  try {
    // Get integration with collection name
    const integration = await prisma.Integration.findUnique({
      where: { 
        id: integrationId, 
        companyId: companyId 
      },
      select: {
        id: true,
        collectionName: true,
      },
    });
    
    // Get group
    const group = await prisma.Group.findUnique({
      where: { 
        id: groupId, 
        companyId: companyId 
      },
      select: {
        id: true,
        opensearchAliasId: true,
      },
    });

    if (!integration?.collectionName || !group?.opensearchAliasId) {
      return res.status(400).json({
        message: 'Missing collection name for the integration or group alias',
      });
    }
    
    // Register the integration-group relation in the database
    await prisma.GroupIntegration.create({
      data: {
        groupId,
        integrationId,
      },
    });

    return res.status(200).json({
      message: `Integration successfully attached to group`,
      collectionName: integration.collectionName,
    });
  
  } catch (error) {
    console.error('Failed to attach integration to group:', error);
    return res.status(500).json({
      message: 'Internal server error while attaching integration to group'
    });
  }
}

async function removeIntegrationFromGroup(req, res) {
  const { companyId } = req.user; // Get companyId from the middleware
  const { integrationId, groupId } = req.body;
  try {
    // Get integration with collection name
    const integration = await prisma.Integration.findUnique({
      where: { 
        id: integrationId, 
        companyId: companyId 
      },
      select: {
        id: true,
        collectionName: true,
      },
    });
    
    // Get group
    const group = await prisma.Group.findUnique({
      where: { 
        id: groupId, 
        companyId: companyId 
      },
      select: {
        id: true,
        opensearchAliasId: true,
      },
    });

    if (!integration?.collectionName || !group?.opensearchAliasId) {
      return res.status(400).json({
        message: 'Missing collection name for the integration or group alias',
      });
    }

    // Remove the integration-group relation from the database
    await prisma.GroupIntegration.deleteMany({
      where: {
        groupId,
        integrationId,
      },
    });
    
    return res.status(200).json({
      message: `Integration successfully removed from group`,
      collectionName: integration.collectionName,
    });
  
  } catch (error) {
    console.error('Failed to remove integration from group:', error);
    return res.status(500).json({
      message: 'Internal server error while removing integration from group'
    });
  }
}



async function getResourceUsage(req, res) {
  try {
    const { companyId } = req.user; // Get companyId from the middleware

    if (!companyId) {
      return res.status(400).json({ error: "Company ID is missing" });
    }

    // Step 1: Fetch all integrations belonging to the company
    const integrations = await prisma.integration.findMany({
      where: { companyId },
      select: {
        id: true,
        collectionName: true,
      },
    });

    let totalSizeInGb = 0;

    if (integrations.length > 0) {
      // Step 2: Calculate size based on LangChain vector store collections
      for (const integration of integrations) {
        if (integration.collectionName) {
          try {
            // Get collection info from PGVectorStore
            const collectionInfo = await pgVectorService.getCollectionInfo(integration.collectionName);
            
            if (collectionInfo && collectionInfo.documentCount > 0) {
              // Approximate size calculation: each document ~1KB + vector (1024 * 4 bytes)
              const sizeInBytes = collectionInfo.documentCount * (1024 + 1024 * 4);
              totalSizeInGb += sizeInBytes / (1024 * 1024 * 1024);
            }
          } catch (error) {
            console.warn(`Failed to get size for collection ${integration.collectionName}:`, error.message);
            // Continue with other collections
          }
        }
      }
    }

    // Step 3: Fetch current usage from CompanyUsage
    const currentUsage = await prisma.companyUsage.findUnique({
      where: { companyId_key: { companyId, key: "storageGbUsed" } },
    });

    // Step 4: Update usage only if it differs from the calculated size
    const roundedSize = Math.round(totalSizeInGb * 1000) / 1000; // Round to 3 decimal places
    if (!currentUsage || Math.abs(currentUsage.value - roundedSize) > 0.001) {
      await prisma.companyUsage.upsert({
        where: { companyId_key: { companyId, key: "storageGbUsed" } },
        update: { value: roundedSize },
        create: { companyId, key: "storageGbUsed", value: roundedSize },
      });
    }

    // Step 5: Fetch max allowed usage from SubscriptionFeature
    const subscriptionFeature = await prisma.subscriptionFeature.findFirst({
      where: {
        subscription: { companies: { some: { id: companyId } } },
        key: "storageLimitGb",
      },
    });

    if (!subscriptionFeature) {
      return res.status(400).json({ error: "Storage limit not defined for the subscription" });
    }

    const usageData = {
      currentUsage: roundedSize,
      maxAllowedUsage: subscriptionFeature.value,
      collections: integrations.length,
    };

    return res.status(200).json(usageData);
  } catch (error) {
    console.error(`[ERROR] [${new Date().toISOString()}] Failed to get resource usage:`, error);
    return res.status(500).json({ message: "Internal server error" });
  }
}

module.exports = { getIntegrations, getIntegrationById, 
  getIntegrationMetrics, attachIntegrationToGroup, 
  removeIntegrationFromGroup, getIntegrationsByGroup, getResourceUsage
};