const prisma = require("../config/db");

const FEATURE_DEFINITIONS = {
  maxEmailAgents: {
    name: "Email Builder",
    unit: "emails/month",
  },
  maxFormAgents: {
    name: "Form Builder",
    unit: "forms/month",
  },
  maxAiInterviewHours: {
    name: "AI Interviewer",
    unit: "minutes/month",
    convert: (v) => Math.round(parseFloat(v) * 60),
    usageKey: "aiInterviewHoursUsed",
  },
  maxSearchQueries: {
    name: "Search Queries",
    unit: "queries/month",
  },
  maxChatMessages: {
    name: "Chat Messages",
    unit: "messages/month",
  },
  maxUsers: {
    name: "Users",
    unit: "users",
  },
  storageLimitGb: {
    name: "Storage",
    unit: "GB",
  },
  fileStorageLimitGb: {
    name: "File Storage",
    unit: "GB/file",
  },
};

const getFeatureValue = (features, key) =>
  features.find((f) => f.key === key)?.value || 0;

// ✅ Updated
const checkUsageLimit = async (req, res) => {
  const { companyId } = req.user;
  const { key } = req.body; // key is the feature key, e.g. "maxEmailAgents"

  try {
    const company = await prisma.company.findUnique({
      where: { id: companyId },
      include: {
        subscription: { include: { features: true } },
        usage: true
      }
    });

    if (!company || !company.subscription) {
      return res.status(403).json({
        success: false,
        message: "No active subscription found"
      });
    }

    const feature = company.subscription.features.find(f => f.key === key);
    if (!feature) {
      return res.status(400).json({
        success: false,
        message: "Feature not found in subscription"
      });
    }

    const def = FEATURE_DEFINITIONS[key];
    let limit = typeof def?.convert === "function" ? def.convert(feature.value) : parseInt(feature.value);

    // Usage key convention: e.g. "emailAgentsUsed" for "maxEmailAgents"
    let usageKey = def?.usageKey || key.replace(/^max/, "").charAt(0).toLowerCase() + key.replace(/^max/, "").slice(1) + "Used";
    let used = company.usage.find(u => u.key === usageKey)?.value || 0;

    // Special case for interview: usage is in hours, convert to minutes
    if (key === "maxAiInterviewHours") {
      used = Math.round(used * 60);
    }

    let remaining = limit - used;
    const isExceeded = used >= limit;

    res.status(200).json({
      success: true,
      isExceeded,
      remaining: remaining >= 0 ? remaining : 0,
      currentUsage: used,
      limit
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Failed to check usage limit"
    });
  }
};

const checkUsageLimitByCompanyId = async (req, res) => {
  const { key, companyId } = req.body; // key is the feature key

  try {
    const company = await prisma.company.findUnique({
      where: { id: companyId },
      include: {
        subscription: { include: { features: true } },
        usage: true
      }
    });

    if (!company || !company.subscription) {
      return res.status(403).json({
        success: false,
        message: "No active subscription found"
      });
    }

    const feature = company.subscription.features.find(f => f.key === key);
    if (!feature) {
      return res.status(400).json({
        success: false,
        message: "Feature not found in subscription"
      });
    }

    const def = FEATURE_DEFINITIONS[key];
    let limit = typeof def?.convert === "function" ? def.convert(feature.value) : parseInt(feature.value);

    let usageKey = def?.usageKey || key.replace(/^max/, "").charAt(0).toLowerCase() + key.replace(/^max/, "").slice(1) + "Used";
    let used = company.usage.find(u => u.key === usageKey)?.value || 0;

    if (key === "maxAiInterviewHours") {
      used = Math.round(used * 60);
    }

    let remaining = limit - used;
    const isExceeded = used >= limit;

    res.status(200).json({
      success: true,
      isExceeded,
      remaining: remaining >= 0 ? remaining : 0,
      currentUsage: used,
      limit
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Failed to check usage limit"
    });
  }
};

// ✅ Updated to use CompanyUsage table instead of fields
const updateCompanyUsage = async (req, res) => {
  const { companyId } = req.user;
  const { key, value } = req.body; // key is now the usage key, e.g. "emailAgentsUsed"

  if (!key || typeof value === "undefined") {
    return res.status(400).json({
      success: false,
      message: "Invalid update type or value"
    });
  }

  try {
    const usage = await prisma.companyUsage.upsert({
      where: {
        companyId_key: {
          companyId,
          key
        }
      },
      update: {
        value: {
          increment: parseFloat(value)
        }
      },
      create: {
        companyId,
        key,
        value: parseFloat(value)
      }
    });

    res.status(200).json({
      success: true,
      usage
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Failed to update company usage"
    });
  }
};

const getCompanySubscriptionName = async (req, res) => {
  const { companyId } = req.user;

  try {
    const company = await prisma.company.findUnique({
      where: { id: companyId },
      include: {
        subscription: true
      }
    });

    if (!company) {
      return res.status(404).json({
        success: false,
        message: "Company not found"
      });
    }

    return res.status(200).json({
      success: true,
      subscriptionName: company.subscription?.name || "No Active Subscription"
    });

  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "Failed to fetch subscription name"
    });
  }
};

// ✅ Updated to use `SubscriptionFeature` + `CompanyUsage`
const getCompanyUsageInfo = async (req, res) => {
  const { companyId } = req.user;

  try {
    const company = await prisma.company.findUnique({
      where: { id: companyId },
      include: {
        subscription: {
          include: { features: true }
        },
        usage: true
      }
    });

    if (!company) {
      return res.status(404).json({
        success: false,
        message: "Company not found"
      });
    }

    const features = (company.subscription?.features || []).map((feature) => {
      const def = FEATURE_DEFINITIONS[feature.key];
      if (!def) return null;

      // Convert value if needed (e.g. hours to minutes)
      let limit =
        typeof def.convert === "function"
          ? def.convert(feature.value)
          : parseInt(feature.value);

      // Find usage for this feature
      let usageKey = def.usageKey || feature.key.replace(/^max/, "").charAt(0).toLowerCase() + feature.key.replace(/^max/, "").slice(1) + "Used";
      let used = company.usage.find((u) => u.key === usageKey)?.value || 0;

      // Special case for interview: usage is in hours, convert to minutes
      if (feature.key === "maxAiInterviewHours") {
        used = Math.round(used * 60);
      }

      let remaining = limit - used;
      return {
        key: feature.key,
        name: def.name,
        unit: def.unit,
        limit,
        used,
        remaining: remaining >= 0 ? remaining : 0,
      };
    }).filter(Boolean);

    return res.status(200).json({
      success: true,
      features,
    });

  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "Failed to fetch usage information"
    });
  }
};

module.exports = {
  updateCompanyUsage,
  checkUsageLimit,
  getCompanySubscriptionName,
  checkUsageLimitByCompanyId,
  getCompanyUsageInfo
};
