const { isOdooConnected, ensureOdooConnection } = require('@/config/odoo.config');
const OdooService = require('@/services/odoo/odoo.service');
const odooService = new OdooService();
const multer = require('multer');
const upload = multer(); // stockage en mémoire
// Middleware pour s'assurer que la connexion Odoo est établie
const ensureOdooConnectionMiddleware = async (req, res, next) => {
  try {
    if (!isOdooConnected()) {
      console.log('[INFO] Odoo not connected, attempting to connect...');
      await ensureOdooConnection();
    }
    next();
  } catch (error) {
    console.error('[ERROR] Failed to connect to Odoo:', error);
    return res.status(503).json({
      error: 'Odoo service unavailable',
      message: 'Unable to connect to Odoo'
    });
  }
};

// Récupérer la liste des tickets
const getHelpdeskTicketsController = async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 100;
    const { companyId } = req.user;

    const tickets = await odooService.getHelpdeskTickets(limit, companyId);

    res.json({
      success: true,
      data: tickets,
      count: tickets.length
    });
  } catch (error) {
    console.error('[ERROR] Fetching tickets failed:', error);
    res.status(500).json({ error: 'Internal error', message: error.message });
  }
};




const createHelpdeskTicketController = async (req, res) => {
  try {
    const { companyId } = req.user;  // companyId = UUID string
    const ticketData = req.body;
    const files = req.files;

    if (!ticketData.name || !ticketData.name.trim()) {
      return res.status(400).json({ error: 'Le champ "name" est requis.' });
    }

    if (!ticketData.description || !ticketData.description.trim()) {
      return res.status(400).json({ error: 'Le champ "description" est requis.' });
    }

    // 1) Trouver l'ID Odoo de saas.company
    const saasCompanyId = await odooService.getSaasCompanyIdByUuid(companyId);
    if (!saasCompanyId) {
      return res.status(400).json({ error: "Company introuvable dans Odoo" });
    }

    // 2) Injecter l'ID dans ticketData (et ne pas toucher company_uuid)
    ticketData.saas_company_id = saasCompanyId;

    // 3) Convertir category_id en entier si besoin
    if (ticketData.category_id) {
      ticketData.category_id = parseInt(ticketData.category_id);
    }

    // 4) Créer le ticket
    const ticketId = await odooService.createHelpdeskTicket(ticketData);

    if (!ticketId) {
      return res.status(500).json({ error: 'La création du ticket a échoué.' });
    }

    // 5) Créer les pièces jointes (fichiers)
    if (files && files.length > 0) {
      for (const file of files) {
        await odooService.createAttachment({
          name: file.originalname,
          datas: file.buffer.toString('base64'),
          res_model: 'helpdesk.ticket',
          res_id: ticketId,
          mimetype: file.mimetype,
        });
      }
    }

    return res.json({
      success: true,
      data: { id: ticketId },
      message: 'Ticket créé avec succès avec pièces jointes',
    });

  } catch (error) {
    console.error('[ERROR] createHelpdeskTicket:', error);
    res.status(500).json({ error: 'Erreur serveur', message: error.message });
  }
};



// Récupérer un ticket par ID
const getHelpdeskTicketByIdController = async (req, res) => {
  try {
    const ticketId = parseInt(req.params.id);
    const { companyId } = req.user;

    const ticket = await odooService.getHelpdeskTicketById(ticketId, companyId);

    if (!ticket) {
      return res.status(404).json({
        error: 'Not found',
        message: `Ticket ${ticketId} not found or unauthorized`
      });
    }

    res.json({ success: true, data: ticket });
  } catch (error) {
    console.error('[ERROR] Fetch ticket by ID failed:', error);
    res.status(500).json({ error: 'Internal error', message: error.message });
  }
};

// Mettre à jour un ticket
// const updateHelpdeskTicketController = async (req, res) => {
//   try {
//     const ticketId = parseInt(req.params.id);
//     const updateData = req.body;
//     const { companyId } = req.user;

//     // Validation (si champs présents)
//     if (updateData.name !== undefined && !updateData.name?.trim()) {
//       return res.status(400).json({ error: 'Validation failed', message: 'Field "name" cannot be empty' });
//     }
//     if (updateData.description !== undefined && !updateData.description?.trim()) {
//       return res.status(400).json({ error: 'Validation failed', message: 'Field "description" cannot be empty' });
//     }

//     // Ne jamais autoriser la modification de saas_company_id
//     delete updateData.saas_company_id;

//     const success = await odooService.updateHelpdeskTicket(ticketId, updateData, companyId);

//     if (!success) {
//       return res.status(404).json({
//         error: 'Update failed',
//         message: `Ticket ${ticketId} not found or unauthorized`
//       });
//     }

//     res.json({ success: true, message: 'Ticket updated successfully' });
//   } catch (error) {
//     console.error('[ERROR] Ticket update failed:', error);
//     res.status(500).json({ error: 'Internal error', message: error.message });
//   }
// };

const updateHelpdeskTicketController = async (req, res) => {
  try {
    const ticketId = parseInt(req.params.id);
    const { name, description, category_id, existing_file_ids } = req.body;
    const files = req.files || [];
    const { companyId } = req.user;

    // Validation (si champs présents)
    if (name !== undefined && !name?.trim()) {
      return res.status(400).json({ error: 'Validation failed', message: 'Field "name" cannot be empty' });
    }
    if (description !== undefined && !description?.trim()) {
      return res.status(400).json({ error: 'Validation failed', message: 'Field "description" cannot be empty' });
    }

    // Préparer les données de mise à jour
    const updateData = {
      ...(name && { name }),
      ...(description && { description }),
      ...(category_id && { category_id: parseInt(category_id) })
    };

    // Parser existing_file_ids si c'est une string JSON
    let existingFileIds = [];
    if (existing_file_ids) {
      existingFileIds = typeof existing_file_ids === 'string' 
        ? JSON.parse(existing_file_ids) 
        : existing_file_ids;
    }

    const success = await odooService.updateHelpdeskTicket(
      ticketId, 
      updateData, 
      companyId, 
      files, 
      existingFileIds
    );

    if (!success) {
      return res.status(404).json({
        error: 'Update failed',
        message: `Ticket ${ticketId} not found or unauthorized`
      });
    }

    res.json({ success: true, message: 'Ticket updated successfully' });
  } catch (error) {
    console.error('[ERROR] Ticket update failed:', error);
    res.status(500).json({ error: 'Internal error', message: error.message });
  }
};



// Supprimer un ticket
const deleteHelpdeskTicketController = async (req, res) => {
  try {
    const ticketId = parseInt(req.params.id);
    const { companyId } = req.user;

    const success = await odooService.deleteHelpdeskTicket(ticketId, companyId);

    if (!success) {
      return res.status(404).json({
        error: 'Deletion failed',
        message: `Ticket ${ticketId} not found or unauthorized`
      });
    }

    res.json({ success: true, message: 'Ticket deleted successfully' });
  } catch (error) {
    console.error('[ERROR] Ticket deletion failed:', error);
    res.status(500).json({ error: 'Internal error', message: error.message });
  }
};

// Récupérer les catégories
const getCategoriesController = async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 100;

    const categories = await odooService.getCategories(limit);

    res.json({
      success: true,
      data: categories,
      count: categories.length
    });
  } catch (error) {
    console.error('[ERROR] Fetching categories failed:', error);
    res.status(500).json({ error: 'Internal error', message: error.message });
  }
};

const getTicketMessagesController = async (req, res) => {
  try {
    const ticketId = parseInt(req.params.id);
    if (isNaN(ticketId)) {
      return res.status(400).json({ error: 'Invalid ticket ID' });
    }

    const messages = await odooService.getTicketMessages(ticketId);

    res.json({
      success: true,
      data: messages,
      count: messages.length
    });
  } catch (error) {
    console.error('[ERROR] getTicketMessages:', error);
    res.status(500).json({ error: 'Internal server error', message: error.message });
  }
};



const postTicketMessageController = async (req, res) => {
  try {
    const ticketId = parseInt(req.params.id);
    const { content } = req.body;

    if (isNaN(ticketId)) return res.status(400).json({ error: 'Invalid ticket ID' });
    if (!content || !content.trim()) return res.status(400).json({ error: 'Content is required' });

    const authorName = req.user.name;
    const authorType = req.user.role === 'agent' ? 'agent' : 'client';

    const attachments = req.files || []; // depuis multer

    const message = await odooService.postTicketMessage(ticketId, content, authorType, authorName, attachments);

    return res.json({
      success: true,
      message: 'Message posted successfully',
      messageId: message
    });

  } catch (error) {
    console.error('[ERROR] postTicketMessage:', error);
    return res.status(500).json({ error: 'Internal server error', message: error.message });
  }
};






// Export des contrôleurs
module.exports = {
  ensureOdooConnection: ensureOdooConnectionMiddleware,
  getHelpdeskTickets: getHelpdeskTicketsController,
  createHelpdeskTicket: createHelpdeskTicketController,
  getHelpdeskTicketById: getHelpdeskTicketByIdController,
  updateHelpdeskTicket: updateHelpdeskTicketController,
  deleteHelpdeskTicket: deleteHelpdeskTicketController,
  getCategories: getCategoriesController,
  getTicketMessages: getTicketMessagesController,     
  postTicketMessage: postTicketMessageController 
};

