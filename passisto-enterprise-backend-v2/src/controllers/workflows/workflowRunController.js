const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const { validationResult } = require('express-validator');

// Get all workflow runs
exports.getWorkflowRuns = async (req, res) => {

  const { userId } = req.user;
  try {
    const workflowRuns = await prisma.workflowRun.findMany({
    where: {
      workflow: {
        userId: userId  // Filter by the workflow's userId
      }
    },
    orderBy: {
      createdAt: 'desc'
    },
    include: {
      workflow: {
        select: {
          id: true,
          name: true,
          user: {
            select: {
              id: true,
              firstName: true
            }
          }
        }
      },
      nodeRuns: true
    }
    });
    res.json(workflowRuns);
  } catch (error) {
    console.error('Error fetching workflow runs:', error);
    res.status(500).json({
      message: 'Failed to fetch workflow runs',
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};

// Get workflow runs by workflow ID
exports.getWorkflowRunsByWorkflowId = async (req, res) => {
  try {
    const workflowRuns = await prisma.workflowRun.findMany({
      where: {
        workflowId: req.params.workflowId
      },
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        nodeRuns: true
      }
    });
    res.json(workflowRuns);
  } catch (error) {
    console.error('Error fetching workflow runs:', error);
    res.status(500).json({ message: error.message });
  }
};

// Get single workflow run
exports.getWorkflowRun = async (req, res) => {
  try {
    const workflowRun = await prisma.workflowRun.findUnique({
      where: { id: req.params.id },
      include: {
        workflow: {
          select: {
            id: true,
            name: true,
            nodes: true,
            edges: true
          }
        },
        nodeRuns: true
      }
    });

    if (!workflowRun) {
      return res.status(404).json({ message: 'Workflow run not found' });
    }
    res.json(workflowRun);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Create workflow run
exports.createWorkflowRun = async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const { workflowId } = req.body;

    // Check if workflow exists with nodes and edges
    const workflow = await prisma.workflow.findUnique({
      where: { id: workflowId },
      select: {
        id: true,
        name: true,
        nodes: true,
        edges: true
      }
    });

    if (!workflow) {
      return res.status(404).json({ message: 'Workflow not found' });
    }

    const workflowRun = await prisma.workflowRun.create({
      data: {
        workflowId,
        status: 'PENDING'
      },
      include: {
        workflow: {
          select: {
            id: true,
            name: true,
            nodes: true,
            edges: true
          }
        }
      }
    });

    // Get socket instance and execution service
    const executionService = req.app.get('workflowExecutionService');

    // Start workflow execution
    executionService.startWorkflowRun(workflowRun.id);

    res.status(201).json(workflowRun);
  } catch (error) {
    console.error('Error creating workflow run:', error);
    res.status(400).json({ message: error.message });
  }
};

// Update workflow run status
exports.updateWorkflowRunStatus = async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const { status } = req.body;

    const workflowRun = await prisma.workflowRun.update({
      where: { id: req.params.id },
      data: {
        status,
        ...(status === 'RUNNING' && { startedAt: new Date() }),
        ...(['SUCCESS', 'FAILED'].includes(status) && { finishedAt: new Date() })
      }
    });

    res.json(workflowRun);
  } catch (error) {
    if (error.code === 'P2025') {
      return res.status(404).json({ message: 'Workflow run not found' });
    }
    res.status(400).json({ message: error.message });
  }
};

// Delete workflow run
exports.deleteWorkflowRun = async (req, res) => {
  try {
    // First delete associated node runs
    await prisma.nodeRun.deleteMany({
      where: { workflowRunId: req.params.id }
    });

    // Then delete the workflow run
    await prisma.workflowRun.delete({
      where: { id: req.params.id }
    });

    res.json({ message: 'Workflow run deleted' });
  } catch (error) {
    if (error.code === 'P2025') {
      return res.status(404).json({ message: 'Workflow run not found' });
    }
    res.status(500).json({ message: error.message });
  }
};

// Get assigned tasks for the current user
exports.getUserAssignedTasks = async (req, res) => {
  try {
    const userId = req.user.userId; // Assuming user info is added by auth middleware

    // Find all node runs assigned to this user that are in WAITING_FOR_USER state
    const assignedTasks = await prisma.nodeRun.findMany({
      where: {
        assigneeId: userId,
        status: 'WAITING_FOR_USER'
      },
      include: {
        workflowRun: {
          include: {
            workflow: {
              select: {
                id: true,
                name: true,
                nodes: true,
                user: {
                  select: {
                    id: true,
                    firstName: true
                  }
                }
              }
            }
          }
        }
      },
      orderBy: {
        assignedAt: 'desc'
      }
    });

    // Transform the data to include node details from the workflow
    const tasksWithDetails = await Promise.all(assignedTasks.map(async task => {
      // Find the node details from the workflow nodes
      const nodes = typeof task.workflowRun.workflow.nodes === 'string'
        ? JSON.parse(task.workflowRun.workflow.nodes)
        : task.workflowRun.workflow.nodes;

      const nodeDetails = nodes.find(node => node.id === task.nodeId) || {};

      console.log(`[getUserAssignedTasks] Processing task ${task.nodeId} for workflow run ${task.workflowRunId}`);
      console.log(`[getUserAssignedTasks] task.output:`, JSON.stringify(task.output, null, 2));
      console.log(`[getUserAssignedTasks] task.formData:`, JSON.stringify(task.formData, null, 2));
      console.log(`[getUserAssignedTasks] nodeDetails.data:`, JSON.stringify(nodeDetails.data, null, 2));

      let contextualData = task.output?.taskDetails?.contextualData || {};
      console.log(`[getUserAssignedTasks] extracted contextualData:`, JSON.stringify(contextualData, null, 2));

      // TEMPORARY FIX: If contextualData is empty but selectedOutputs exist, try to reconstruct it
      if (Object.keys(contextualData).length === 0 && nodeDetails.data?.selectedOutputs?.length > 0) {
        console.log(`[getUserAssignedTasks] Contextual data is empty, attempting to reconstruct from selectedOutputs`);
        try {
          contextualData = await exports.reconstructContextualData(task.workflowRunId, nodeDetails.data.selectedOutputs);
          console.log(`[getUserAssignedTasks] Reconstructed contextualData:`, JSON.stringify(contextualData, null, 2));
        } catch (error) {
          console.error(`[getUserAssignedTasks] Failed to reconstruct contextual data:`, error);
        }
      }

      return {
        id: task.id,
        nodeId: task.nodeId,
        workflowRunId: task.workflowRunId,
        workflowId: task.workflowRun.workflowId,
        workflowName: task.workflowRun.workflow.name,
        workflowOwner: task.workflowRun.workflow.user,
        assignedAt: task.assignedAt,
        status: task.status,
        nodeType: nodeDetails.type || 'unknown',
        nodeLabel: nodeDetails.data?.label || 'Unnamed Task',
        nodeDescription: nodeDetails.data?.description || '',
        nodeCritical: nodeDetails.data?.critical || false,
        // Include form fields from either the node details or the node run output
        formFields: nodeDetails.data?.formFields ||
                   task.formData?.fields ||
                   task.output?.taskDetails?.formFields || [],
        // Include contextual data from previous nodes if available
        contextualData: contextualData
      };
    }));

    console.log(`[getUserAssignedTasks] Returning ${tasksWithDetails.length} tasks with details`);
    console.log(`[getUserAssignedTasks] Final tasksWithDetails:`, JSON.stringify(tasksWithDetails, null, 2));

    res.json(tasksWithDetails);
  } catch (error) {
    console.error('Error fetching user assigned tasks:', error);
    res.status(500).json({ message: error.message });
  }
};

// Complete a user task
exports.completeUserTask = async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const { workflowRunId, nodeId } = req.params;
    const userId = req.user.userId; // Assuming user info is added by auth middleware
    const taskData = req.body.taskData || {};

    // Get the workflow execution service
    const executionService = req.app.get('workflowExecutionService');

    if (!executionService) {
      throw new Error('Workflow execution service not available');
    }

    // Call the service to complete the task
    await executionService.completeUserTask(workflowRunId, nodeId, userId, taskData);

    res.json({
      message: 'Task completed successfully',
      workflowRunId,
      nodeId
    });
  } catch (error) {
    console.error('Error completing user task:', error);
    res.status(400).json({ message: error.message });
  }
};

// Helper method to reconstruct contextual data from selectedOutputs
exports.reconstructContextualData = async function(workflowRunId, selectedOutputs) {
  const contextualData = {};

  for (const selection of selectedOutputs) {
    const { nodeId, key, label } = selection;

    try {
      // Get the node run for this specific node
      const nodeRun = await prisma.nodeRun.findUnique({
        where: {
          workflowRunId_nodeId: {
            workflowRunId: workflowRunId,
            nodeId: nodeId
          }
        }
      });

      if (nodeRun && nodeRun.output && nodeRun.status === 'SUCCESS') {
        const output = typeof nodeRun.output === 'string'
          ? JSON.parse(nodeRun.output)
          : nodeRun.output;

        let value;
        if (key === 'result') {
          value = output.result;
        } else if (key.includes('.')) {
          // Handle nested paths
          const parts = key.split('.');
          let current = output;
          for (const part of parts) {
            if (current && typeof current === 'object' && part in current) {
              current = current[part];
            } else {
              current = undefined;
              break;
            }
          }
          value = current;
        } else {
          value = output[key];
        }

        if (value !== undefined) {
          contextualData[label || `${nodeId}.${key}`] = value;
        }
      }
    } catch (error) {
      console.error(`Error reconstructing contextual data for ${nodeId}.${key}:`, error);
    }
  }

  return contextualData;
};

// Stop workflow run
exports.stopWorkflowRun = async (req, res) => {
  try {
    const workflowRunId = req.params.id;

    // Check if workflow run exists
    const workflowRun = await prisma.workflowRun.findUnique({
      where: { id: workflowRunId }
    });

    if (!workflowRun) {
      return res.status(404).json({ message: 'Workflow run not found' });
    }

    // Get the execution service
    const executionService = req.app.get('workflowExecutionService');

    if (!executionService) {
      throw new Error('Workflow execution service not available');
    }

    // Stop the workflow run
    await executionService.stopWorkflowRun(workflowRunId);

    // Get the updated workflow run
    const updatedWorkflowRun = await prisma.workflowRun.findUnique({
      where: { id: workflowRunId },
      include: {
        workflow: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    res.json({
      message: 'Workflow run stopped successfully',
      workflowRun: updatedWorkflowRun
    });
  } catch (error) {
    console.error('Error stopping workflow run:', error);
    res.status(500).json({ message: error.message });
  }
};

// Get workflow run with detailed node status
exports.getWorkflowRunWithNodeDetails = async (req, res) => {
  try {
    const workflowRun = await prisma.workflowRun.findUnique({
      where: { id: req.params.id },
      include: {
        workflow: {
          select: {
            id: true,
            name: true,
            nodes: true,
            edges: true,
            user: {
              select: {
                id: true,
                firstName: true
              }
            }
          }
        },
        nodeRuns: {
          include: {
            assignee: {
              select: {
                id: true,
                firstName: true,
                email: true
              }
            }
          }
        }
      }
    });

    if (!workflowRun) {
      return res.status(404).json({ message: 'Workflow run not found' });
    }

    // Get the execution service to check in-memory state
    const executionService = req.app.get('workflowExecutionService');
    const inMemoryState = executionService ? executionService.getWorkflowRunStatus(workflowRun.id) : null;

    // Combine database and in-memory state
    const result = {
      ...workflowRun,
      executionPath: inMemoryState?.executionPath || [],
      currentNodeId: inMemoryState?.currentNodeId || null
    };

    res.json(result);
  } catch (error) {
    console.error('Error fetching workflow run details:', error);
    res.status(500).json({ message: error.message });
  }
};