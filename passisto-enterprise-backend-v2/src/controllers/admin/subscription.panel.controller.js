const prisma = require("@/config/db");
const stripe = require("@/config/stripe");


const updateSubscriptionFeature = async (req, res) => {
  const { companyId, key, newValue } = req.body;

  if (!companyId || !key || newValue === undefined) {
    return res.status(400).json({ error: 'Missing required fields: companyId, key, newValue' });
  }

  try {
    const company = await prisma.company.findUnique({
      where: { id: companyId },
      include: { subscription: true },
    });

    if (!company || !company.subscriptionId) {
      return res.status(404).json({ error: 'Company or subscription not found' });
    }

    const updatedFeature = await prisma.subscriptionFeature.upsert({
      where: {
        subscriptionId_key: {
          subscriptionId: company.subscriptionId,
          key,
        },
      },
      update: {
        value: newValue,
      },
      create: {
        subscriptionId: company.subscriptionId,
        key,
        value: newValue,
      },
    });

    return res.status(200).json({ success: true, feature: updatedFeature });
  } catch (err) {
    console.error(err);
    return res.status(500).json({ error: 'Internal server error' });
  }
}


const cancelSubscription = async (req, res) => {
  const { companyId, subscriptionId, targetStatus, cancelType } = req.body;

  if (!companyId || !subscriptionId || !targetStatus || !cancelType) {
    return res.status(400).json({ error: 'Missing required parameters' });
  }
  try {
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    const currentStatus = subscription.status;

    const validTransitions = {
      active: ['canceled'],
      trialing: ['canceled'],
    };

    if (!validTransitions[currentStatus]?.includes(targetStatus)) {
      return res.status(400).json({
        error: `Invalid transition: ${currentStatus} → ${targetStatus}`,
      });
    }
    if (targetStatus === 'canceled') {
      if (cancelType === 'hard') {
        // Immediately delete subscription
        await stripe.subscriptions.cancel(subscriptionId);

        // Update app DB to reflect canceled state
        await prisma.company.update({
          where: { id: companyId },
          data: { subscriptionStatus: 'canceled' },
        });

      } else if (cancelType === 'soft') {
        // Cancel at period end
        await stripe.subscriptions.update(subscriptionId, {
          cancel_at_period_end: true,
        });

        // Update app DB to reflect canceling state
        await prisma.company.update({
          where: { id: companyId },
          data: { subscriptionStatus: 'canceling' },
        });

      } else {
        return res.status(400).json({ error: 'Invalid cancel type' });
      }
    }

    return res.json({ message: `Subscription ${cancelType} canceled successfully` });

  } catch (error) {
    console.error('Stripe error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
};


module.exports = { updateSubscriptionFeature, cancelSubscription };