const BaseNodeExecutor = require('./BaseNodeExecutor');

/**
 * Executor for data extraction nodes
 */
class DataExtractionNodeExecutor extends BaseNodeExecutor {
  /**
   * Execute a data extraction node
   * @param {string} workflowRunId - The ID of the workflow run
   * @param {string} nodeId - The ID of the node to execute
   * @param {Object} node - The node data
   * @param {Object} inputs - The inputs to the node
   * @returns {Promise<{output: Object, success: boolean, error: Error|null}>} - The execution result
   */
  async execute(workflowRunId, nodeId, node, inputs) {
    let output;
    let success = true;
    let error = null;

    try {
      console.log(`[Data Extraction Node] Starting execution for node ${nodeId}`);

      // Get the fields to extract from node configuration
      const fields = node.data?.fields || [];
      const selectedOutputs = node.data?.selectedOutputs || [];

      console.log(`[Data Extraction Node] Fields to extract: ${fields.length}`);
      console.log(`[Data Extraction Node] Selected outputs: ${selectedOutputs.length}`);

      if (fields.length === 0) {
        console.log(`[Data Extraction Node] No fields configured for extraction`);
        output = {
          result: 'No fields configured for data extraction',
          extractedData: {},
          fields: fields
        };
        return { output, success, error };
      }

      // Process selected inputs using the universal system
      let processedInputs = inputs;
      if (selectedOutputs.length > 0) {
        processedInputs = await this.processSelectedInputs(inputs, selectedOutputs, workflowRunId);
        console.log(`[Data Extraction Node] Processed ${selectedOutputs.length} selected inputs`);
      }

      // Extract data based on configured fields
      const extractedData = await this.extractDataFromInputs(processedInputs, fields, selectedOutputs);

      console.log(`[Data Extraction Node] Extracted data:`, extractedData);

      output = {
        result: `Successfully extracted ${Object.keys(extractedData).length} fields from input data`,
        extractedData: extractedData,
        fields: fields,
        processedInputs: processedInputs,
        selectedOutputsCount: selectedOutputs.length
      };

    } catch (err) {
      console.error(`[Data Extraction Node] Error:`, err);
      success = false;
      error = err;
      output = { 
        error: err.message, 
        extractedData: {},
        fields: node.data?.fields || [],
        inputs 
      };
    }

    return { output, success, error };
  }

  /**
   * Extract data from inputs based on configured fields
   * @param {Object} processedInputs - The processed inputs from previous nodes
   * @param {Array} fields - Array of field definitions {name, description}
   * @param {Array} selectedOutputs - Array of selected outputs for context
   * @returns {Promise<Object>} - Extracted data object
   */
  async extractDataFromInputs(processedInputs, fields, selectedOutputs) {
    const extractedData = {};

    // If no inputs available, return empty extraction
    if (!processedInputs || Object.keys(processedInputs).length === 0 || processedInputs.isStartNode) {
      console.log(`[Data Extraction Node] No input data available for extraction`);
      // Initialize all fields with null values
      fields.forEach(field => {
        extractedData[field.name] = null;
      });
      return extractedData;
    }

    // Collect all available data from inputs
    const allInputData = this.collectAllInputData(processedInputs);
    console.log(`[Data Extraction Node] Collected input data keys:`, Object.keys(allInputData));

    // Extract each configured field
    for (const field of fields) {
      try {
        const extractedValue = this.extractFieldValue(allInputData, field);
        extractedData[field.name] = extractedValue;
        console.log(`[Data Extraction Node] Extracted field '${field.name}': ${extractedValue}`);
      } catch (err) {
        console.error(`[Data Extraction Node] Error extracting field '${field.name}':`, err);
        extractedData[field.name] = null;
      }
    }

    return extractedData;
  }

  /**
   * Collect all available data from processed inputs into a flat structure
   * @param {Object} processedInputs - The processed inputs from previous nodes
   * @returns {Object} - Flattened data object with all available values
   */
  collectAllInputData(processedInputs) {
    const allData = {};

    // Iterate through each node's output
    Object.keys(processedInputs).forEach(nodeId => {
      const nodeOutput = processedInputs[nodeId];
      
      if (nodeOutput && typeof nodeOutput === 'object') {
        // Add direct properties
        Object.keys(nodeOutput).forEach(key => {
          const value = nodeOutput[key];
          
          // Create prefixed keys for node-specific data
          allData[`${nodeId}.${key}`] = value;
          
          // Also add without prefix for easier matching
          if (!allData[key]) {
            allData[key] = value;
          }
          
          // If the value is an object, flatten it one level
          if (value && typeof value === 'object' && !Array.isArray(value)) {
            Object.keys(value).forEach(subKey => {
              allData[`${nodeId}.${key}.${subKey}`] = value[subKey];
              if (!allData[`${key}.${subKey}`]) {
                allData[`${key}.${subKey}`] = value[subKey];
              }
            });
          }
        });
      }
    });

    return allData;
  }

  /**
   * Extract a specific field value from the collected input data
   * @param {Object} allInputData - All available input data
   * @param {Object} field - Field definition {name, description}
   * @returns {*} - Extracted value or null if not found
   */
  extractFieldValue(allInputData, field) {
    const fieldName = field.name.toLowerCase();
    const fieldDescription = field.description.toLowerCase();

    // Strategy 1: Direct key match (case-insensitive)
    for (const [key, value] of Object.entries(allInputData)) {
      if (key.toLowerCase() === fieldName) {
        return value;
      }
    }

    // Strategy 2: Key contains field name
    for (const [key, value] of Object.entries(allInputData)) {
      if (key.toLowerCase().includes(fieldName)) {
        return value;
      }
    }

    // Strategy 3: Search in string values for field name or description keywords
    const searchTerms = [
      fieldName,
      ...fieldDescription.split(' ').filter(word => word.length > 2)
    ];

    for (const [key, value] of Object.entries(allInputData)) {
      if (typeof value === 'string') {
        const valueLower = value.toLowerCase();
        for (const term of searchTerms) {
          if (valueLower.includes(term)) {
            return value;
          }
        }
      }
    }

    // Strategy 4: Look for common patterns based on field description
    const extractedValue = this.extractByPattern(allInputData, field);
    if (extractedValue !== null) {
      return extractedValue;
    }

    // Strategy 5: If field name matches common data types, try to find the first matching value
    const commonPatterns = {
      'email': /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/,
      'phone': /\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/,
      'date': /\b\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}\b/,
      'url': /https?:\/\/[^\s]+/,
      'number': /\b\d+\.?\d*\b/
    };

    for (const [pattern, regex] of Object.entries(commonPatterns)) {
      if (fieldName.includes(pattern) || fieldDescription.includes(pattern)) {
        for (const [key, value] of Object.entries(allInputData)) {
          if (typeof value === 'string') {
            const match = value.match(regex);
            if (match) {
              return match[0];
            }
          }
        }
      }
    }

    console.log(`[Data Extraction Node] Could not extract field '${field.name}' from available data`);
    return null;
  }

  /**
   * Extract value based on common patterns in field description
   * @param {Object} allInputData - All available input data
   * @param {Object} field - Field definition
   * @returns {*} - Extracted value or null
   */
  extractByPattern(allInputData, field) {
    const description = field.description.toLowerCase();

    // Look for specific patterns in description
    if (description.includes('first') || description.includes('name')) {
      // Look for name-related data
      for (const [key, value] of Object.entries(allInputData)) {
        if (key.toLowerCase().includes('name') || key.toLowerCase().includes('first')) {
          return value;
        }
      }
    }

    if (description.includes('last') || description.includes('surname')) {
      // Look for last name data
      for (const [key, value] of Object.entries(allInputData)) {
        if (key.toLowerCase().includes('last') || key.toLowerCase().includes('surname')) {
          return value;
        }
      }
    }

    if (description.includes('result') || description.includes('output')) {
      // Look for result data
      for (const [key, value] of Object.entries(allInputData)) {
        if (key.toLowerCase().includes('result') || key.toLowerCase().includes('output')) {
          return value;
        }
      }
    }

    return null;
  }
}

module.exports = DataExtractionNodeExecutor;
