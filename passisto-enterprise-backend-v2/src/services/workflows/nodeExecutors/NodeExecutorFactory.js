const AskAINodeExecutor = require('./AskAINodeExecutor');
const TaskNodeExecutor = require('./TaskNodeExecutor');
const EmailNodeExecutor = require('./EmailNodeExecutor');
const SpeechToTextNodeExecutor = require('./SpeechToTextNodeExecutor');
const DecisionNodeExecutor = require('./DecisionNodeExecutor');
const DataExtractionNodeExecutor = require('./DataExtractionNodeExecutor');
const DefaultNodeExecutor = require('./DefaultNodeExecutor');

/**
 * Factory for creating node executors
 */
class NodeExecutorFactory {
  /**
   * Constructor for the node executor factory
   * @param {Object} prisma - Prisma client instance
   * @param {Object} io - Socket.io instance
   */
  constructor(prisma, io) {
    this.prisma = prisma;
    this.io = io;
    this.executors = {
      'ask-ai': new AskAINodeExecutor(prisma, io),
      'task': new TaskNodeExecutor(prisma, io),
      'email': new EmailNodeExecutor(prisma, io),
      'speech-to-text': new SpeechToTextNodeExecutor(prisma, io),
      'decision': new DecisionNodeExecutor(prisma, io),
      'data-extraction': new DataExtractionNodeExecutor(prisma, io),
      'default': new DefaultNodeExecutor(prisma, io)
    };
  }

  /**
   * Get an executor for a node type
   * @param {string} nodeType - The type of node
   * @returns {BaseNodeExecutor} - The node executor
   */
  getExecutor(nodeType) {
    return this.executors[nodeType] || this.executors.default;
  }

  /**
   * Register a new executor for a node type
   * @param {string} nodeType - The type of node
   * @param {BaseNodeExecutor} executor - The node executor
   */
  registerExecutor(nodeType, executor) {
    this.executors[nodeType] = executor;
  }
}

module.exports = NodeExecutorFactory;
