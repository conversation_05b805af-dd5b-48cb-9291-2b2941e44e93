const { GoogleGenAI } = require("@google/genai");
require('dotenv').config();

/**
 * Service for interacting with Google's Gemini AI API directly
 */
class GeminiService {
  constructor() {
    // Use the workflow-specific Gemini API key
    this.apiKey = process.env.GEMINI_API_KEY_WORKFLOW;
    this.defaultModel = process.env.GEMINI_DEFAULT_MODEL;
    
    if (!this.apiKey) {
      console.warn('GEMINI_API_KEY_WORKFLOW not found in environment variables. Gemini functionality will be limited.');
      this.ai = null;
    } else {
      console.log('[Gemini Service] Initialized with workflow API key');
      this.ai = new GoogleGenAI({
        apiKey: this.apiKey
      });
    }
  }

  /**
   * Check if a model is a Gemini model
   * @param {string} model - The model name to check
   * @returns {boolean} - Whether the model is a Gemini model
   */
  isGeminiModel(model) {
    if (!model) return false;
    return model.toLowerCase().includes('gemini');
  }

  /**
   * Generate a response from the Gemini AI model
   * @param {string} prompt - The prompt to send to the AI
   * @param {Object} options - Additional options for the API call
   * @returns {Promise<Object>} - The AI response with metadata
   */
  async generateResponse(prompt, options = {}) {
    if (!this.ai) {
      console.warn('Gemini API not initialized. Using mock response.');
      return {
        content: `[Mock Gemini Response] Response to: ${prompt}`,
        model: options.model || this.defaultModel,
        usage: { total_tokens: 0 }
      };
    }

    try {
      const model = options.model || this.defaultModel;
      
      // Map temperature to Gemini's expected range (0-2)
      const temperature = options.temperature !== undefined ? 
        Math.max(0, Math.min(2, options.temperature)) : 1.0;

      // Configure generation parameters
      const generationConfig = {
        temperature: temperature
      };

      // Add maxOutputTokens if specified
      if (options.maxTokens) {
        generationConfig.maxOutputTokens = options.maxTokens;
      }

      console.log(`[Gemini Service] Using model: ${model} with temperature: ${temperature}`);

      const response = await this.ai.models.generateContent({
        model: model,
        contents: prompt,
        generationConfig: generationConfig
      });

      const content = response.text || '';

      // Return in the same format as OpenRouter for consistency
      return {
        content,
        model: model,
        usage: {
          total_tokens: response.usage?.totalTokens || 0,
          prompt_tokens: response.usage?.promptTokens || 0,
          completion_tokens: response.usage?.candidatesTokens || 0
        },
        id: `gemini-${Date.now()}`
      };

    } catch (error) {
      console.error('Error calling Gemini API:', error.message);
      
      // Handle specific Gemini errors
      if (error.message?.includes('API key not valid')) {
        throw new Error('Gemini API key is not valid. Please check your GEMINI_API_KEY environment variable.');
      } else if (error.message?.includes('quota exceeded')) {
        throw new Error('Gemini API quota exceeded. Please check your usage limits.');
      } else if (error.message?.includes('model not found')) {
        throw new Error(`Gemini model "${options.model}" not found. Please check the model name.`);
      }
      
      throw new Error(`Gemini API error: ${error.message}`);
    }
  }

  /**
   * Generate a streaming response from Gemini AI
   * @param {string} prompt - The prompt to send to the AI
   * @param {function} onChunk - Callback for each chunk of the response
   * @param {Object} options - Additional options for the API call
   * @returns {Promise<Object>} - The complete AI response
   */
  async generateStreamingResponse(prompt, onChunk, options = {}) {
    if (!this.ai) {
      console.warn('Gemini API not initialized. Using mock response.');
      const mockResponse = `[Mock Gemini Response] Response to: ${prompt}`;
      onChunk(mockResponse);
      return {
        content: mockResponse,
        model: options.model || this.defaultModel
      };
    }

    try {
      const model = options.model || this.defaultModel;
      
      const temperature = options.temperature !== undefined ? 
        Math.max(0, Math.min(2, options.temperature)) : 1.0;

      const generationConfig = {
        temperature: temperature
      };

      if (options.maxTokens) {
        generationConfig.maxOutputTokens = options.maxTokens;
      }

      console.log(`[Gemini Service] Starting streaming for model: ${model}`);

      // Note: Gemini SDK might not support streaming in the same way as OpenRouter
      // For now, we'll use the regular generateContent and simulate streaming
      const response = await this.ai.models.generateContent({
        model: model,
        contents: prompt,
        generationConfig: generationConfig
      });

      const content = response.text || '';
      
      // Simulate streaming by sending chunks
      const chunkSize = 50;
      for (let i = 0; i < content.length; i += chunkSize) {
        const chunk = content.slice(i, i + chunkSize);
        onChunk(chunk);
        // Small delay to simulate streaming
        await new Promise(resolve => setTimeout(resolve, 10));
      }

      return {
        content,
        model: model,
        usage: {
          total_tokens: response.usage?.totalTokens || 0,
          prompt_tokens: response.usage?.promptTokens || 0,
          completion_tokens: response.usage?.candidatesTokens || 0
        }
      };

    } catch (error) {
      console.error('Error with streaming Gemini API:', error.message);
      throw new Error(`Gemini API streaming error: ${error.message}`);
    }
  }

  /**
   * Get available Gemini models
   * @returns {Array} - List of available Gemini models
   */
  getAvailableModels() {
    return [
      'gemini-2.5-flash',
      'gemini-2.5-pro',
      'gemini-1.5-flash',
      'gemini-1.5-pro',
      'gemini-1.0-pro'
    ];
  }
}

// Create a singleton instance
const geminiService = new GeminiService();

module.exports = geminiService;
