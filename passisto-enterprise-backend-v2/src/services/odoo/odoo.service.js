const { callOdooAPI } = require('@/config/odoo.config');

function formatOdooDatetime(date) {
  const d = new Date(date);
  const pad = (n) => n.toString().padStart(2, '0');
  return (
    d.getFullYear() + '-' +
    pad(d.getMonth() + 1) + '-' +
    pad(d.getDate()) + ' ' +
    pad(d.getHours()) + ':' +
    pad(d.getMinutes()) + ':' +
    pad(d.getSeconds())
  );
}

class OdooService {
  constructor(config) {}

  async getHelpdeskTickets(limit = 100, companyId) {
  const domain = [['active', '=', true]];
  if (companyId) {
    domain.push(['saas_company_id.company_uuid', '=', companyId]);
  }
  console.log("🔍 Domaine Odoo:", domain);

  const result = await callOdooAPI('object', 'execute_kw', [
    'helpdesk.ticket',
    'search_read',
    [domain],
    {
      fields: [
        'id', 'name', 'description', 'stage_id', 'create_date', 'write_date',
        'priority', 'partner_name', 'saas_company_id','category_id','company_uuid'
      ],
      limit
    }
  ]);
  console.log("🔍 Résultat Odoo:", result);
  if (!result) {
    console.error("[❌] Aucun résultat Odoo");
    return [];
  }

  // Adapter les données ici :
  const mapped = result.map(ticket => ({
    id: ticket.id,
    subject: ticket.name,
    description: ticket.description || '',
    status: ticket.stage_id?.[1]?.toLowerCase() || 'nouveau', // exemple
    priority: ticket.priority || 'normale',
    category: ticket.category_id?.[1],
    createdAt: ticket.create_date,
    updatedAt: ticket.write_date || ticket.create_date,
    clientName: ticket.partner_name || '',
    assignedAgent: null, // à compléter si nécessaire
    unreadMessages: 0, // valeur par défaut
  }));

  return mapped;
}


 async createHelpdeskTicket(ticketData) {
  try {
    console.log('🔧 OdooService: Creating ticket with data:', ticketData);

    const result = await callOdooAPI('object', 'execute_kw', [
      'helpdesk.ticket',
      'create',
      [ticketData]
    ]);
    
    console.log('📦 OdooService result:', result);
    return result;
  } catch (err) {
    console.error('❌ Error while creating ticket:', err);
    throw err;
  }
}

async createAttachment({ name, datas, res_model, res_id, mimetype }) {
  try {
    const vals = {
      name,
      datas,
      res_model,
      res_id,
      mimetype,
      type: 'binary',
    };

    const result = await callOdooAPI('object', 'execute_kw', [
      'ir.attachment',
      'create',
      [vals]
    ]);

    console.log('📎 Pièce jointe créée avec ID:', result);
    return result;
  } catch (err) {
    console.error('❌ Erreur lors de la création de la pièce jointe :', err);
    throw err;
  }
}



async getHelpdeskTicketById(id, companyUuid) {
  const domain = [['id', '=', id]];
  if (companyUuid) {
    domain.push(['saas_company_id.company_uuid', '=', companyUuid]);
  }

  const ticketResult = await callOdooAPI('object', 'execute_kw', [
    'helpdesk.ticket',
    'search_read',
    [domain],
    {
      fields: [
        'id', 'name', 'description', 'stage_id', 'create_date',
        'user_id', 'partner_id', 'team_id', 'creator_company_id',
        'saas_company_id', 'category_id','partner_name',
        'attachment_ids'
      ]
    }
  ]);

  const ticket = ticketResult?.[0] || null;
  if (!ticket) return null;

  if (ticket.attachment_ids && ticket.attachment_ids.length > 0) {
    // 2) récupérer les infos détaillées des attachments
    const attachments = await callOdooAPI('object', 'execute_kw', [
      'ir.attachment',
      'search_read',
      [[['id', 'in', ticket.attachment_ids]]],
      {
        fields: ['id', 'name', 'mimetype', 'datas', 'create_date'],
      }
    ]);
    ticket.attachments = attachments;
  } else {
    ticket.attachments = [];
  }

  return ticket;
}


  // async updateHelpdeskTicket(id, updateData, companyId) {
  //   // Vérifier que le ticket appartient bien à cette entreprise
  //   const ticket = await this.getHelpdeskTicketById(id, companyId);
  //   if (!ticket) {
  //     return false;
  //   }

  //   const result = await callOdooAPI('object', 'execute_kw', [
  //     'helpdesk.ticket',
  //     'write',
  //     [[id], updateData]
  //   ]);

  //   return result;
  // }

async updateHelpdeskTicket(id, updateData, companyId, files = [], existingFileIds = []) {
  // Vérifier que le ticket appartient bien à cette entreprise
  const ticket = await this.getHelpdeskTicketById(id, companyId);
  if (!ticket) {
    return false;
  }

  // 1. Mettre à jour les données du ticket
  const result = await callOdooAPI('object', 'execute_kw', [
    'helpdesk.ticket',
    'write',
    [[id], updateData]
  ]);

  // 2. Gérer les pièces jointes existantes
  if (ticket.attachment_ids && ticket.attachment_ids.length > 0) {
    // Supprimer les fichiers qui ne sont plus dans existingFileIds
    const filesToDelete = ticket.attachment_ids.filter(
      attachmentId => !existingFileIds.includes(attachmentId)
    );
    
    if (filesToDelete.length > 0) {
      await callOdooAPI('object', 'execute_kw', [
        'ir.attachment',
        'unlink',
        [filesToDelete]
      ]);
    }
  }

  // 3. Ajouter les nouveaux fichiers
  if (files && files.length > 0) {
    for (const file of files) {
      await this.createAttachment({
        name: file.originalname,
        datas: file.buffer.toString('base64'),
        res_model: 'helpdesk.ticket',
        res_id: id,
        mimetype: file.mimetype
      });
    }
  }

  return result;
}
  async deleteHelpdeskTicket(id, companyId) {
    // Vérifier que le ticket appartient bien à cette entreprise
    const ticket = await this.getHelpdeskTicketById(id, companyId);
    if (!ticket) {
      return false;
    }

    const result = await callOdooAPI('object', 'execute_kw', [
      'helpdesk.ticket',
      'unlink',
      [[id]]
    ]);

    return result;
  }

  async getCategories(limit = 100) {
    const domain = [['active', '=', true]];

    return await callOdooAPI('object', 'execute_kw', [
      'helpdesk.ticket.category',
      'search_read',
      [domain],
      {
        fields: [
          'id', 'name', 'complete_name', 'parent_id', 'child_id', 
          'sequence', 'company_id', 'create_date'
        ],
        limit,
        order: 'sequence, id'
      }
    ]);
  }

  async getSaasCompanyIdByUuid(uuid) {
    const result = await callOdooAPI('object', 'execute_kw', [
      'saas.company',
      'search',
      [[['company_uuid', '=', uuid]]],
      { limit: 1 }
    ]);
    return result.length ? result[0] : null;
  }

  


async getTicketMessages(ticketId) {
  try {
    // 1️⃣ Récupérer tous les messages
    const messages = await callOdooAPI('object', 'execute_kw', [
      'helpdesk.ticket.message',
      'search_read',
      [[['ticket_id', '=', ticketId]]],
      {
        fields: ['id', 'partner_name', 'author_type', 'content', 'message_date', 'attachment_ids'],
        order: 'message_date asc'
      }
    ]);

    if (!messages || messages.length === 0) return [];

    // 2️⃣ Récupérer tous les IDs d'attachments en une seule requête
    const allAttachmentIds = messages.flatMap(m => m.attachment_ids || []);
    let attachments = [];
    if (allAttachmentIds.length > 0) {
      attachments = await callOdooAPI('object', 'execute_kw', [
        'ir.attachment',
        'search_read',
        [[['id', 'in', allAttachmentIds]]],
        { fields: ['id', 'name', 'mimetype', 'datas', 'store_fname'] }
      ]);
    }

    // 3️⃣ Construire un mapping attachmentId → attachment
    const attachmentMap = {};
    attachments.forEach(att => {
      attachmentMap[att.id] = att;
    });

    // 4️⃣ Construire le résultat final
    return messages.map(msg => ({
      id: msg.id,
      author: msg.partner_name || (msg.author_type === 'client' ? 'Client' : 'Agent'),
      authorType: msg.author_type || 'Unknown',
      content: msg.content || '',
      date: msg.message_date,
      attachments: (msg.attachment_ids || []).map(id => attachmentMap[id] ? {
        id: attachmentMap[id].id,
        name: attachmentMap[id].name,
        mimetype: attachmentMap[id].mimetype,
        data: attachmentMap[id].datas || null,
        filename: attachmentMap[id].store_fname || null
      } : null).filter(a => a !== null)
    }));

  } catch (err) {
    console.error('❌ Error fetching ticket messages:', err);
    throw err;
  }
}








async postTicketMessage(ticketId, content, authorType = 'client', authorName = '', attachments = []) {
  try {
    // 1️⃣ Récupérer le ticket
    const tickets = await callOdooAPI('object', 'execute_kw', [
      'helpdesk.ticket',
      'read',
      [[ticketId], ['partner_id', 'partner_name']]
    ]);

    if (!tickets || tickets.length === 0) throw new Error('Ticket not found');

    const ticket = tickets[0];
    const partnerId = ticket.partner_id?.[0];
    const partnerName = ticket.partner_name || authorName || (authorType === 'client' ? 'Client' : 'Agent');

    // 2️⃣ Préparer le message
    const messageData = {
      ticket_id: ticketId,
      content,
      author_type: authorType,
      message_date: formatOdooDatetime(new Date()),
      partner_name: partnerName, // toujours défini
    };

    if (partnerId) {
      messageData.author_id = partnerId; // Odoo utilise author_id pour le nom
    }

    // 3️⃣ Créer le message
    const messageId = await callOdooAPI('object', 'execute_kw', [
      'helpdesk.ticket.message',
      'create',
      [messageData]
    ]);

    const attachmentNames = [];

    // 4️⃣ Créer les attachments et les lier au message
    for (const file of attachments) {
      const base64Data = file.buffer.toString('base64');

      const attachmentId = await callOdooAPI('object', 'execute_kw', [
        'ir.attachment',
        'create',
        [{
          name: file.originalname,
          datas: base64Data,
          res_model: 'helpdesk.ticket.message',
          res_id: messageId,
          type: 'binary',
          mimetype: file.mimetype
        }]
      ]);

      // Lier l’attachment au champ Many2many du message
      await callOdooAPI('object', 'execute_kw', [
        'helpdesk.ticket.message',
        'write',
        [[messageId], { attachment_ids: [[4, attachmentId]] }]
      ]);

      attachmentNames.push(file.originalname);
    }

    // 5️⃣ Retourner le résultat
    return {
      success: true,
      message: "Message posted successfully",
      messageId: {
        id: messageId,
        content,
        author: partnerName,
        authorType,
        date: messageData.message_date,
        attachments: attachmentNames
      }
    };

  } catch (err) {
    console.error('❌ Error posting ticket message:', err);
    throw err;
  }
}











}



module.exports = OdooService;


