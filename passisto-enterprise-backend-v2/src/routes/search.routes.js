const express = require("express");
const checkPermission = require("@/middlewares/permMiddleware");
const { authMiddleware } = require("@/middlewares/authMiddleware");
const checkFeatureQuota = require("@/middlewares/checkFeatureQuota");
const { performSearch } = require("@/controllers/search.controller");
const validateRequest = require("@/middlewares/search-chatbotMiddleware")
const  { searchSchema } = require('@/middlewares/schema/chatbot-search.schema')

const router = express.Router();
router.use(authMiddleware)
// router.use((req, res, next)=>{
//     userId = req.headers['userid'];
//     companyId = req.headers['companyid']
//     req.user = { userId };
//     req.company = { companyId }
//     next();
//   });

// Define the search route with permission checks and usage tracking
router.post("/",
  checkPermission(["CAN_SEARCH"]),
  checkFeatureQuota("maxSearchQueries", "searchQueriesUsed"),
  validateRequest(searchSchema),
  performSearch
);

module.exports = router;