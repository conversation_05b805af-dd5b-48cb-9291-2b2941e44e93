const express = require("express");
const { updateSubscriptionFeature, cancelSubscription } = require('@/controllers/admin/subscription.panel.controller.js');
const verifyAdminToken  = require('@/middlewares/adminMiddleware.js');

const adminRouter = express.Router();

adminRouter.put('/edit-subscription-feature', verifyAdminToken, updateSubscriptionFeature);
adminRouter.delete('/cancel-subscription', verifyAdminToken, cancelSubscription);


module.exports = adminRouter ;