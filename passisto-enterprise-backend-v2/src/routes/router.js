// create router 
const express = require('express');
const authRouter = require('./auth.routes');
const userRouter = require('./user.routes');
const formBuilderRouter = require('./form-builder.routes');
const emailBuilderRouter = require('./email-builder.routes');
const integrationRouter = require('./integrations.routes');
const aiInterviewAgent = require('./ai-interview-agent.routes');
const stripRouter = require('./stripe.routes');
const companyRouter = require('./company.routes');
const odooRoutes = require('./odoo.routes');

const rolePermissionRouter = require('./role-permission.routes');
const groupRouter = require('./group.routes');

const searchRoutes = require("./search.routes");
const chatbotRoutes = require("./chatbot.routes");
const workflowsRouter = require('./workflows/index');

const dashboardRoutes = require("./dashboard.routes");

const demoRequestRouter = require("./demo-request.routes");

const adminRouter = require("./admin.routes");

const router = express.Router();
router.use('/auth', authRouter)
router.use('/users', userRouter)
router.use('/form-builder', formBuilderRouter)
router.use('/email-builder', emailBuilderRouter)
router.use('/interview-agent', aiInterviewAgent)
router.use('/stripe', stripRouter)
router.use('/company', companyRouter)
router.use('/role-permission', rolePermissionRouter)
router.use('/groups', groupRouter)
router.use('/integrations', integrationRouter)
router.use("/search", searchRoutes);
router.use("/chatbot", chatbotRoutes);
router.use('/api/odoo', odooRoutes);

router.use('/workflows-api', workflowsRouter);


// dashboard stats
router.use('/dashboard', dashboardRoutes)

router.use("/contact", demoRequestRouter)

router.use("/admin", adminRouter)



module.exports = router;
