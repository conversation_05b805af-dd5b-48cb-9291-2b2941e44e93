const express = require('express');
const router = express.Router();
const { authMiddleware } = require('../middlewares/authMiddleware');
//  const { mockAuthMiddleware } = require('../middlewares/mockAuthMiddleware');
const { 
  getHelpdeskTickets, 
  createHelpdeskTicket,
  getHelpdeskTicketById,
  updateHelpdeskTicket,
  deleteHelpdeskTicket,
  getCategories,
  getTicketMessages,
  postTicketMessage
} = require('@/controllers/odoo/helpdesk.controller');
const upload = require('../middlewares/multer-odoo');

// Appliquer le middleware d'authentification
// router.use(authMiddleware);
// router.use(mockAuthMiddleware);

// Odoo Helpdesk routes
router.get('/helpdesk/tickets',authMiddleware,getHelpdeskTickets);
router.post('/helpdesk/tickets',authMiddleware,upload.array('files'), createHelpdeskTicket);
router.get('/helpdesk/tickets/:id',authMiddleware, getHelpdeskTicketById);
router.put('/helpdesk/tickets/:id',authMiddleware,upload.array('files') ,updateHelpdeskTicket);
router.delete('/helpdesk/tickets/:id',authMiddleware, deleteHelpdeskTicket);
router.get('/helpdesk/categories', getCategories);
router.get('/helpdesk/tickets/:id/messages', authMiddleware, getTicketMessages);
// router.post('/helpdesk/tickets/:id/messages', authMiddleware, postTicketMessage);
router.post(
  '/helpdesk/tickets/:id/messages',
  authMiddleware,
  upload.array('attachments'), // support multiple files
  postTicketMessage
);


module.exports = router;

