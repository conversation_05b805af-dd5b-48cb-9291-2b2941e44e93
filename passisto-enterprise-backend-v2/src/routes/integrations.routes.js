const express = require("express");
const { validateIntegration, verifyStorageQuota } = require("@/middlewares/integrationMiddleware");
const checkPermission = require("@/middlewares/permMiddleware");
const {
  getIntegrations,
  getIntegrationById,
  getIntegrationMetrics,
  getIntegrationsByGroup,
  attachIntegrationToGroup,
  removeIntegrationFromGroup,
  getResourceUsage
} = require("@/controllers/integrations/integration.controller");
const {
  createFtpIntegration,
  getFtpIntegrations,
  deleteFtpIntegration,
  updateFtpIntegration,
  retryFtpIntegration,
} = require("@/controllers/integrations/ftp.controller");
const {
  createJiraIntegration,
  getJiraIntegrations,
  deleteJiraIntegration,
  updateJiraIntegration,
  retryJiraIntegration,
} = require("@/controllers/integrations/jira.controller");
const {
  createWebIntegration,
  getWebIntegrations,
  deleteWebIntegration,
  updateWebIntegration,
} = require("@/controllers/integrations/web.controller");

const { authMiddleware } = require("@/middlewares/authMiddleware");

const router = express.Router();

router.use(authMiddleware);
// router.use((req, res, next)=>{
//   userId = req.headers['userid'];
//   companyId = req.headers['companyid']
//   req.user = { userId };
//   console.log(req.user)
//   req.company = { companyId }
//   next();
// });
const CAN_VIEW_INTEGRATION = ["CAN_VIEW_INTEGRATION", "CAN_MANAGE_INTEGRATIONS", "CAN_CREATE_FTP", "CAN_CREATE_JIRA", "CAN_CREATE_WEB", "CAN_UPDATE_FTP", "CAN_UPDATE_JIRA", "CAN_UPDATE_WEB", "CAN_DELETE_FTP", "CAN_DELETE_JIRA", "CAN_DELETE_WEB", "CAN_ASK_CHATBOT", "CAN_SEARCH"];
// General integrations routes
router.get("/", getIntegrations);
router.get('/group', checkPermission(CAN_VIEW_INTEGRATION), getIntegrationsByGroup);
router.get(
  "/integration/:id",
  checkPermission(CAN_VIEW_INTEGRATION),
  getIntegrationById
);
router.get(
  "/metrics/",
  checkPermission(CAN_VIEW_INTEGRATION),
  getIntegrationMetrics
);
router.post(
  "/add-integration-group",
  checkPermission([
    "CAN_ASSIGN_INTEGRATION_TO_GROUP",
    "CAN_MANAGE_INTEGRATIONS",
  ]),
  attachIntegrationToGroup
);
router.post(
  "/remove-integration-group",
  checkPermission([
    "CAN_REMOVE_INTEGRATION_FROM_GROUP",
    "CAN_MANAGE_INTEGRATIONS",
  ]),
  removeIntegrationFromGroup
);

// FTP integration routes
router.get(
  "/ftp/",
  checkPermission(["CAN_VIEW_INTEGRATION", "CAN_MANAGE_INTEGRATIONS"]),
  getFtpIntegrations
);
router.post(
  "/ftp",
  verifyStorageQuota,
  validateIntegration,
  checkPermission(["CAN_CREATE_FTP", "CAN_MANAGE_INTEGRATIONS"]),
  createFtpIntegration
);
router.put(
  "/ftp/:id",
  validateIntegration,
  checkPermission(["CAN_UPDATE_FTP", "CAN_MANAGE_INTEGRATIONS"]),
  updateFtpIntegration
);
router.delete(
  "/ftp/:id",
  checkPermission(["CAN_DELETE_FTP", "CAN_MANAGE_INTEGRATIONS"]),
  deleteFtpIntegration
);
router.post(
  "/ftp/:id/retry",
  checkPermission(["CAN_CREATE_FTP", "CAN_MANAGE_INTEGRATIONS"]),
  retryFtpIntegration
);

// Jira integration routes
router.get(
  "/jira/",
  checkPermission(["CAN_VIEW_INTEGRATION", "CAN_MANAGE_INTEGRATIONS"]),
  getJiraIntegrations
);
router.post(
  "/jira",
  verifyStorageQuota,
  validateIntegration,
  checkPermission(["CAN_CREATE_JIRA", "CAN_MANAGE_INTEGRATIONS"]),
  createJiraIntegration
);
router.put(
  "/jira/:id",
  validateIntegration,
  checkPermission(["CAN_UPDATE_JIRA", "CAN_MANAGE_INTEGRATIONS"]),
  updateJiraIntegration
);
router.delete(
  "/jira/:id",
  checkPermission(["CAN_DELETE_JIRA", "CAN_MANAGE_INTEGRATIONS"]),
  deleteJiraIntegration
);
router.post(
  "/jira/:id/retry",
  checkPermission(["CAN_CREATE_JIRA", "CAN_MANAGE_INTEGRATIONS"]),
  retryJiraIntegration
);

// Web integration routes
router.get(
  "/web/",
  checkPermission(["CAN_VIEW_INTEGRATION", "CAN_MANAGE_INTEGRATIONS"]),
  getWebIntegrations
);
router.post(
  "/web",
  verifyStorageQuota,
  validateIntegration,
  checkPermission(["CAN_CREATE_WEB", "CAN_MANAGE_INTEGRATIONS"]),
  createWebIntegration
);
router.put(
  "/web/:id",
  validateIntegration,
  checkPermission(["CAN_UPDATE_WEB", "CAN_MANAGE_INTEGRATIONS"]),
  updateWebIntegration
);
router.delete(
  "/web/:id",
  checkPermission(["CAN_DELETE_WEB", "CAN_MANAGE_INTEGRATIONS"]),
  deleteWebIntegration
);

// Resource usage route
router.get("/resource-usage", checkPermission(CAN_VIEW_INTEGRATION), getResourceUsage);

module.exports = router;
