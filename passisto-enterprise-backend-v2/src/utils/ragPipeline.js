const { Document } = require("@langchain/core/documents");
const { AzureOpenAIEmbeddings } = require("@langchain/openai");
const vectorStore = require("@/config/vectorStorePgVector"); // Updated to use pgvector
const dotenv = require("dotenv");

dotenv.config();

/**
 * Loads documents into the vector store using pgvector.
 * @param {Array<Object>} rawDocuments - Array of plain objects with `text` and `metadata`.
 * @param {string} indexName - Name of the vector index (replaces OpenSearch index).
 * @returns {Promise<Object>} Vector store instance
 */
async function loadDocumentsToVectorStore(rawDocuments, indexName) {
  // Convert raw documents into LangChain Document objects.
  const documents = rawDocuments.map(doc => new Document({
    pageContent: doc.text,
    metadata: doc.metadata
  })); 

  // Get vector store instance for the index
  const vectorStoreInstance = await vectorStore(indexName);
  
  // Add documents to the vector store
  await vectorStoreInstance.addDocuments(documents);
  
  console.log(`Loaded ${documents.length} documents into pgvector index "${indexName}"`);
  return vectorStoreInstance;
}

module.exports = { loadDocumentsToVectorStore };