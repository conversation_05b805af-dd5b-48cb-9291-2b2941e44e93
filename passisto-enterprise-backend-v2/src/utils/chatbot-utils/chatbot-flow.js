const { StateGraph, END } = require('@langchain/langgraph');
const { HumanMessage, AIMessage } = require('@langchain/core/messages');
const { ChatPromptTemplate } = require('@langchain/core/prompts');
const getVectorStore = require('@/config/vectorStore');

// Node functions for the graph
async function retrieveDocuments(state, config) {
    console.log("--- RETRIEVING DOCUMENTS ---");
    try {
      const { alias } = config.configurable;

      // Handle both single collection and multiple collections
      const collections = Array.isArray(alias) ? alias : [alias];
      console.log(`Searching in ${collections.length} collection(s): ${collections.join(', ')}`);

      // Use rephrased question if available, otherwise use original question
      const queryText = state.rephrased_question || state.question;
      console.log(`Query: "${queryText}"`);

      const documentsPerCollection = Math.ceil(10 / collections.length); // Distribute k across collections

      let allDocuments = [];

      // Search all collections in parallel for better performance
      console.log(`  Starting parallel search across ${collections.length} collections...`);
      const searchStartTime = Date.now();

      const searchPromises = collections.map(async (collectionName) => {
        try {
          console.log(`  Searching collection: ${collectionName}`);
          const collectionStartTime = Date.now();

          const vectorStore = await getVectorStore(collectionName);

          // Configure retriever for this collection
          const retriever = vectorStore.asRetriever({
            searchType: "similarity",
            searchKwargs: {
              k: documentsPerCollection,
            },
          });

          const documents = await retriever.invoke(queryText);
          const collectionTime = Date.now() - collectionStartTime;
          console.log(`    Found ${documents.length} documents in ${collectionName} (${collectionTime}ms)`);

          // Add collection info to metadata for tracking
          const documentsWithCollection = documents.map(doc => ({
            ...doc,
            metadata: {
              ...doc.metadata,
              collection: collectionName
            }
          }));

          return {
            collectionName,
            documents: documentsWithCollection,
            success: true,
            searchTime: collectionTime
          };

        } catch (collectionError) {
          console.error(`Error searching collection ${collectionName}:`, collectionError.message);
          return {
            collectionName,
            documents: [],
            success: false,
            error: collectionError.message
          };
        }
      });

      // Wait for all searches to complete
      const searchResults = await Promise.allSettled(searchPromises);
      const totalSearchTime = Date.now() - searchStartTime;

      // Process results and collect documents
      let successfulSearches = 0;
      let totalDocumentsFound = 0;

      searchResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          const searchResult = result.value;
          if (searchResult.success) {
            allDocuments.push(...searchResult.documents);
            successfulSearches++;
            totalDocumentsFound += searchResult.documents.length;
          }
        } else {
          console.error(`Search promise rejected for collection ${collections[index]}:`, result.reason);
        }
      });

      console.log(`  Parallel search completed in ${totalSearchTime}ms`);
      console.log(`  Successful searches: ${successfulSearches}/${collections.length}`);
      console.log(`  Total documents found: ${totalDocumentsFound}`);

      // Sort by relevance score if available, otherwise keep original order
      // Note: LangChain retrievers don't always return scores, so we'll keep the order
      console.log(`Total retrieved: ${allDocuments.length} documents from ${collections.length} collection(s)`);

      // Log document sources for debugging
      if (allDocuments.length > 0) {
        const sources = allDocuments.map(doc => doc.metadata?.source || 'unknown').slice(0, 5);
        const collectionsFound = [...new Set(allDocuments.map(doc => doc.metadata?.collection))];
        console.log(`Sample sources: ${sources.join(', ')}`);
        console.log(`Collections with results: ${collectionsFound.join(', ')}`);
      } else {
        console.log("No documents found across any collections");
      }

      return {
        ...state,
        context: allDocuments
      };
    } catch (error) {
      console.error("Error in retrieveDocuments:", error);
      return {
        ...state,
        error: `Failed to retrieve documents: ${error.message}`
      };
    }
  }
  

  async function rephraseQuestion(state, config) {
    console.log("--- REPHRASING QUESTION ---");
    try {
      // If no chat history, no need to rephrase
      if (!state.messages || state.messages.length === 0) {
        return {
          ...state,
          rephrased_question: state.question
        };
      }
      const { llm } = config.configurable;
      // Format chat history as a string for the system message
      const chatHistoryText = state.messages
        .map(msg => {
          const type = msg.type || (msg._getType ? msg._getType() : 'unknown');
          return `${type}: ${msg.content}`;
        })
        .join('\n');

      // Escape curly braces in chatHistoryText and question to prevent template parsing errors
      const escapedChatHistoryText = chatHistoryText.replace(/\{/g, '{{').replace(/\}/g, '}}');
      const escapedQuestion = state.question.replace(/\{/g, '{{').replace(/\}/g, '}}');

      // Create a custom prompt template instead of using contextualizeQPrompt2
      const contextualizePrompt = await ChatPromptTemplate.fromMessages([
        ["system", `You're are not a chatbot. You're a text processor. Process input by preserving non-questions exactly as given, while reformulating questions into standalone complete queries that maintain the original meaning without context dependencies or references to prior messages.
  ====== Chat History ======
  ${escapedChatHistoryText}
  ====== End Chat History ======
  Note 1: The chat history may be in different languages, but the standalone question should always be in the same language as the input bellow.
  Note 2: Return the bellow input as is if it is not a question.`],
        ["human", `Current Input: ${escapedQuestion}`]
      ]).format({});
  
      const response = await llm.invoke(contextualizePrompt);
      const rephrasedQuestion = response.content.trim();
  
      console.log(`Original: ${state.question}`);
      console.log(`Rephrased: ${rephrasedQuestion}`);
  
      return {
        ...state,
        rephrased_question: rephrasedQuestion
      };
    } catch (error) {
      console.error("Error in rephraseQuestion:", error);
      return {
        ...state,
        rephrased_question: state.question, // Fallback to original question
        error: `Failed to rephrase question: ${error.message}`
      };
    }
  }
  

  async function generateAnswer(state, config) {
    console.log("--- GENERATING ANSWER ---");
    try {
      if (state.error) {
        return state; // Skip if there's an error
      }
      const { llm } = config.configurable;
      // Format context documents
      const contextText = state.context
        .map(doc => doc.pageContent)
        .join('\n\n');
      // Format chat history as a string for the system message
      const chatHistoryText = state.messages.length > 0
        ? state.messages
            .map(msg => {
              const type = msg.type || (msg._getType ? msg._getType() : 'unknown');
              return `${type}: ${msg.content}`;
            })
            .join('\n')
        : "No previous conversation.";

      // Escape curly braces in contextText and chatHistoryText to prevent template parsing errors
      const escapedContextText = contextText.replace(/\{/g, '{{').replace(/\}/g, '}}');
      const escapedChatHistoryText = chatHistoryText.replace(/\{/g, '{{').replace(/\}/g, '}}');
      const escapedQuestion = state.question.replace(/\{/g, '{{').replace(/\}/g, '}}');

      // Create the QA prompt without the MessagesPlaceholder
      const qaPromptFormatted = await ChatPromptTemplate.fromMessages([
        ["system", `You are an AI Assistant for an enterprise environment.
  Use the provided context from enterprise data sources to answer questions accurately and professionally.
  Keep your response **concise** and focused on the enterprise data provided in the context.
  If you don't know the answer based on the provided context, state that clearly instead of guessing.
  Always answer in the **same language** as the question, regardless of the context language.
  ====== START CONTEXT ======
  ${escapedContextText}
  ====== END CONTEXT ======
  ====== Chat History ======
  ${escapedChatHistoryText}
  ====== End Chat History ======
  Note: The context may be in different languages, but the answer should always be in the same language as the question below.
  When referring to enterprise data, be precise and professional in your response.`],
        ["human", `Question: ${escapedQuestion}`]
      ]).format({});
  
      const response = await llm.invoke(qaPromptFormatted);
      const answer = response.content.trim();

      // Extract unique sources with better handling
      const sources = [...new Set(
        state.context
          .map(doc => doc.metadata?.source)
          .filter(source => source && source.trim() !== '')
      )];

      // Log collection distribution for debugging
      const collectionStats = state.context.reduce((acc, doc) => {
        const collection = doc.metadata?.collection || 'unknown';
        acc[collection] = (acc[collection] || 0) + 1;
        return acc;
      }, {});

      console.log(`Generated answer: ${answer}`);
      console.log(`Sources found: ${sources.length > 0 ? sources.join(', ') : 'None'}`);
      console.log(`Documents by collection:`, collectionStats);

      return {
        ...state,
        answer: answer,
        sources: sources
      };
    } catch (error) {
      console.error("Error in generateAnswer:", error);
      return {
        ...state,
        answer: "I apologize, but I encountered an error while generating the answer.",
        error: `Failed to generate answer: ${error.message}`
      };
    }
  }
  

  async function saveToHistory(state, config) {
    console.log("--- SAVING TO HISTORY ---");
    try {
      const { sessionHistory } = config.configurable;
      // Add the user question and bot answer to history using proper message classes
      await sessionHistory.addMessage(new HumanMessage(state.question));
      await sessionHistory.addMessage(new AIMessage(state.answer));
      console.log("Successfully saved to history");
      return state;
    } catch (error) {
      console.error("Error in saveToHistory:", error);
      return {
        ...state,
        error: `Failed to save to history: ${error.message}`
      };
    }
  }
  // Conditional function to decide whether to rephrase
  function shouldRephrase(state) {
    // Rephrase if we have chat history
    return state.messages && state.messages.length > 0 ? "rephrase" : "retrieve";
  }
  
  // Create the graph
  const createChatbotGraph = () => {
    // Define the state schema for StateGraph
    const stateSchema = {
      messages: { default: () => [] },
      question: { default: () => "" },
      context: { default: () => [] },
      rephrased_question: { default: () => "" },
      answer: { default: () => "" },
      sources: { default: () => [] },
      session_id: { default: () => "" },
      user_id: { default: () => "" },
      error: { default: () => null }
    };
  
    const workflow = new StateGraph({
      channels: stateSchema
    });
  
    // Add nodes
    workflow.addNode("rephrase", rephraseQuestion);
    workflow.addNode("retrieve", retrieveDocuments);
    workflow.addNode("generate", generateAnswer);
    workflow.addNode("save_history", saveToHistory);
  
    // Define the flow - use addEdge instead of deprecated setEntryPoint
    workflow.addEdge("__start__", "rephrase");
  
    // Conditional edge: rephrase or go straight to retrieve
    workflow.addConditionalEdges(
      "rephrase",
      shouldRephrase,
      {
        "rephrase": "retrieve",
        "retrieve": "retrieve"
      }
    );
  
    // Linear flow after retrieval
    workflow.addEdge("retrieve", "generate");
    workflow.addEdge("generate", "save_history");
    workflow.addEdge("save_history", END);
  
    return workflow.compile();
  }
  
module.exports = createChatbotGraph;