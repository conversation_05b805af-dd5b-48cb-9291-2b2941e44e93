const { redisClient } = require('@/config/redis');
const crypto = require('crypto');
const pgVectorService = require("@/config/pgvector");

const algorithm = process.env.ENCRYPTION_ALGORITHM;
const secretKey = process.env.ENCRYPTION_SECRET;
const ivLength = 16;

function encrypt(text) {
  const iv = crypto.randomBytes(ivLength);
  const cipher = crypto.createCipheriv(algorithm, Buffer.from(secretKey, 'hex'), iv);
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return iv.toString('hex') + ':' + encrypted;
}


function decrypt(text) {
    const [ivHex, encrypted] = text.split(':');
    const iv = Buffer.from(ivHex, 'hex');
    const decipher = crypto.createDecipheriv(algorithm, Buffer.from(secretKey, 'hex'), iv);
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
}


async function redisDeleteTaskKey(integration){
    try{
        const redisKey = `celery-task-meta-${integration.celeryTaskId}`;
        const deletedCount = await redisClient.del(redisKey);
        console.log(`[INFO] Deleted old Celery task key: ${redisKey} (Deleted: ${deletedCount})`);
    }catch(error){
        console.error(`[ERROR] [${new Date().toISOString()}] failed to clean previous celery task id from REDIS `);
    }
}


async function getIndexSize(indexName) {
  try {
    const indexInfo = await pgVectorService.getIndexSize(indexName);
    
    if (indexInfo) {
      // Estimate size in MB based on document count and average content size
      const estimatedSizeMB = (indexInfo.documentCount * 2) / 1024; // Rough estimate
      console.log(`Index: ${indexName}, Documents: ${indexInfo.documentCount}, Estimated Size: ${estimatedSizeMB.toFixed(2)} MB`);
      return estimatedSizeMB;
    } else {
      throw new Error(`Vector index ${indexName} not found`);
    }
  } catch (error) {
    console.error(`[ERROR] Failed to get index size for ${indexName}:`, error);
    return 0; // Return 0 if there's an error
  }
}

module.exports = {redisDeleteTaskKey, encrypt, decrypt, getIndexSize};

