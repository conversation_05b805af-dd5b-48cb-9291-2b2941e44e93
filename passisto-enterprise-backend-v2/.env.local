# Environment variables declared in this file are automatically made available to <PERSON>risma.
# See the documentation for more detail: https://pris.ly/d/prisma-schema#accessing-environment-variables-from-the-schema

# Prisma supports the native connection string format for PostgreSQL, MySQL, SQLite, SQL Server, MongoDB and CockroachDB.
# See the documentation for all the connection string options: https://pris.ly/d/connection-strings
PORT=1025
LLM_API_BASE_URL=http://chatbot:5921

ENVIRONMENT='DEV'

EMBEDDING_MODEL="text-embedding-3-small"
EMBEDDING_DIMENSION=1024

AZURE_OPENAI_EMBEDDING_API_KEY="BydratmbMYpAW6vLRgGgbkQ47KRFaw3WMwFq8iMSgphmJOChulqdJQQJ99BAACHYHv6XJ3w3AAAAACOGBwEF"
AZURE_OPENAI_EMBEDDING_API_VERSION="2023-05-15"
AZUR_OPENAI_EMBEDDING_INSTANCE_NAME="seffa-m5m9g4ho-eastus2"
AZUR_OPENAI_EMBEDDING_DEPLOYMENT_NAME="text-embedding-3-small"

AZURE_OPENAI_API_INSTANCE_NAME=passisto
AZURE_OPENAI_API_DEPLOYMENT_NAME=gpt-4o-mini
AZURE_OPENAI_API_VERSION=2024-02-01
AZURE_OPENAI_API_KEY=0d16e6ce209344cf9f5c43bc41c1d827

# DB_HOST=pe-db
# DB_HOST=postgres-local-1
DB_HOST=localhost
DB_USER=postgres
DB_PASSWORD=K5EadzuxNRaIGO0
# DB_PORT=5432
DB_PORT=5036
DB_NAME=passisto

RABBITMQ_USER="rabbit"
RABBITMQ_PASS="Fqb3Ita2lsb"

FLOWER_USER="flower"
FLOWER_PASS="Yk6AUPVDEg"

OPENSEARCH_USERNAME="admin"
OPENSEARCH_PASSWORD="Hasdgg52u1"
OPENSEARCH_HOST="http://opensearch-local:9200"

FLOWER_URL="http://flower-local:5555/api/tasks"
DB_NAME=passisto

RABBITMQ_USER="rabbit"
RABBITMQ_PASS="Fqb3Ita2lsb"

FLOWER_USER="flower"
FLOWER_PASS="Yk6AUPVDEg"

OPENSEARCH_USERNAME="admin"
OPENSEARCH_PASSWORD="Hasdgg52u1"
OPENSEARCH_HOST="http://opensearch-local:9200"

FLOWER_URL="http://flower-local:5555/api/tasks"

TURNSTILE_SECRET_KEY="0x4AAAAAABhn46M-GhFLyzFEDgTVDsQK800"

DATABASE_URL="postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}?schema=public"
MONGO_URI="***********************************************"
# DATABASE_URL="************************************************/BACKEND?schema=public"
# MONGO_URI="********************************************************************"
# DATABASE_URL="************************************************/BACKEND?schema=public"
# MONGO_URI="********************************************************************"


# CLERK_PUBLISHABLE_KEY=pk_test_Z3VpZGVkLWdob3N0LTEwLmNsZXJrLmFjY291bnRzLmRldiQ
# CLERK_SECRET_KEY=sk_test_B9J8cqzffJeos6dGOQWiaoWpPeIHNetdY9a8NNenPt

CLERK_PUBLISHABLE_KEY=pk_test_ZGVhci10cmVlZnJvZy00My5jbGVyay5hY2NvdW50cy5kZXYk
CLERK_SECRET_KEY=sk_test_H6vUBF1EgBdq3RNlZ3xhviNEQuTVijE6N2Mt5IswgJ
CLERK_WEBHOOK_SECRET=whsec_GxebvrEcBXut7keLrhvJxcTy8/N9s1Tq
#webhook 2
# CLERK_WEBHOOK_SECRET=whsec_vVHl0Kfqv9G00h6nOutnyd255w2T7Bx+
#webhook 2
# CLERK_WEBHOOK_SECRET=whsec_vVHl0Kfqv9G00h6nOutnyd255w2T7Bx+


EMAIL_HOST = "mail.privateemail.com"
EMAIL_PORT = 465
EMAIL_USER = "<EMAIL>"
EMAIL_PASSWORD = "informatica1E..."
EMAIL_USE_SSL = True
EMAIL_USE_TLS = False
DEFAULT_FROM_EMAIL = "Passisto <<EMAIL>>"

TEAM_NOTIFICATION_EMAIL="<EMAIL>"

FRONTEND_NGINX = http://dev.passisto.com/payment/auth/login
FRONTEND_URL= http://localhost:3030

FRONTEND_NGINX_CHECKOUT_SUCESS = http://localhost:3030/payment/success
FRONTEND_NGINX_CHECKOUT_CANCEL = http://localhost:3030/payment/


STRIPE_SECRET_KEY=sk_test_51PCNs92NnZaAtl1rVJGTPJSEZmGIDUBRQuuQnGTVFgiaM4aeZB110Qa3uNeqzqE8ESkSHiP0Gg3SINDeDFvm69RI00xCbjRx4I
#webhook 1
# STRIPE_WEBHOOK_SECRET=whsec_CZHLZ11dSAVEAJ8ZzYAO6sIERNa0lH88
#webhook 2
STRIPE_WEBHOOK_SECRET=whsec_rjYb26Ji6XOQnJc99EuUoLxVNwrnjxum

TEST=TEST

GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent"
GEMINI_API_KEY_1 = "AIzaSyA-VL6kqfGHXFba8I-JwLe70i--APcXoW4"
GEMINI_API_KEY_2= "AIzaSyBitfPXTo6LVHPxHJlvQKsz8CnN7p07Bdw"
GEMINI_API_KEY_3= "AIzaSyDiPcFIOUMJuBmW0h_t-LFOWYZFqFfPoNw"


RABBITMQ_CELERY_BROKER='amqp://rabbit:Fqb3Ita2lsb@rabbitmq-celery:5672'
REDIS_CELERY_BACKEND='redis://redis-celery:6379'


OPENSEARCH_USERNAME="admin"
OPENSEARCH_PASSWORD="Hasdgg52u1"
OPENSEARCH_HOST="http://opensearch:9200"


ENCRYPTION_SECRET="c8a868008eeca30a99e4b021a9b32b6656325c679e4d380d0094002bbdcce453"
ENCRYPTION_ALGORITHM='aes-256-cbc'


SCRAPYD_SERVER="http://scrapyd-server:6800"
SCRAPYD_USERNAME="yS2RsEFBrnDiG4CUvoX7b"
SCRAPYD_PASSWORD="S32vNKQlv8hgKiDD2Vxq0qbOeUVfvLdKNsHCnbrK3xLTzgWjPV"
SCRAPYD_PROJECT="website_scraper"
SCRAPYD_SPIDER="pe-web-worker"


FLOWER_URL="http://passisto-flower:5555/api/tasks"

RABBITMQ_CELERY_BROKER='amqp://rabbit:Fqb3Ita2lsb@rabbitmq-celery:5672'
REDIS_CELERY_BACKEND='redis://redis-celery:6379'


OPENSEARCH_USERNAME="admin"
OPENSEARCH_PASSWORD="Hasdgg52u1"
OPENSEARCH_HOST="http://opensearch:9200"


ENCRYPTION_SECRET="c8a868008eeca30a99e4b021a9b32b6656325c679e4d380d0094002bbdcce453"
ENCRYPTION_ALGORITHM='aes-256-cbc'


SCRAPYD_SERVER="http://scrapyd-server:6800"
SCRAPYD_USERNAME="yS2RsEFBrnDiG4CUvoX7b"
SCRAPYD_PASSWORD="S32vNKQlv8hgKiDD2Vxq0qbOeUVfvLdKNsHCnbrK3xLTzgWjPV"
SCRAPYD_PROJECT="website_scraper"
SCRAPYD_SPIDER="pe-web-worker"


FLOWER_URL="http://passisto-flower:5555/api/tasks"


# MONGODB_URI=mongodb://localhost:27017/workflow-app 

# OpenRouter AI API
OPENROUTER_API_KEY="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"
OPENROUTER_DEFAULT_MODEL="gemini-2.0-flash"
OPENROUTER_MAX_TOKENS=8000


SMTP_HOST="sandbox.smtp.mailtrap.io"
SMTP_PORT=2525
SMTP_USER="975f584e7c516d"
SMTP_PASS="4cd8e9ddf44dfb"
SMTP_SECURE=false
SMTP_FROM="Workflow System <<EMAIL>>"


# Rev.ai API
REVAI_ACCESS_TOKEN="02abtbWJIJCtEQGAlWr6c9vFYlyEGI_ctVQ4X0HMr0GXgG2AGbFZtyjXg8G63Lj3rWnU5t_XJnZnshhGDT8enZLYKNvnY"


AZURE_SPEECH_KEY="BydratmbMYpAW6vLRgGgbkQ47KRFaw3WMwFq8iMSgphmJOChulqdJQQJ99BAACHYHv6XJ3w3AAAAACOGBwEF"
AZURE_SPEECH_REGION="eastus2"
AZURE_SPEECH_ENDPOINT="https://seffa-m5m9g4ho-eastus2.cognitiveservices.azure.com/openai/deployments/whisper/audio/translations?api-version=2024-06-01"