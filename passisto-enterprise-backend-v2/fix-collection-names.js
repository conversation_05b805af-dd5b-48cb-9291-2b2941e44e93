#!/usr/bin/env node
/**
 * <PERSON>ript to fix collection names that have extra curly braces
 */

require('module-alias/register');
require('dotenv').config();

const { Pool } = require('pg');

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.PG_SSL === "true" ? { rejectUnauthorized: false } : undefined,
});

async function fixCollectionNames() {
  console.log('🔧 Fixing collection names with extra braces...\n');
  
  const client = await pool.connect();
  
  try {
    // Get all collections with problematic names
    const collectionsResult = await client.query(`
      SELECT name, uuid 
      FROM langchain_pg_collection 
      WHERE name LIKE '{%}' OR name LIKE '"%"'
    `);
    
    console.log(`Found ${collectionsResult.rows.length} collections with problematic names:`);
    
    for (const collection of collectionsResult.rows) {
      const oldName = collection.name;
      const uuid = collection.uuid;
      
      // Remove extra braces and quotes
      let newName = oldName;
      
      // Remove outer curly braces
      if (newName.startsWith('{') && newName.endsWith('}')) {
        newName = newName.slice(1, -1);
      }
      
      // Remove outer quotes
      if (newName.startsWith('"') && newName.endsWith('"')) {
        newName = newName.slice(1, -1);
      }
      
      console.log(`  📝 "${oldName}" → "${newName}"`);
      
      if (oldName !== newName) {
        // Update the collection name
        await client.query(
          'UPDATE langchain_pg_collection SET name = $1 WHERE uuid = $2',
          [newName, uuid]
        );
        console.log(`     ✅ Updated collection ${uuid}`);
      } else {
        console.log(`     ⏭️  No change needed`);
      }
    }
    
    // Verify the fix
    console.log('\n🔍 Verification - Current collection names:');
    const verifyResult = await client.query('SELECT name, uuid FROM langchain_pg_collection ORDER BY name');
    verifyResult.rows.forEach(row => {
      console.log(`  ${row.name} (${row.uuid})`);
    });
    
    console.log('\n✅ Collection name fix completed!');
    
  } catch (error) {
    console.error('❌ Fix failed:', error);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

// Run fix
if (require.main === module) {
  fixCollectionNames()
    .then(() => {
      console.log('✅ Collection name fix completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Collection name fix failed:', error);
      process.exit(1);
    });
}

module.exports = fixCollectionNames;
