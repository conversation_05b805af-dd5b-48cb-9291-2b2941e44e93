#!/usr/bin/env node
/**
 * Migration script to move data from individual collection tables to standard LangChain schema
 */

require('module-alias/register');
require('dotenv').config();

const { Pool } = require('pg');

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.PG_SSL === "true" ? { rejectUnauthorized: false } : undefined,
});

async function migrateVectorData() {
  console.log('🚀 Starting vector data migration...\n');
  
  const client = await pool.connect();
  
  try {
    // Get all individual collection tables
    const tablesResult = await client.query(`
      SELECT tablename 
      FROM pg_tables 
      WHERE schemaname = 'public' 
      AND tablename LIKE 'web-%'
      AND tablename != 'langchain_pg_embedding'
      AND tablename != 'langchain_pg_collection'
    `);
    
    console.log(`Found ${tablesResult.rows.length} individual collection tables to migrate:`);
    tablesResult.rows.forEach(row => console.log(`  - ${row.tablename}`));
    console.log('');
    
    for (const tableRow of tablesResult.rows) {
      const tableName = tableRow.tablename;
      console.log(`📦 Migrating table: ${tableName}`);
      
      try {
        // Check if collection already exists in langchain_pg_collection
        const collectionResult = await client.query(
          'SELECT uuid FROM langchain_pg_collection WHERE name = $1',
          [tableName]
        );
        
        let collectionId;
        if (collectionResult.rows.length > 0) {
          collectionId = collectionResult.rows[0].uuid;
          console.log(`   ✓ Found existing collection with ID: ${collectionId}`);
        } else {
          // Create collection
          const newCollectionResult = await client.query(
            'INSERT INTO langchain_pg_collection (name, cmetadata, uuid) VALUES ($1, $2, gen_random_uuid()) RETURNING uuid',
            [tableName, '{}']
          );
          collectionId = newCollectionResult.rows[0].uuid;
          console.log(`   ✓ Created new collection with ID: ${collectionId}`);
        }
        
        // Check how many documents are in the individual table
        const countResult = await client.query(`SELECT COUNT(*) FROM "${tableName}"`);
        const docCount = parseInt(countResult.rows[0].count);
        console.log(`   📄 Found ${docCount} documents to migrate`);
        
        if (docCount === 0) {
          console.log(`   ⏭️  Skipping empty table\n`);
          continue;
        }
        
        // Check if documents already exist in langchain_pg_embedding for this collection
        const existingDocsResult = await client.query(
          'SELECT COUNT(*) FROM langchain_pg_embedding WHERE collection_id = $1',
          [collectionId]
        );
        const existingCount = parseInt(existingDocsResult.rows[0].count);
        
        if (existingCount > 0) {
          console.log(`   ⚠️  Collection already has ${existingCount} documents in langchain_pg_embedding`);
          console.log(`   🔄 Clearing existing documents before migration...`);
          await client.query(
            'DELETE FROM langchain_pg_embedding WHERE collection_id = $1',
            [collectionId]
          );
        }
        
        // Migrate documents from individual table to langchain_pg_embedding
        const migrateQuery = `
          INSERT INTO langchain_pg_embedding (text, metadata, embedding, collection_id)
          SELECT 
            content as text,
            langchain_metadata::jsonb as metadata,
            embedding,
            $1 as collection_id
          FROM "${tableName}"
        `;
        
        const migrateResult = await client.query(migrateQuery, [collectionId]);
        console.log(`   ✅ Migrated ${migrateResult.rowCount} documents`);
        
        // Verify migration
        const verifyResult = await client.query(
          'SELECT COUNT(*) FROM langchain_pg_embedding WHERE collection_id = $1',
          [collectionId]
        );
        const migratedCount = parseInt(verifyResult.rows[0].count);
        console.log(`   🔍 Verification: ${migratedCount} documents now in langchain_pg_embedding`);
        
        if (migratedCount !== docCount) {
          console.log(`   ❌ Migration count mismatch! Expected ${docCount}, got ${migratedCount}`);
        } else {
          console.log(`   ✅ Migration successful!\n`);
        }
        
      } catch (error) {
        console.error(`   ❌ Error migrating table ${tableName}:`, error.message);
      }
    }
    
    // Final summary
    console.log('📊 Migration Summary:');
    const finalCollectionsResult = await client.query(`
      SELECT 
        c.name,
        COUNT(e.id) as doc_count
      FROM langchain_pg_collection c
      LEFT JOIN langchain_pg_embedding e ON c.uuid = e.collection_id
      GROUP BY c.name, c.uuid
      ORDER BY c.name
    `);
    
    console.log('Collections in langchain_pg_embedding:');
    finalCollectionsResult.rows.forEach(row => {
      console.log(`  ${row.name}: ${row.doc_count} documents`);
    });
    
    console.log('\n🎉 Migration completed!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

// Run migration
if (require.main === module) {
  migrateVectorData()
    .then(() => {
      console.log('✅ Migration script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Migration script failed:', error);
      process.exit(1);
    });
}

module.exports = migrateVectorData;
