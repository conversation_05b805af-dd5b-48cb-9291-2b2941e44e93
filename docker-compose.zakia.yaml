
services:

  passisto-enterprise-frontend-v2:
    container_name: passisto-enterprise-frontend-v2
    restart: always
    build:
      context: ./passisto-enterprise-frontend-v2
      dockerfile: Dockerfile.zakia.development
    volumes:
      - ./passisto-enterprise-frontend-v2/src:/app/src:Z
      - ./passisto-enterprise-frontend-v2/messages:/app/messages:Z
      - ./passisto-enterprise-frontend-v2/.env.zakia.local:/app/.env:Z
    # env_file:
    #   - ./passisto-enterprise-frontend-v2/.env.zakia.local
    environment:
      - PORT=3030
    ports:
      - 3030:3030
    command: npx next dev 
    networks:
      - pe-net  
  
  passisto-enterprise-backend-v2:
    container_name: passisto-enterprise-backend-v2
    build:
      context: ./passisto-enterprise-backend-v2
      dockerfile: Dockerfile
    env_file:
      - ./passisto-enterprise-backend-v2/.env.zakia
    environment:
      - PORT=5000
    ports:
      - "5000:5000"
      # - ********:5000:5000
    restart: always
    depends_on:
      - pe-db

    volumes:
      - ./passisto-enterprise-backend-v2/src:/app/src:Z
      - ./passisto-enterprise-backend-v2/prisma:/app/prisma:Z
      # - ./passisto-enterprise-backend-v2/package.json:/app/package.json:Z
      - ./passisto-enterprise-backend-v2/scripts:/app/scripts:Z
    tty: true
    networks: 
      - pe-net
  
  pe-db:
    image: cleisonfmelo/postgres-pg-cron
    container_name: pe-db
    ports:
      - 5932:5432
    restart: always
    volumes:
      - pgdata:/var/lib/postgresql/data
    environment:
      - POSTGRES_PASSWORD=K5EadzuxNRaIGO0
      - POSTGRES_USER=postgres
      - POSTGRES_DB=passisto
    networks:
      - pe-net
  nginx:
    image: nginx:latest
    container_name: nginx
    restart: always
    ports:
      - "80:80"
    volumes:
      - ./nginx/default.conf:/etc/nginx/conf.d/default.conf:Z
    networks:
      - pe-net


networks:
  pe-net:
    external: true

volumes:
  pgdata:

