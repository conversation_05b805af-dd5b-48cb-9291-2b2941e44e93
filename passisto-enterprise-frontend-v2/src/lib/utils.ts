import { Role } from "@/store/slices/rolePermissionSlice";
import { Team } from "@/store/slices/teamSlice";
import { User } from "@/store/slices/userSlice";
import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function hasPermission(
  permissions: string[],
  required: string | string[]
) {
  if (!permissions) return false;
  if (Array.isArray(required)) {
    return required.some((p) => permissions.includes(p));
  }
  return permissions.includes(required);
}

export function userHasPermission(
  permissions: string[],
  requiredPermission: string | string[]
): boolean {
  return hasPermission(permissions, requiredPermission);
}





// Interview Phase Types and Interfaces
export type InterviewPhaseType =
  | "screening"
  | "technical"
  | "behavioral"
  | "cultural-fit"
  | "leadership"
  | "final-round"
  | "post-offer"
  | "custom"

export type InterviewPhaseStatus = "not-started" | "in-progress" | "completed" | "skipped"

export interface InterviewPhase {
  id: string
  name: string
  type: InterviewPhaseType
  description?: string
  order: number
  isRequired: boolean
  estimatedDuration: number // in minutes
  startDate?: string // ISO date string
  endDate?: string // ISO date string
  template?: {
    id: string
    name: string
    questionCount: number
  }
  configuration: {
    allowSkip: boolean
    requiresApproval: boolean
    autoAdvance: boolean
    passingScore?: number
    maxAttempts?: number
  }
  status: InterviewPhaseStatus
  createdAt: string
  updatedAt: string
}

export interface InterviewPhaseConfiguration {
  projectId: string
  phases: InterviewPhase[]
  totalEstimatedDuration: number
  createdAt: string
  updatedAt: string
}

export interface CandidatePhaseProgress {
  candidateId: string
  phaseId: string
  status: InterviewPhaseStatus
  score?: number
  feedback?: string
  startedAt?: string
  completedAt?: string
  attempts: number
  notes?: string
}

export interface CandidateInterviewProgress {
  candidateId: string
  projectId: string
  phases: CandidatePhaseProgress[]
  currentPhaseId?: string
  overallStatus: "not-started" | "in-progress" | "completed" | "rejected" | "accepted"
  totalScore?: number
  createdAt: string
  updatedAt: string
}

// Default phase templates
export const DEFAULT_INTERVIEW_PHASES: Omit<InterviewPhase, 'id' | 'createdAt' | 'updatedAt' | 'status'>[] = [
  {
    name: "Initial Screening",
    type: "screening",
    description: "Basic qualification and interest assessment",
    order: 1,
    isRequired: true,
    estimatedDuration: 30,
    configuration: {
      allowSkip: false,
      requiresApproval: false,
      autoAdvance: true,
      passingScore: 60,
      maxAttempts: 1
    }
  },
  {
    name: "Technical Assessment",
    type: "technical",
    description: "Evaluate technical skills and problem-solving abilities",
    order: 2,
    isRequired: true,
    estimatedDuration: 60,
    configuration: {
      allowSkip: false,
      requiresApproval: true,
      autoAdvance: false,
      passingScore: 70,
      maxAttempts: 1
    }
  },
  {
    name: "Behavioral Interview",
    type: "behavioral",
    description: "Assess soft skills, communication, and cultural fit",
    order: 3,
    isRequired: true,
    estimatedDuration: 45,
    configuration: {
      allowSkip: false,
      requiresApproval: true,
      autoAdvance: false,
      passingScore: 65,
      maxAttempts: 1
    }
  },
  {
    name: "Final Round",
    type: "final-round",
    description: "Final decision-making interview with senior stakeholders",
    order: 4,
    isRequired: true,
    estimatedDuration: 60,
    configuration: {
      allowSkip: false,
      requiresApproval: true,
      autoAdvance: false,
      passingScore: 75,
      maxAttempts: 1
    }
  },
  {
    name: "Post-Offer Discussion",
    type: "post-offer",
    description: "Discuss offer details, expectations, and onboarding",
    order: 5,
    isRequired: false,
    estimatedDuration: 30,
    configuration: {
      allowSkip: true,
      requiresApproval: false,
      autoAdvance: true,
      maxAttempts: 2
    }
  }
]

// Helper functions
export function generatePhaseId(): string {
  return `phase-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

export function calculateTotalDuration(phases: InterviewPhase[]): number {
  return phases.reduce((total, phase) => total + phase.estimatedDuration, 0)
}

export function getPhaseTypeLabel(type: InterviewPhaseType): string {
  const labels: Record<InterviewPhaseType, string> = {
    screening: "Screening",
    technical: "Technical",
    behavioral: "Behavioral",
    "cultural-fit": "Cultural Fit",
    leadership: "Leadership",
    "final-round": "Final Round",
    "post-offer": "Post-Offer",
    custom: "Custom"
  }
  return labels[type]
}

export function getPhaseStatusLabel(status: InterviewPhaseStatus): string {
  const labels: Record<InterviewPhaseStatus, string> = {
    "not-started": "Not Started",
    "in-progress": "In Progress",
    completed: "Completed",
    skipped: "Skipped"
  }
  return labels[status]
}
