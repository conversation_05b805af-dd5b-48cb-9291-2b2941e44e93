"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { CheckCircle, ArrowRight, Mail, Bot, HeadsetIcon, Loader2, Sparkles, Crown, Gift, Zap } from "lucide-react"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { useAuth } from "@clerk/nextjs"
import axiosInstance from "@/config/axios"
import { toast } from "sonner"
import { GET_STRIPE_SUBSCRIPTION_DETAILS } from "@/utils/routes"
import { useTranslations } from "next-intl"
import { Badge } from "@/components/ui/badge"

interface PlanFeature {
  id: string
  name: string
  limit: string
  description: string
  used?: number
  remaining?: number
}

interface PlanLimits {
  planName: string
  features: PlanFeature[]
  support: {
    limit: string
    description: string
  }
}

export default function PaymentSuccessPage() {
  const t = useTranslations()
  const router = useRouter()
  const { getToken } = useAuth()
  const [planDetails, setPlanDetails] = useState<PlanLimits | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchSubscriptionDetails = async () => {
      try {
        const token = await getToken()
        const response = await axiosInstance.get(GET_STRIPE_SUBSCRIPTION_DETAILS, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        })
        setPlanDetails(response.data)
      } catch (error) {
        console.error("Error fetching subscription details:", error)
        toast.error(t("failed-to-load-subscription-details"))
      } finally {
        setLoading(false)
      }
    }

    fetchSubscriptionDetails()
  }, [getToken])

  if (loading) {
    return (
      <div className="h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600 dark:text-blue-400" />
          <p className="text-slate-600 dark:text-slate-400">{t("loading-subscription-details")}</p>
        </div>
      </div>
    )
  }

  const isEnterprise = planDetails?.planName?.toLowerCase().includes("enterprise")
  const isPremium =
    planDetails?.planName?.toLowerCase().includes("pro") || planDetails?.planName?.toLowerCase().includes("premium")

  return (
    <div className="h-screen p-10 bg-gradient-to-br from-slate-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 overflow-hidden">
      <div className="h-full flex">
        {/* Left Side - Success Message */}
        <div className="w-1/3 flex flex-col justify-center items-center p-8 bg-white/80 dark:bg-slate-800/80 backdrop-blur-xl border-r border-slate-200 dark:border-slate-700">
          {/* Success Animation */}
          <div className="mb-8">
            <div className="relative inline-block">
              <div className="absolute inset-0 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full blur-xl opacity-30 animate-pulse"></div>
              <div className="relative h-20 w-20 rounded-full bg-gradient-to-r from-emerald-500 to-green-600 flex items-center justify-center shadow-2xl">
                <CheckCircle className="h-10 w-10 text-white" />
              </div>
            </div>
          </div>

          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-900 via-blue-800 to-slate-900 dark:from-white dark:via-blue-200 dark:to-white bg-clip-text text-transparent mb-4">
              {t("payment-successful")}
            </h1>
            <p className="text-lg text-slate-600 dark:text-slate-400 mb-2">
              {t("welcome-to")}{" "}
              <span className="font-semibold text-blue-600 dark:text-blue-400">{planDetails?.planName}</span>!
            </p>
            <p className="text-slate-500 dark:text-slate-400">{t("your-account-has-been-successfully-upgraded")}</p>
          </div>

          {/* Plan Summary Card */}
          <div
            className={`w-full p-6 rounded-2xl border-2 mb-8 ${
              isEnterprise
                ? "bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-900 border-slate-300 dark:border-slate-600"
                : isPremium
                  ? "bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-950/50 dark:to-indigo-950/50 border-blue-200 dark:border-blue-800"
                  : "bg-gradient-to-br from-emerald-50 to-green-100 dark:from-emerald-950/50 dark:to-green-950/50 border-emerald-200 dark:border-emerald-800"
            }`}
          >
            <div className="flex items-center gap-3 mb-4">
              <div
                className={`p-3 rounded-xl ${
                  isEnterprise
                    ? "bg-slate-200 dark:bg-slate-700"
                    : isPremium
                      ? "bg-blue-200 dark:bg-blue-900/50"
                      : "bg-emerald-200 dark:bg-emerald-900/50"
                }`}
              >
                {isEnterprise ? (
                  <Crown className="h-6 w-6 text-slate-700 dark:text-slate-300" />
                ) : isPremium ? (
                  <Sparkles className="h-6 w-6 text-blue-700 dark:text-blue-300" />
                ) : (
                  <Gift className="h-6 w-6 text-emerald-700 dark:text-emerald-300" />
                )}
              </div>
              <div>
                <h3
                  className={`text-lg font-bold ${
                    isEnterprise
                      ? "text-slate-800 dark:text-slate-200"
                      : isPremium
                        ? "text-blue-800 dark:text-blue-200"
                        : "text-emerald-800 dark:text-emerald-200"
                  }`}
                >
                  {planDetails?.planName}
                </h3>
                <p
                  className={`text-sm ${
                    isEnterprise
                      ? "text-slate-600 dark:text-slate-400"
                      : isPremium
                        ? "text-blue-600 dark:text-blue-400"
                        : "text-emerald-600 dark:text-emerald-400"
                  }`}
                >
                  Active subscription
                </p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div className="text-center p-3 bg-white/60 dark:bg-slate-800/60 rounded-lg">
                <div
                  className={`text-xl font-bold ${
                    isEnterprise
                      ? "text-slate-700 dark:text-slate-300"
                      : isPremium
                        ? "text-blue-700 dark:text-blue-300"
                        : "text-emerald-700 dark:text-emerald-300"
                  }`}
                >
                  {planDetails?.features.length || 0}
                </div>
                <div className="text-xs text-slate-600 dark:text-slate-400">Features</div>
              </div>
              <div className="text-center p-3 bg-white/60 dark:bg-slate-800/60 rounded-lg">
                <div
                  className={`text-xl font-bold ${
                    isEnterprise
                      ? "text-slate-700 dark:text-slate-300"
                      : isPremium
                        ? "text-blue-700 dark:text-blue-300"
                        : "text-emerald-700 dark:text-emerald-300"
                  }`}
                >
                  {planDetails?.support.limit}
                </div>
                <div className="text-xs text-slate-600 dark:text-slate-400">Support</div>
              </div>
            </div>
          </div>

          {/* CTA Button */}
          <Button
            size="lg"
            className="w-full h-12 text-base font-semibold bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
            onClick={() => router.push("/enterprise/dashboard")}
          >
            {t("go-to-dashboard")}
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>

        {/* Right Side - Features Grid */}
        <div className="flex-1 p-8 overflow-hidden">
          <div className="h-full flex flex-col">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-slate-900 dark:text-slate-100 mb-2 flex items-center gap-3">
                <Zap className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                Your Plan Features
              </h2>
              <p className="text-slate-600 dark:text-slate-400">Everything included in your subscription</p>
            </div>

            {/* Features Grid */}
            <div className="flex-1 overflow-y-auto">
              <div className="grid grid-cols-2 gap-4 h-fit">
                {planDetails?.features.map((feature, index) => (
                  <div
                    key={feature.id}
                    className="flex items-start gap-3 p-4 bg-white/60 dark:bg-slate-800/60 rounded-xl border border-slate-200 dark:border-slate-700 hover:shadow-md transition-shadow"
                  >
                    <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex-shrink-0">
                      {index % 3 === 0 ? (
                        <Mail className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                      ) : index % 3 === 1 ? (
                        <Bot className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                      ) : (
                        <HeadsetIcon className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-slate-900 dark:text-slate-100 text-sm mb-1 truncate">
                        {feature.name}
                      </div>
                      <div className="text-xs text-slate-600 dark:text-slate-400 mb-2 line-clamp-2">
                        {feature.description}
                      </div>
                      <Badge
                        variant="secondary"
                        className="bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 text-xs"
                      >
                        {feature.limit}
                      </Badge>
                    </div>
                  </div>
                ))}

                {/* Support Feature */}
                <div className="flex items-start gap-3 p-4 bg-emerald-50 dark:bg-emerald-950/30 rounded-xl border border-emerald-200 dark:border-emerald-800 hover:shadow-md transition-shadow">
                  <div className="p-2 bg-emerald-100 dark:bg-emerald-900/50 rounded-lg flex-shrink-0">
                    <HeadsetIcon className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-emerald-900 dark:text-emerald-100 text-sm mb-1">
                      {t("support")}
                    </div>
                    <div className="text-xs text-emerald-700 dark:text-emerald-300 mb-2">
                      {planDetails?.support.description}
                    </div>
                    <Badge className="bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-300 border-0 text-xs">
                      {planDetails?.support.limit}
                    </Badge>
                  </div>
                </div>
              </div>
            </div>

            {/* Bottom Help Section */}
            <div className="mt-6 p-4 bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm rounded-xl border border-slate-200 dark:border-slate-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-900 dark:text-slate-100">Need help getting started?</p>
                  <p className="text-xs text-slate-600 dark:text-slate-400">Our team is here to help!</p>
                </div>
                <Button variant="ghost" size="sm" className="text-blue-600 hover:text-blue-700 dark:text-blue-400">
                  <Mail className="h-4 w-4 mr-2" />
                  Contact Support
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
