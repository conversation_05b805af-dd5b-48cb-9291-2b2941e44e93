"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ArrowLeft, FileText, Mail, CheckCircle, Clock, AlertCircle } from "lucide-react"
import { Link } from '@/i18n/nvigation';
import type { Candidate, InterviewResponse } from "@/app/[locale]/enterprise/ai-agents/ai-interviewer/old/interviews/[id]/page"
import axiosInstance from "@/config/axios"
import { GET_CANDIDATE_BY_INTERVIEW_ID_AND_CANDIDATE_ID } from "@/utils/routes"
import { useParams } from "next/navigation"
import { UsageLimitDialog } from "@/components/usage-limit-dialog"
import { checkUsageLimitByCompanyId } from "@/lib/checkUsageLimit"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import { useTranslations } from "next-intl" // Import useTranslations

export default function InterviewDetailPage() {
  const params = useParams<{ interviewId: string; candidateId: string }>()
  const router = useRouter()
  const t = useTranslations("InterviewDetailPage") // Initialize useTranslations

  const [interview, setInterview] = useState<InterviewResponse | null>(null)
  const [candidate, setCandidate] = useState<Candidate | null>(null)
  const [loading, setLoading] = useState(true)
  const [showLimitDialog, setShowLimitDialog] = useState(false)
  const [limitInfo, setLimitInfo] = useState({ type: "interview" as const, limit: 0 })

  const fetchInterview = async (interviewId: string, candidateId: string) => {
    try {
      const response = await axiosInstance.get<InterviewResponse>(
        GET_CANDIDATE_BY_INTERVIEW_ID_AND_CANDIDATE_ID(interviewId, candidateId),
      )
      console.log(response.data)
      setInterview(response.data)

      const matchedCandidate = response.data.candidates[0]
      setCandidate(matchedCandidate || null)
      setLoading(false)
    } catch (error) {
      console.error("Error fetching interview data:", error)
      setLoading(false)
    }
  }

  useEffect(() => {
    if (params.interviewId && params.candidateId) {
      fetchInterview(params.interviewId, params.candidateId)
    }
  }, [params.interviewId, params.candidateId])

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Helper function for status badge
  const getStatusBadge = (status: "pending" | "in_progress" | "completed") => {
    switch (status) {
      case "completed":
        return (
          <Badge className="bg-green-500">
            <CheckCircle className="h-3 w-3 mr-1" />
            {t("statusCompleted")}
          </Badge>
        )
      case "in_progress":
        return (
          <Badge className="bg-amber-500">
            <Clock className="h-3 w-3 mr-1" />
            {t("statusInProgress")}
          </Badge>
        )
      case "pending":
      default:
        return (
          <Badge variant="secondary">
            <AlertCircle className="h-3 w-3 mr-1" />
            {t("statusPending")}
          </Badge>
        )
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto py-8 px-4 flex items-center justify-center min-h-[60vh]">
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
          <p className="mt-4 text-muted-foreground">{t("loadingInterviewDetails")}</p>
        </div>
      </div>
    )
  }

  if (!interview || !candidate) {
    return (
      <div className="container mx-auto py-8 px-4">
        <Card>
          <CardContent className="pt-6 flex flex-col items-center">
            <div className="h-16 w-16 rounded-full bg-muted flex items-center justify-center mb-4">
              <FileText className="h-8 w-8 text-muted-foreground" />
            </div>
            <h2 className="text-xl font-bold mb-2">{t("interviewOrCandidateNotFoundTitle")}</h2>
            <p className="text-center text-muted-foreground mb-6">
              {t("interviewOrCandidateNotFoundDescription")}
            </p>
            <Link href="/enterprise/ai-agents/ai-interviewer/interviews">
              <Button>{t("returnToInterviews")}</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    )
  }

  const handleStartInterview = async () => {
    try {
      console.log(interview.company);
      const companyId = interview.company.id
      const result = await checkUsageLimitByCompanyId("interview", companyId!)
      
      if (!result.canProceed) {
        setLimitInfo({ type: "interview", limit: result.limit || 0 })
        setShowLimitDialog(true)
        return
      }

      router.push(`take/start`)
    } catch (error) {
      console.error("Error checking usage limit:", error)
      toast.error(t("failedToStartInterviewToast"))
    }
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex items-center gap-2 mb-6">
        {/* <Link href="/enterprise/ai-agents/ai-interviewer/interviews">
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link> */}
        <h1 className="text-2xl font-bold">{t("interviewDetailsTitle")}</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left column - Interview details */}
        <div className="flex flex-col gap-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>{t("roleCardTitle")}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <span className="text-lg font-medium">{interview.role}</span>
                <Badge>{interview.level}</Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle>{t("interviewTypeCardTitle")}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <span className="text-lg font-medium capitalize">{interview.type}</span>
                <Badge variant={interview.finalized ? "default" : "secondary"}>
                  {interview.finalized ? t("completedBadge") : t("notStartedBadge")}
                </Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle>{t("techStackCardTitle")}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {interview.techstack.map((tech) => (
                  <Badge key={tech} variant="secondary">
                    {tech}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Middle column - Candidate information */}
        <div className="flex flex-col gap-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>{t("candidateInformationCardTitle")}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col items-center text-center mb-4">
                <Avatar className="h-24 w-24 mb-3">
                  <AvatarImage
                    src={`https://ui-avatars.com/api/?name=${encodeURIComponent(candidate.name)}`}
                    alt={candidate.name}
                  />
                  <AvatarFallback>
                    {candidate.name
                      .split(" ")
                      .map((n) => n[0])
                      .join("")
                      .toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <h2 className="text-xl font-bold">{candidate.name}</h2>
                <div className="flex items-center text-muted-foreground mt-1">
                  <Mail className="h-4 w-4 mr-1" />
                  <span>{candidate.email}</span>
                </div>
              </div>

              <div className="space-y-4 mt-4">
                <div>
                  <p className="text-sm font-medium mb-1">{t("statusLabel")}</p>
                  <div>{getStatusBadge(candidate.status)}</div>
                </div>

                {candidate.score !== undefined && (
                  <div>
                    <p className="text-sm font-medium mb-1">{t("scoreLabel")}</p>
                    <div className="flex items-center">
                      <div className="bg-muted rounded-full h-2 w-full">
                        <div className="bg-primary rounded-full h-2" style={{ width: `${candidate.score}%` }} />
                      </div>
                      <span className="ml-2 font-medium">{candidate.score}%</span>
                    </div>
                  </div>
                )}

                {candidate.completedAt && (
                  <div>
                    <p className="text-sm font-medium mb-1">{t("completedAtLabel")}</p>
                    <p>{formatDate(candidate.completedAt)}</p>
                  </div>
                )}

                <div>
                  <p className="text-sm font-medium mb-1">{t("feedbackStatusLabel")}</p>
                  <Badge variant={candidate.hasFeedback ? "default" : "outline"}>
                    {candidate.hasFeedback ? t("feedbackAvailable") : t("noFeedback")}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right column - Actions */}
        <div className="flex-1 flex justify-center">
          {candidate.status === "completed" && candidate.hasFeedback ? (
            <Card className="max-w-md w-full">
              <CardContent className="pt-6 flex flex-col items-center">
                <div className="h-16 w-16 rounded-full bg-green-100 flex items-center justify-center mb-4">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
                <h2 className="text-xl font-bold mb-2">{t("interviewCompletedCardTitle")}</h2>
                <p className="text-center text-muted-foreground mb-6">
                  {t("interviewCompletedDescription")}
                </p>
                <div className="w-full space-y-3">
                  <div className="text-center text-sm text-muted-foreground text-yellow-500 mt-2">
                    <p>{t("feedbackOnlyVisibleToCompany")}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card className="max-w-md w-full">
              <CardContent className="pt-6 flex flex-col items-center">
                <Avatar className="h-24 w-24 mb-4">
                  <AvatarImage src="/placeholder.svg?height=96&width=96" alt="AI Interviewer" />
                  <AvatarFallback>AI</AvatarFallback>
                </Avatar>
                <h2 className="text-xl font-bold mb-2">{t("readyToStartInterviewTitle")}</h2>
                <p className="text-center text-muted-foreground mb-6">
                  {t("readyToStartInterviewDescription", {
                    interviewType: interview.type,
                    interviewRole: interview.role,
                    interviewLevel: interview.level,
                  })}
                </p>
                <Button
                  size="lg"
                  className="font-semibold shadow-md transition-all duration-300 w-full mb-3"
                  onClick={handleStartInterview}
                >
                  {t("startInterviewButton")}
                </Button>

                {candidate.status === "completed" && (
                  <Button variant="outline" size="lg" className="font-semibold w-full">
                    {t("viewFullTranscriptButton")}
                  </Button>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      </div>
      <UsageLimitDialog
        open={showLimitDialog}
        onOpenChange={setShowLimitDialog}
        type={limitInfo.type}
        limit={limitInfo.limit}
      />
    </div>
  )
}