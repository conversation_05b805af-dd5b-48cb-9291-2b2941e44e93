"use client";
import Agent from "@/app/[locale]/enterprise/ai-agents/ai-interviewer/_components/agent";
import {
  InterviewResponse,
  Message,
} from "@/app/[locale]/enterprise/ai-agents/ai-interviewer/old/interviews/[id]/page";
import axiosInstance from "@/config/axios";
import { GET_CANDIDATE_BY_INTERVIEW_ID_AND_CANDIDATE_ID, GET_INTERVIEW_BY_ID } from "@/utils/routes";
import { useParams, useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
export interface AgentProps {
  userName: string;
  userId?: string;
  interviewId?: string;
  feedbackId?: string;
  type: "generate" | "interview";
  questions?: string[];
  interviewType: string;
  interviewRole: string;
  interviewLevel: string;
}

export interface Interview {
  id: string;
  role: string;
  level: string;
  questions: string[];
  techstack: string[];
  createdAt: string;
  userId: string;
  type: string;
  finalized: boolean;
  candidateName: string;
  candidateEmail: string;
  transcript: Message[];
  hasFeedback: boolean;
}
function page() {
  const params = useParams<{ interviewId: string; candidateId: string }>();
  const router = useRouter();
  const [interview, setInterview] = useState<InterviewResponse | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchInterview = async (interviewId: string, candidateId: string) => {
    try {
      setLoading(true);
      const candidate = await axiosInstance.get<InterviewResponse>(
        GET_CANDIDATE_BY_INTERVIEW_ID_AND_CANDIDATE_ID(interviewId, candidateId),
      )
      if (candidate.data.candidates[0].hasFeedback) {
        router.push(
          `/public-page/interview/${interviewId}/${params.candidateId}/take`
        );
        return;
      }
      const response = await axiosInstance.get<InterviewResponse>(
        `${GET_INTERVIEW_BY_ID(interviewId)}?includeCandidates=false`
      );      
      setInterview(response.data);
      setLoading(false);
    } catch (error) {
      console.error("Error fetching interview:", error);
      setLoading(false);
    }
  };

  useEffect(() => {
    if (params.interviewId) {
      fetchInterview(params.interviewId, params.candidateId);
    }
  }, [params?.interviewId, params?.candidateId]);

  if (loading) {
    return (
      <div className="container mx-auto py-8 px-4 flex items-center justify-center min-h-[60vh]">
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
          <p className="mt-4 text-muted-foreground">Loading Interview...</p>
        </div>
      </div>
    );
  }

  return (
    <Agent
      userName={"You"}
      candidateId={params.candidateId}
      interviewId={params.interviewId}
      type="interview"
      questions={interview?.questions}
      interviewType={interview?.type!}
      interviewRole={interview?.role!}
      interviewLevel={interview?.level!}
    />
  );
}

export default page;
