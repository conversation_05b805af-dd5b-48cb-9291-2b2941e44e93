"use client";

import { useEffect, useState } from "react";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Clock, Mail } from "lucide-react";
import { useAuth } from "@clerk/nextjs";
import axiosInstance from "@/config/axios";
import { COMPANY_USAGE_INFO } from "@/utils/routes";
import { toast } from "sonner";
import { useTranslations } from "next-intl"; // Import useTranslations

interface UsageInfo {
  key: string;
  name: string;
  unit: string;
  limit: number;
  used: number;
  remaining: number;
}

interface CompanyUsageResponse {
  success: boolean;
  features: UsageInfo[];
}

export default function CompanyUsagePage() {
  const { getToken } = useAuth();
const [usageData, setUsageData] = useState<CompanyUsageResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const t = useTranslations("CompanyUsagePage"); // Initialize useTranslations

useEffect(() => {
  const fetchUsageData = async () => {
    try {
      const token = await getToken();
      const response = await axiosInstance.get<CompanyUsageResponse>(
        COMPANY_USAGE_INFO,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.data.success) {
        setUsageData(response.data);
      }
    } catch (error) {
      console.error("Failed to fetch usage data:", error);
      toast.error(t("failedToLoadUsageInfoToast"));
    } finally {
      setLoading(false);
    }
  };

  fetchUsageData();
}, [getToken, t]);

  if (loading) {
    return (
      <div className="container mx-auto p-8">
        <div className="animate-pulse">
          <div className="h-8 w-64 bg-gray-200 rounded mb-8"></div>
          <div className="space-y-6">
            <div className="h-48 bg-gray-200 rounded"></div>
            <div className="h-48 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-3xl font-bold mb-8">{t("loadingPageTitle")}</h1> {/* Use translation */}

      <div className="grid gap-8">
        {usageData?.features.map((feature) => (
          <Card key={feature.key}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-xl font-bold flex items-center gap-2">
                {/* Optionally use an icon map here */}
                {feature.name}
              </CardTitle>
              <span className={`px-3 py-1 rounded-full text-sm ${
                feature.remaining === 0 ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
              }`}>
                {feature.remaining === 0 ? t("exceededStatus") : t("activeStatus")}
              </span>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t("tableHeadTotalLimit")}</TableHead>
                    <TableHead>{t("tableHeadUsed")}</TableHead>
                    <TableHead>{t("tableHeadRemaining")}</TableHead>
                    <TableHead>{t("tableHeadUsagePercentage")}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell>{`${feature.limit} ${feature.unit}`}</TableCell>
                    <TableCell>{`${feature.used} ${feature.unit}`}</TableCell>
                    <TableCell>{`${feature.remaining} ${feature.unit}`}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div
                            className={`h-2.5 rounded-full ${
                              (feature.used || 0) / (feature.limit || 1) > 0.9
                                ? 'bg-red-500'
                                : 'bg-green-500'
                            }`}
                            style={{
                              width: `${((feature.used || 0) / (feature.limit || 1)) * 100}%`
                            }}
                          ></div>
                        </div>
                        <span className="text-sm">
                          {Math.round(((feature.used || 0) / (feature.limit || 1)) * 100)}%
                        </span>
                      </div>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}