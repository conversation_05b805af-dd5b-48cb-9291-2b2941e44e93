"use client";
import { useEffect, useRef, useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>c<PERSON><PERSON>,
  Phone,
  PhoneOff,
  Video,
  VideoOff,
} from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import Webcam from "react-webcam";
import { useRouter } from "next/navigation";
import axiosInstance from "@/config/axios";
import { CREATE_INTERVIEW_FEEDBACK, UPDATE_USAGE_LIMIT } from "@/utils/routes";
import Vapi from "@vapi-ai/web";
import type { Message } from "../interviews/[id]/page";
import { interviewer } from "../_constants";
import { useAuth } from "@clerk/nextjs";
import { useTranslations } from "next-intl";

enum CallStatus {
  INACTIVE = "INACTIVE",
  CONNECTING = "CONNECTING",
  ACTIVE = "ACTIVE",
  FINISHED = "FINISHED",
}

export interface AgentProps {
  userName: string;
  createdBy?: string;
  candidateId: string;
  companyId: string;
  companyName: string;
  interviewId?: string;
  feedbackId?: string;
  type: "generate" | "interview";
  questions?: string[];
  interviewType: string;
  interviewRole: string;
  interviewLevel: string;
  timeInSeconds?: number; // New prop for countdown timer in seconds
}

function Agent({
  userName,
  createdBy,
  candidateId,
  companyId,
  companyName,
  type,
  interviewId,
  questions,
  interviewType,
  interviewRole,
  interviewLevel,
}: // timeInSeconds = 30, // Default to 5 minutes (300 seconds)
AgentProps) {
  const [callStatus, setCallStatus] = useState<CallStatus>(CallStatus.INACTIVE);
  const [messages, setMessages] = useState<Message[]>([]);
  const [audioEnabled, setAudioEnabled] = useState(true);
  const [isCameraOn, setIsCameraOn] = useState(true);
  const webcamRef = useRef<Webcam>(null);
  const router = useRouter();
  const scrollRef = useRef<HTMLDivElement | null>(null);
  const [vapi, setVapi] = useState<Vapi | null>(null);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const { getToken } = useAuth();

  const t = useTranslations()

  // Countdown timer state
  // const [timeRemaining, setTimeRemaining] = useState(timeInSeconds)
  const [isTimerRunning, setIsTimerRunning] = useState(false);

  // Hard-coded tokens and workflows
  const tokens = [
    // process.env.NEXT_PUBLIC_VAPI_WEB_TOKEN_T1!,
    // process.env.NEXT_PUBLIC_VAPI_WEB_TOKEN_T2!,
    // process.env.NEXT_PUBLIC_VAPI_WEB_TOKEN_T3!,
    // FOR TESTING PURPOSES ONLY
    process.env.NEXT_PUBLIC_VAPI_WEB_TOKEN_T1!,
  ];

  const workflows = [
    // process.env.NEXT_PUBLIC_VAPI_WORKFLOW_ID_W1!,
    // process.env.NEXT_PUBLIC_VAPI_WORKFLOW_ID_W2!,
    // process.env.NEXT_PUBLIC_VAPI_WORKFLOW_ID_W3!,
    // FOR TESTING PURPOSES ONLY
    process.env.NEXT_PUBLIC_VAPI_WORFLOW_ID_W1!,
  ];

  // Validate tokens and workflows
  if (tokens.includes(undefined!) || workflows.includes(undefined!)) {
    console.error(
      "Missing environment variables for VAPI tokens or workflows."
    );
    return null;
  }

  // Format time for display (MM:SS)
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
      .toString()
      .padStart(2, "0")}`;
  };

  useEffect(() => {
    let callStartTime: number | null = null;
    let isCallActive = false; // Track if the call is active

    const onCallStart = () => {
      callStartTime = Date.now();
      setCallStatus(CallStatus.ACTIVE);
      setIsTimerRunning(true);
      isCallActive = true; // Set call status to active
    };

    const onCallEnd = () => {
      setCallStatus(CallStatus.FINISHED);
      setIsTimerRunning(false);
      isCallActive = false; // Reset call status

      if (callStartTime) {
        const callEndTime = Date.now();
        const durationInSeconds = Math.floor(
          (callEndTime - callStartTime) / 1000
        );

        console.log(`Call duration: ${durationInSeconds} seconds`);

        // send to your DB here
        const durationInHours = durationInSeconds / 3600; // Convert seconds to hours
        const sendCallDuration = async () => {
          try {
            const token = await getToken();
            await axiosInstance.post(
              UPDATE_USAGE_LIMIT,
              {
                type: "interview",
                value: durationInHours,
              },
              {
                headers: {
                  Authorization: `Bearer ${token}`,
                },
              }
            );
            console.log("Call duration saved:", durationInHours, "hours");
          } catch (err) {
            console.error("Failed to save call duration:", err);
            toast.error(t('failed-to-update-usage-tracking'));
          }
        };
        sendCallDuration();
      }
      callStartTime = null;
    };

    const onMessage = (message: any) => {
      if (message.type === "transcript" && message.transcriptType === "final") {
        const newMessage = {
          role: message.role,
          content: message.transcript,
          timestamp: message.time
            ? new Date(message.time).toDateString()
            : new Date().toDateString(),
        };
        setMessages((prevMessages) => [...prevMessages, newMessage]);
      }
    };

    const onSpeechStart = () => setIsSpeaking(true);
    const onSpeechEnd = () => setIsSpeaking(false);
    const onError = (error: Error) => console.log("Error", error);

    if (vapi) {
      vapi.on("call-start", onCallStart);
      vapi.on("call-end", onCallEnd);
      vapi.on("message", onMessage);
      vapi.on("speech-start", onSpeechStart);
      vapi.on("speech-end", onSpeechEnd);
      vapi.on("error", onError);

      return () => {
        vapi.off("call-start", onCallStart);
        vapi.off("call-end", onCallEnd);
        vapi.off("message", onMessage);
        vapi.off("speech-start", onSpeechStart);
        vapi.off("speech-end", onSpeechEnd);
        vapi.off("error", onError);
      };
    }
  }, [vapi]);

  const handleCall = async (token: string, workflow: string) => {
    setCallStatus(CallStatus.CONNECTING);

    const vapiInstance = new Vapi(token);
    setVapi(vapiInstance); // Save Vapi to state.

    console.log("Using VAPI token:", token);
    console.log("Using workflow:", workflow);

    if (type === "generate") {
      await vapiInstance.start(workflow, {
        variableValues: {
          createdBy,
          companyId,
          companyName,
        }
      });
      setCallStatus(CallStatus.ACTIVE);
    } else {
      let formatedQuestions = "";
      if (questions) {
        formatedQuestions = questions.map((q) => `-${q}`).join("\n");
      }
      await vapiInstance.start(interviewer, {
        variableValues: { questions: formatedQuestions, language: "French" },
      });
    }
  };

  const handleDisconnect = () => {
    vapi?.stop();
    setCallStatus(CallStatus.FINISHED);
    setIsTimerRunning(false);
  };

  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages]);

  useEffect(() => {
    if (callStatus === CallStatus.FINISHED) {
      if (type === "generate") {
        router.push("/enterprise/ai-agents/ai-interviewer/interviews");
      } else {
        handleGenerateFeedback(messages);
        router.push(
          `/public-page/interview/${interviewId}/${candidateId}/take`
        );
      }
    }
  }, [callStatus, messages, type, candidateId, router]);

  const handleGenerateFeedback = async (messages: Message[]) => {
    try {
      if (!interviewId || !candidateId) {
        toast.error(t('interview-id-or-user-id-is-missing'));
        return;
      }
      const formattedTranscript = messages
        .map(
          (sentence: { role: string; content: string }) =>
            `- ${sentence.role}:  ${sentence.content}\n`
        )
        .join("");
      console.log(formattedTranscript);
      const response = await axiosInstance.post(
        CREATE_INTERVIEW_FEEDBACK(interviewId, candidateId),
        {
          formattedTranscript: formattedTranscript,
        }
      );
      console.log(response.data);
    } catch (error) {
      console.error(error);
    }
  };

  const formatTimestamp = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    });
  };

  return (
    <div
      className="container mx-auto py-4 px-4 flex flex-col"
      style={{ height: "calc(100vh - 4rem)" }}
    >
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center gap-4">
          <h1 className="text-2xl font-bold">
            {type === "interview"
              ? `${interviewRole} ${interviewLevel} Interview (${interviewType})`
              : t('generate-the-interview')}
          </h1>
          {/* Countdown Timer - Only show for "generate" type or when timer is running */}
          {/* {(type === "interview" || isTimerRunning) && (
            <div
              className={`px-4 py-2 rounded-full font-mono text-lg ${timeRemaining <= 30 ? "bg-red-100 text-red-600 animate-pulse" : "bg-gray-100 text-gray-700"}`}
            >
              {formatTime(timeRemaining)}
            </div>
          )} */}
        </div>
        {callStatus !== CallStatus.ACTIVE ? (
          <button
            className={cn(
              "flex items-center justify-center gap-2 py-3 px-8 rounded-full text-white font-medium transition-all",
              callStatus === CallStatus.CONNECTING
                ? "bg-amber-400 cursor-not-allowed"
                : "bg-amber-400 hover:bg-amber-500"
            )}
            onClick={() => {
              const index = Math.floor(Math.random() * tokens.length);
              handleCall(tokens[index], workflows[index]);
            }}
            disabled={callStatus === CallStatus.CONNECTING}
          >
            <Phone className="w-4 h-4" />
            <span>
              {callStatus === CallStatus.INACTIVE ? t('call') : "Connecting..."}
            </span>
          </button>
        ) : (
          <button
            className="flex items-center justify-center gap-2 py-3 px-8 rounded-full text-white font-medium transition-all bg-red-500 hover:bg-red-600"
            onClick={handleDisconnect}
          >
            <PhoneOff className="w-4 h-4" />
            <span>{t('end')}</span>
          </button>
        )}
      </div>
      <div className="flex flex-col flex-1 gap-4 overflow-hidden">
        {/* Top section - Two camera cards side by side */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 h-[40vh]">
          {/* User's webcam card */}
          <Card className="flex-1 relative overflow-hidden">
            <CardContent className="p-4 h-full flex flex-col">
              <div className="relative rounded-lg overflow-hidden bg-muted h-full flex items-center justify-center">
                {isCameraOn ? (
                  <Webcam
                    ref={webcamRef}
                    audio={false}
                    screenshotFormat="image/png"
                    className="w-full h-full object-cover transform scale-x-[-1]"
                    mirrored={false} // Disable mirroring
                  />
                ) : (
                  <div className="w-full h-full flex flex-col items-center justify-center">
                    <div className="w-16 h-16 rounded-full bg-gray-800 flex items-center justify-center mb-3">
                      <CameraOff className="w-8 h-8 text-gray-500" />
                    </div>
                    <p className="text-gray-400 text-sm">
                      t('camera-is-turned-off')
                    </p>
                  </div>
                )}
                <div className="absolute bottom-4 left-4 bg-black/50 rounded-lg px-3 py-1.5 text-white">
                  <p className="text-sm font-medium">{t('you')}</p>
                </div>
                <div className="absolute bottom-4 right-4 flex gap-2">
                  <Button
                    size="icon"
                    onClick={() => setIsCameraOn(!isCameraOn)}
                    className="rounded-full bg-red-500 text-white hover:bg-red-600"
                  >
                    {isCameraOn ? (
                      <Video className="h-4 w-4" />
                    ) : (
                      <VideoOff className="h-4 w-4" />
                    )}
                  </Button>
                  <Button
                    variant="destructive"
                    size="icon"
                    onClick={() => setAudioEnabled(!audioEnabled)}
                    className="rounded-full bg-red-500 text-white hover:bg-red-600"
                  >
                    {audioEnabled ? (
                      <Mic className="h-4 w-4" />
                    ) : (
                      <MicOff className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
          {/* AI camera card */}
          <Card className="flex-1 relative overflow-hidden">
            <CardContent className="p-4 h-full flex flex-col">
              <div className="relative rounded-lg overflow-hidden bg-gray-900 h-full flex items-center justify-center">
                {/* AI Avatar */}
                <Avatar className="h-32 w-32">
                  <AvatarImage
                    className="transition-opacity duration-300"
                    src={
                      callStatus === CallStatus.ACTIVE
                        ? "/ai-avatar.png?height=128&width=128"
                        : "/call-now.png?height=128&width=128"
                    }
                    alt={t('agent')}
                  />
                  <AvatarFallback>AI</AvatarFallback>
                  {isSpeaking && callStatus === CallStatus.ACTIVE && (
                    <span className="absolute top-0 left-0 h-full w-full bg-blue-500 opacity-50 animate-ping rounded-full"></span>
                  )}
                </Avatar>

                {/* Camera indicator */}
                <div className="absolute top-4 right-4 flex items-center gap-2">
                  <span
                    className={`h-2 w-2 rounded-full ${
                      callStatus !== CallStatus.INACTIVE
                        ? "bg-red-500"
                        : "bg-yellow-500"
                    }`}
                  ></span>
                  <span className="text-xs text-white">REC</span>
                </div>
                {/* AI Name and status */}
                <div className="absolute bottom-4 left-4 bg-black/50 rounded-lg px-3 py-1.5 text-white">
                  <p className="text-sm font-medium">{t('ai-interviewer')}</p>
                  {callStatus !== CallStatus.INACTIVE && (
                    <p className="text-xs opacity-80">{t('listening')}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        {/* Bottom section - Transcript only */}
        <div className="flex flex-col flex-1 border rounded-lg">
          <div className="p-3 border-b bg-muted">
            <h3 className="font-medium">{t('transcript')}</h3>
          </div>
          {messages.length > 0 ? (
            <ScrollArea className="flex-1 px-4 py-10 overflow-y-auto max-h-[300px]">
              <div className="space-y-4">
                {messages.map((message, index) => (
                  <div key={index} className="mb-4">
                    <p className="font-semibold">
                      {message.role === "user" ? userName : t('ai-interviewer')}:
                    </p>
                    <p>{message.content}</p>
                    <p className="text-xs text-muted-foreground">
                      {formatTimestamp(message.timestamp)}
                    </p>
                  </div>
                ))}
              </div>
              <div ref={scrollRef}></div> {/* Placeholder div for scrolling */}
            </ScrollArea>
          ) : (
            <div className="text-center py-12">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-100 dark:bg-gray-800 mb-4">
                <div className="text-gray-400">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    className="w-8 h-8"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m6.75 12l-3-3m0 0l-3 3m3-3v6m-1.5-15H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"
                    />
                  </svg>
                </div>
              </div>
              <h3 className="text-lg font-medium mb-2">{t('no-messages-found')}</h3>
              <p className="text-gray-500 dark:text-gray-400 mb-6">
                {t('it-looks-like-no-messages-have-been-sent-yet')}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default Agent;
