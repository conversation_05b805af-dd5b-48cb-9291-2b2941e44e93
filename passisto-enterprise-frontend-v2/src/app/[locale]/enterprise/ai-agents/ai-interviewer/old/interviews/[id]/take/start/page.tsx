import React from 'react'
import Agent from '../../../../_components/agent'
import { Message } from '../../page';
export interface AgentProps {
  userName: string;
  createdBy?: string;
  candidateId:string;
  companyId: string;
  companyName: string;
  interviewId?: string;
  feedbackId?: string;
  type: "generate" | "interview";
  questions?: string[];
  interviewType: string;
  interviewRole: string
  interviewLevel: string
}

export interface Interview {
  id: string;
  role: string;
  level: string;
  questions: string[];
  techstack: string[];
  createdAt: string;
  userId: string;
  type: string;
  finalized: boolean;
  candidateName: string;
  candidateEmail: string;
  transcript: Message[];
  hasFeedback: boolean;
}
const mockInterview: Interview = {
  id: "3",
  role: "Frontend Developer",
  level: "senior",
  questions: [
    "Tell me about your experience with React",
    "How do you handle state management?",
    "Explain your approach to responsive design",
    "How do you optimize performance in web applications?",
    "Describe a challenging project you worked on",
  ],
  techstack: ["React", "TypeScript", "Next.js"],
  createdAt: "2023-05-15T10:30:00Z",
  userId: "user1",
  type: "technical",
  finalized: false,
  candidateName: "<PERSON>",
  candidateEmail: "<EMAIL>",
  transcript: [],
  hasFeedback: false,
};
function page({
  params,
}: {
  params: { id: string };
}) {
  return (
    <Agent 
    // userName={"Abdelkabir"}
    createdBy={"Manager"}
    companyId={"123"}
    interviewId={mockInterview.id}
    type="interview"
    questions={mockInterview.questions}
    interviewType={mockInterview.type}
    interviewRole={mockInterview.role}
    interviewLevel={mockInterview.level}
    />
  )
}

export default page