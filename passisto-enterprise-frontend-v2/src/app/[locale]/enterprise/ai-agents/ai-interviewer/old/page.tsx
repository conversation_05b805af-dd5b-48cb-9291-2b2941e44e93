"use client";
import { useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Link } from "@/i18n/nvigation";
import {
  ArrowRight,
  Calendar,
  Clock,
  Plus,
  Sparkles,
  Users,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useAuth } from "@clerk/nextjs";
import { checkUsageLimit } from "@/lib/checkUsageLimit";
import { useState } from "react";
import { UsageLimitDialog } from "@/components/usage-limit-dialog";
import { useBackendUser } from "@/hooks/useBackendUser";
import {
  checkPermissions,
  interviewPermissions,
} from "@/utils/ACTION_PERMISSIONS";
import { useTranslations } from "next-intl";

export default function Home() {
  const t = useTranslations();
  const { getToken } = useAuth();
  const router = useRouter();
  const [showLimitDialog, setShowLimitDialog] = useState(false);
  const [limitInfo, setLimitInfo] = useState({ type: "interview", limit: 0 });

  const { backendUser, loading: backendUserLoading } = useBackendUser();
  const handleCreateInterview = async () => {
    const token = await getToken();
    const result = await checkUsageLimit("interview", token!);
    if (!result.canProceed) {
      setLimitInfo({ type: "interview", limit: result.limit || 0 });
      setShowLimitDialog(true);
      return;
    }
    if (result.canProceed) {
      router.push("/enterprise/ai-agents/ai-interviewer/create");
    }
  };
  useEffect(() => {
    // Only check permissions after backendUser has loaded
    if (!backendUserLoading && backendUser) {
      const permissions = backendUser?.permissions ?? [];

      // Check if user has at least one interview-related permission
      const hasAnyInterviewPermission = checkPermissions(permissions, [
        interviewPermissions.canView,
        interviewPermissions.canCreate,
        interviewPermissions.canUpdate,
        interviewPermissions.canDelete,
        interviewPermissions.canSendToCandidate,
        interviewPermissions.canViewFeedback,
      ]);

      // Redirect if user doesn't have any interview-related permissions
      if (!hasAnyInterviewPermission) {
        toast.error(t("permission-denied"), {
          description: t(
            "you-dont-have-permission-to-access-the-interviews-page"
          ),
        });
        router.push("/enterprise/dashboard");
      }
    }
  }, [backendUser, backendUserLoading, router]);

  return (
    <div className="flex min-h-screen flex-col">
      <main className="flex-1">
        <section className="w-full py-12 md:py-24 lg:py-32">
          <div className="container px-4 md:px-6">
            {/* <div className="grid gap-6 lg:grid-cols-2 lg:gap-12"> */}
            <div className="flex flex-col justify-center items-center space-y-12">
              <div className="flex flex-col justify-center items-center space-y-4">
                <div className="flex flex-col justify-center items-center text-center space-y-2">
                  <h2 className="flex items-center justify-center gap-2 text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                    <Sparkles className="h-6 w-6 text-primary" />
                    {t("ai-powered-interviewer")}
                  </h2>
                  <p className="max-w-[600px] text-center text-gray-500 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed dark:text-gray-400">
                    {t(
                      "practice-your-interview-skills-with-our-ai-interviewer-get-real-time-feedback-and-improve-your-chances-of-landing-your-dream-job"
                    )}
                  </p>
                </div>
                <div className="flex flex-col gap-2 min-[400px]:flex-row">
                  {interviewPermissions.canCreate(
                    backendUser?.permissions || []
                  ) && (
                    <Button
                      onClick={handleCreateInterview}
                      className="inline-flex h-10 items-center justify-center rounded-md bg-primary px-8 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50"
                    >
                      {t("create-an-interview")}
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  )}
                  <Link href="/enterprise/ai-agents/ai-interviewer/interviews">
                    {interviewPermissions.canView(
                      backendUser?.permissions || []
                    ) && (
                      <Button
                        variant="outline"
                        className="inline-flex h-10 items-center justify-center rounded-md border border-input bg-background px-8 text-sm font-medium shadow-sm transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50"
                      >
                        {t("view-interviews-templates")}
                      </Button>
                    )}
                  </Link>
                </div>
              </div>
              <div className="flex justify-center lg:justify-end">
                <div className="grid gap-4 sm:grid-cols-2">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle>{t("realistic-experience")}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-primary" />
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {t("simulates-real-interview-scenarios")}
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle>{t("instant-feedback-0")}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-primary" />
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {t("get-immediate-insights-on-your-performance")}
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle>{t("customizable")}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-primary" />
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {t("choose-role-level-and-tech-stack")}
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle>{t("track-progress")}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center gap-2">
                        <ArrowRight className="h-4 w-4 text-primary" />
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {t("monitor-your-improvement-over-time")}
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      <UsageLimitDialog
        open={showLimitDialog}
        onOpenChange={setShowLimitDialog}
        type="interview"
        limit={limitInfo.limit}
      />
    </div>
  );
}
