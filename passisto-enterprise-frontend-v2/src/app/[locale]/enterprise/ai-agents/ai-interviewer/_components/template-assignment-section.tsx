"use client"

import * as React from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Label } from "@/components/ui/label"
import { 
  CheckCircle, 
  Clock, 
  AlertTriangle, 
  FileText, 
  Settings,
  ChevronRight,
  User,
  Calendar
} from "lucide-react"
import { 
  InterviewPhase, 
  InterviewPhaseType, 
  getPhaseTypeLabel 
} from "@/lib/utils"

type InterviewQuestion = {
  id: string
  question: string
  type: "Technical" | "Behavioral" | "Problem-Solving" | "System Design"
  difficulty: "Easy" | "Medium" | "Hard"
  category: string
}

type InterviewTemplate = {
  id: string
  name: string
  type: "Technical" | "Behavioral" | "Mixed" | "Custom" | "Sales" | "Leadership"
  dateModified: string
  role?: string
  level?: string
  techStack?: string
  questionCount?: number
  questions?: InterviewQuestion[]
  phases?: InterviewPhaseType[]
}

type PhaseTemplateAssignment = {
  phaseId: string
  templateId: string | null
}

type TemplateAssignmentProps = {
  projectId: string
  phases: InterviewPhase[]
  templates: InterviewTemplate[]
  onNext: () => void
  onAssignmentsChange?: (assignments: PhaseTemplateAssignment[]) => void
}

export function TemplateAssignmentSection({ 
  projectId, 
  phases: propPhases, 
  templates: propTemplates, 
  onNext, 
  onAssignmentsChange 
}: TemplateAssignmentProps) {
  // Add mock phases if none provided (for UI preview)
  const mockPhases: InterviewPhase[] = [
    {
      id: "mock-phase-1",
      name: "Initial Screening",
      type: "screening",
      description: "Initial phone/video screening to assess basic qualifications and cultural fit",
      order: 1,
      isRequired: true,
      estimatedDuration: 30,
      startDate: "2025-08-25T09:00",
      endDate: "2025-08-25T17:00",
      configuration: {
        allowSkip: false,
        requiresApproval: true,
        autoAdvance: false,
        passingScore: 70,
        maxAttempts: 1
      },
      status: "not-started",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: "mock-phase-2",
      name: "Technical Assessment",
      type: "technical",
      description: "Coding assessment focusing on problem-solving and technical skills",
      order: 2,
      isRequired: true,
      estimatedDuration: 90,
      startDate: "2025-08-26T10:00",
      endDate: "2025-08-26T16:00",
      configuration: {
        allowSkip: false,
        requiresApproval: true,
        autoAdvance: false,
        passingScore: 75,
        maxAttempts: 1
      },
      status: "not-started",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: "mock-phase-3",
      name: "Behavioral Interview",
      type: "behavioral",
      description: "Discussion about past experiences, problem-solving approach, and team collaboration",
      order: 3,
      isRequired: true,
      estimatedDuration: 60,
      startDate: "2025-08-27T14:00",
      endDate: "2025-08-27T18:00",
      configuration: {
        allowSkip: false,
        requiresApproval: true,
        autoAdvance: false,
        passingScore: 70,
        maxAttempts: 1
      },
      status: "not-started",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: "mock-phase-4",
      name: "System Design Interview",
      type: "technical",
      description: "Architecture and system design discussion for senior-level candidates",
      order: 4,
      isRequired: false,
      estimatedDuration: 75,
      startDate: "2025-08-28T13:00",
      endDate: "2025-08-28T17:00",
      configuration: {
        allowSkip: true,
        requiresApproval: true,
        autoAdvance: false,
        passingScore: 80,
        maxAttempts: 1
      },
      status: "not-started",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: "mock-phase-5",
      name: "Final Round",
      type: "final-round",
      description: "Final interview with team lead and stakeholders",
      order: 5,
      isRequired: true,
      estimatedDuration: 45,
      startDate: "2025-08-29T11:00",
      endDate: "2025-08-29T15:00",
      configuration: {
        allowSkip: false,
        requiresApproval: true,
        autoAdvance: false,
        passingScore: 75,
        maxAttempts: 1
      },
      status: "not-started",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ]

  // Add mock templates if none provided (for UI preview)
  const mockTemplates: InterviewTemplate[] = [
    {
      id: "mock-screening",
      name: "Initial Screening Interview",
      type: "Mixed",
      dateModified: "2025-08-20",
      role: "Software Engineer",
      level: "All Levels",
      techStack: "General Programming",
      questionCount: 5,
      questions: []
    },
    {
      id: "mock-technical",
      name: "Technical Deep Dive - Frontend",
      type: "Technical",
      dateModified: "2025-08-19",
      role: "Frontend Developer",
      level: "Senior",
      techStack: "React, TypeScript, JavaScript",
      questionCount: 12,
      questions: []
    },
    {
      id: "mock-behavioral",
      name: "Behavioral & Culture Fit",
      type: "Behavioral",
      dateModified: "2025-08-18",
      role: "Software Engineer",
      level: "All Levels",
      techStack: "General",
      questionCount: 8,
      questions: []
    },
    {
      id: "mock-system-design",
      name: "System Design & Architecture",
      type: "Technical",
      dateModified: "2025-08-17",
      role: "Senior Engineer",
      level: "Senior",
      techStack: "System Design, AWS, Microservices",
      questionCount: 6,
      questions: []
    },
    {
      id: "mock-leadership",
      name: "Leadership & Management",
      type: "Leadership",
      dateModified: "2025-08-16",
      role: "Engineering Manager",
      level: "Senior",
      techStack: "Management, Team Leadership",
      questionCount: 10,
      questions: []
    },
    {
      id: "mock-final",
      name: "Final Round - Executive Interview",
      type: "Mixed",
      dateModified: "2025-08-15",
      role: "Software Engineer",
      level: "All Levels",
      techStack: "General, Company Vision",
      questionCount: 4,
      questions: []
    }
  ]

  // Use provided phases or fall back to mock data for preview
  const phases = propPhases.length > 0 ? propPhases : mockPhases

  // Use provided templates or fall back to mock data for preview
  const templates = propTemplates.length > 0 ? propTemplates : mockTemplates

  // Smart pre-assignment for demo purposes
  const getDefaultTemplateForPhase = (phase: InterviewPhase): string | null => {
    if (propTemplates.length > 0) return null // Don't auto-assign if real templates exist
    
    // Auto-assign mock templates based on phase type for preview
    switch (phase.type) {
      case "screening":
        return mockTemplates.find(t => t.id === "mock-screening")?.id || null
      case "technical":
        // For system design phases, use system design template, otherwise use general technical
        if (phase.name.toLowerCase().includes("system") || phase.name.toLowerCase().includes("design")) {
          return mockTemplates.find(t => t.id === "mock-system-design")?.id || null
        }
        return mockTemplates.find(t => t.id === "mock-technical")?.id || null
      case "behavioral":
        return mockTemplates.find(t => t.id === "mock-behavioral")?.id || null
      case "cultural-fit":
        return mockTemplates.find(t => t.id === "mock-behavioral")?.id || null
      case "leadership":
        return mockTemplates.find(t => t.id === "mock-leadership")?.id || null
      case "final-round":
        return mockTemplates.find(t => t.id === "mock-final")?.id || null
      default:
        return null
    }
  }

  const [assignments, setAssignments] = React.useState<PhaseTemplateAssignment[]>(() => 
    phases.map(phase => ({
      phaseId: phase.id,
      templateId: phase.template?.id || getDefaultTemplateForPhase(phase)
    }))
  )

  const handleTemplateAssignment = (phaseId: string, templateId: string | null) => {
    const newAssignments = assignments.map(assignment => 
      assignment.phaseId === phaseId 
        ? { ...assignment, templateId }
        : assignment
    )
    setAssignments(newAssignments)
    onAssignmentsChange?.(newAssignments)
  }

  const getAssignedTemplate = (phaseId: string) => {
    const assignment = assignments.find(a => a.phaseId === phaseId)
    return assignment?.templateId ? templates.find(t => t.id === assignment.templateId) : null
  }

  const getRecommendedTemplates = (phaseType: InterviewPhaseType) => {
    return templates.filter(template => {
      switch (phaseType) {
        case "technical":
          return template.type === "Technical" || template.type === "Mixed"
        case "behavioral":
          return template.type === "Behavioral" || template.type === "Mixed"
        case "leadership":
          return template.type === "Leadership" || template.type === "Behavioral"
        case "cultural-fit":
          return template.type === "Behavioral" || template.type === "Custom"
        case "screening":
          return template.type === "Mixed" || template.type === "Technical" || template.type === "Behavioral"
        case "final-round":
          return template.type === "Mixed" || template.type === "Leadership"
        default:
          return templates
      }
    })
  }

  const getPhaseTypeColor = (type: InterviewPhaseType) => {
    switch (type) {
      case "screening": return "bg-blue-100 text-blue-800"
      case "technical": return "bg-green-100 text-green-800"
      case "behavioral": return "bg-purple-100 text-purple-800"
      case "cultural-fit": return "bg-orange-100 text-orange-800"
      case "leadership": return "bg-red-100 text-red-800"
      case "final-round": return "bg-gray-100 text-gray-800"
      case "post-offer": return "bg-yellow-100 text-yellow-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  const getAssignmentStatus = () => {
    const assignedCount = assignments.filter(a => a.templateId !== null).length
    const totalCount = assignments.length
    const requiredPhases = phases.filter(p => p.isRequired)
    const assignedRequiredCount = requiredPhases.filter(p => 
      assignments.find(a => a.phaseId === p.id)?.templateId !== null
    ).length

    return {
      assigned: assignedCount,
      total: totalCount,
      requiredAssigned: assignedRequiredCount,
      requiredTotal: requiredPhases.length,
      isComplete: assignedRequiredCount === requiredPhases.length
    }
  }

  const status = getAssignmentStatus()

  const handleContinue = () => {
    if (!status.isComplete) {
      alert(`Please assign templates to all required phases. ${status.requiredAssigned}/${status.requiredTotal} required phases have templates assigned.`)
      return
    }
    console.log("Template assignments:", assignments)
    onNext()
  }

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle>Assign Interview Templates to Phases</CardTitle>
        {/* <CardDescription>
          Choose which interview template to use for each phase of your interview process.
          {(propTemplates.length === 0 || propPhases.length === 0) && (
            <span className="block mt-2 text-amber-600 text-sm">
              📋 Preview mode: Showing sample {propPhases.length === 0 ? 'phases and ' : ''}templates for demonstration
            </span>
          )}
        </CardDescription> */}
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Progress Summary */}
        <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <span className="font-medium">Assignment Progress</span>
            </div>
            <div className="text-sm text-muted-foreground">
              {status.assigned} of {status.total} phases assigned
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant={status.isComplete ? "default" : "secondary"}>
              Required: {status.requiredAssigned}/{status.requiredTotal}
            </Badge>
            {(propTemplates.length === 0 || propPhases.length === 0) && templates.length > 0 && phases.length > 0 && (
              <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                Using {propPhases.length === 0 ? `${phases.length} sample phases & ` : ''}
                {templates.length} sample template{templates.length !== 1 ? 's' : ''}
              </Badge>
            )}
            {templates.length === 0 && (
              <Badge variant="destructive">
                No templates available
              </Badge>
            )}
          </div>
        </div>

        {phases.length === 0 ? (
          <Card className="p-6 text-center">
            <AlertTriangle className="h-12 w-12 text-blue-500 mx-auto mb-4" />
            <h3 className="font-semibold mb-2">No Interview Phases Configured</h3>
            <p className="text-sm text-muted-foreground mb-4">
              You need to configure interview phases first before you can assign templates to them.
            </p>
            <Button variant="outline" onClick={() => window.history.back()}>
              Go Back to Configure Phases
            </Button>
          </Card>
        ) : templates.length === 0 ? (
          <Card className="p-6 text-center">
            <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
            <h3 className="font-semibold mb-2">No Interview Templates Available</h3>
            <p className="text-sm text-muted-foreground mb-4">
              You need to create interview templates before you can assign them to phases.
            </p>
            <Button variant="outline" onClick={() => window.history.back()}>
              Go Back to Create Templates
            </Button>
          </Card>
        ) : (
          <div className="space-y-4">
            {/* Preview Mode Notice */}
            {/* {(propTemplates.length === 0 || propPhases.length === 0) && (
              <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg">
                <div className="flex items-start gap-3">
                  <FileText className="h-5 w-5 text-amber-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-amber-900">Preview Mode</h4>
                    <p className="text-sm text-amber-700 mt-1">
                      You're seeing sample {propPhases.length === 0 ? 'interview phases and ' : ''}templates to demonstrate the assignment interface. 
                      {propPhases.length === 0 ? ' Configure your own phases and create' : ' Create'} your own templates for actual use.
                    </p>
                  </div>
                </div>
              </div>
            )} */}
            
            <h3 className="text-lg font-semibold">Phase Template Assignments</h3>
            
            <div className="space-y-3">
              {phases.map((phase, index) => {
                const assignedTemplate = getAssignedTemplate(phase.id)
                const recommendedTemplates = getRecommendedTemplates(phase.type)
                const hasRecommendations = recommendedTemplates.length > 0

                return (
                  <Card key={phase.id} className="p-4">
                    <div className="space-y-4">
                      {/* Phase Header */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-medium text-muted-foreground">
                              Phase {index + 1}
                            </span>
                            {phase.isRequired && (
                              <Badge variant="outline" className="text-xs">Required</Badge>
                            )}
                          </div>
                          <ChevronRight className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <h4 className="font-medium">{phase.name}</h4>
                            <div className="flex items-center gap-2 mt-1">
                              <Badge className={getPhaseTypeColor(phase.type)} variant="secondary">
                                {getPhaseTypeLabel(phase.type)}
                              </Badge>
                              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                                <Clock className="h-3 w-3" />
                                {phase.estimatedDuration} min
                              </div>
                              {phase.startDate && phase.endDate && (
                                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                                  <Calendar className="h-3 w-3" />
                                  {new Date(phase.startDate).toLocaleDateString()} - {new Date(phase.endDate).toLocaleDateString()}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {assignedTemplate ? (
                            <CheckCircle className="h-5 w-5 text-green-600" />
                          ) : phase.isRequired ? (
                            <AlertTriangle className="h-5 w-5 text-yellow-500" />
                          ) : (
                            <Clock className="h-5 w-5 text-gray-400" />
                          )}
                        </div>
                      </div>

                      {/* Template Selection */}
                      <div className="grid gap-4 md:grid-cols-2">
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <Label htmlFor={`template-${phase.id}`}>
                              Interview Template {phase.isRequired && <span className="text-red-500">*</span>}
                            </Label>
                            {hasRecommendations && (
                              <Badge variant="outline" className="text-xs">
                                {recommendedTemplates.length} recommended
                              </Badge>
                            )}
                          </div>
                          <Select 
                            value={assignedTemplate?.id || "none"} 
                            onValueChange={(value) => handleTemplateAssignment(phase.id, value === "none" ? null : value)}
                          >
                            <SelectTrigger id={`template-${phase.id}`}>
                              <SelectValue placeholder="Select a template..." />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="none">No template</SelectItem>
                              {hasRecommendations && (
                                <>
                                  <div className="px-2 py-1.5 text-xs font-medium text-muted-foreground">
                                    Recommended
                                  </div>
                                  {recommendedTemplates.map(template => (
                                    <SelectItem key={template.id} value={template.id}>
                                      <div className="flex items-center gap-2">
                                        <span>{template.name}</span>
                                        <Badge variant="outline" className="text-xs">
                                          {template.type}
                                        </Badge>
                                      </div>
                                    </SelectItem>
                                  ))}
                                  {templates.length > recommendedTemplates.length && (
                                    <>
                                      <Separator className="my-1" />
                                      <div className="px-2 py-1.5 text-xs font-medium text-muted-foreground">
                                        Other Templates
                                      </div>
                                    </>
                                  )}
                                </>
                              )}
                              {templates.filter(t => !recommendedTemplates.includes(t)).map(template => (
                                <SelectItem key={template.id} value={template.id}>
                                  <div className="flex items-center gap-2">
                                    <span>{template.name}</span>
                                    <Badge variant="outline" className="text-xs">
                                      {template.type}
                                    </Badge>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        {/* Assigned Template Info */}
                        {assignedTemplate && (
                          <div className="p-3 bg-muted/50 rounded-lg">
                            <div className="flex items-start justify-between">
                              <div>
                                <h5 className="font-medium text-sm">{assignedTemplate.name}</h5>
                                <div className="flex items-center gap-2 mt-1">
                                  <Badge variant="outline" className="text-xs">
                                    {assignedTemplate.type}
                                  </Badge>
                                  <span className="text-xs text-muted-foreground">
                                    {assignedTemplate.questionCount} questions
                                  </span>
                                </div>
                                {assignedTemplate.role && assignedTemplate.level && (
                                  <p className="text-xs text-muted-foreground mt-1">
                                    {assignedTemplate.role} • {assignedTemplate.level}
                                  </p>
                                )}
                              </div>
                              <FileText className="h-4 w-4 text-muted-foreground" />
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Phase Description */}
                      {phase.description && (
                        <p className="text-sm text-muted-foreground">{phase.description}</p>
                      )}
                    </div>
                  </Card>
                )
              })}
            </div>

            {/* Summary */}
            <Card className="p-4 bg-muted/30">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">Assignment Summary</h4>
                  <p className="text-sm text-muted-foreground">
                    {status.assigned} templates assigned to {status.total} phases
                  </p>
                </div>
                <div className="flex items-center gap-3">
                  <div className="text-right">
                    <div className="text-sm font-medium">
                      {status.isComplete ? "Ready to Continue" : "Assignments Needed"}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {status.requiredAssigned}/{status.requiredTotal} required phases assigned
                    </div>
                  </div>
                  {status.isComplete ? (
                    <CheckCircle className="h-6 w-6 text-green-600" />
                  ) : (
                    <AlertTriangle className="h-6 w-6 text-yellow-500" />
                  )}
                </div>
              </div>
            </Card>
          </div>
        )}

        <Button onClick={handleContinue} className="w-full" disabled={!status.isComplete && templates.length > 0 && phases.length > 0}>
          {phases.length === 0 
            ? "Configure Phases First"
            : !status.isComplete && templates.length > 0 
            ? `Assign Templates to Required Phases (${status.requiredAssigned}/${status.requiredTotal})`
            : "Continue to Next Step"
          }
        </Button>
      </CardContent>
    </Card>
  )
}
