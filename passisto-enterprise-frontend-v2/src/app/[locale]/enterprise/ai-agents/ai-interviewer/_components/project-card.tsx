import Link from "next/link"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Button } from "@/components/ui/button"

type Project = {
  id: string
  name: string
  jobTitle?: string
  dateCreated: string
  numCandidates: number
  progress: number
  status: "In Progress" | "Complete" | "Draft"
}

export function ProjectCard({ project }: { project: Project }) {
  return (
    <Card className="flex flex-col justify-between">
      <CardHeader>
        <CardTitle>{project.name}</CardTitle>
        {project.jobTitle && <CardDescription>{project.jobTitle}</CardDescription>}
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="text-sm text-muted-foreground">
          <p>Created: {project.dateCreated}</p>
          <p>Candidates: {project.numCandidates}</p>
        </div>
        <div>
          <Progress value={project.progress} className="h-2" />
          <p className="mt-1 text-sm text-muted-foreground">Status: {project.status}</p>
        </div>
      </CardContent>
      <div className="p-4 pt-0">
        <Button asChild className="w-full">
          <Link href={`/projects/${project.id}`}>View Project</Link>
        </Button>
      </div>
    </Card>
  )
}
