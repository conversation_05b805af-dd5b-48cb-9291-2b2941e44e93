"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Download, XCircle, FileText } from "lucide-react"

type ProjectOverviewSectionProps = {
  projectId: string
}

type CandidateSummary = {
  name: string
  compatibilityScore: number
  interviewScore: number | null
  status: string
}

export function ProjectOverviewSection({ projectId }: ProjectOverviewSectionProps) {
  // Simulate data for the overview
  const totalCVs = 65
  const parsedCVs = 58
  const avgCompatibilityScore = 82.5

  const interviewStatus = {
    notInterviewed: 15,
    sent: 10,
    done: 25,
    rejected: 5,
    accepted: 3,
  }

  const topCandidates: CandidateSummary[] = [
    { name: "<PERSON>", compatibilityScore: 92, interviewScore: 85, status: "Done" },
    { name: "<PERSON>", compatibilityScore: 95, interviewScore: null, status: "<PERSON><PERSON>" },
    { name: "<PERSON>", compatibilityScore: 80, interviewScore: 90, status: "Accepted" },
  ]

  const handleCloseProject = () => console.log(`Closing project ${projectId}`)
  // Logic to mark project as closed

  const handleExportReport = () => console.log(`Exporting report for project ${projectId}`)
  // Logic to generate and download report

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle>Project Overview</CardTitle>
        <CardDescription>A summary of your recruitment project's progress and key metrics.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-8">
        <div className="grid gap-4 md:grid-cols-2">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total CVs Uploaded</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalCVs}</div>
              <p className="text-xs text-muted-foreground">{parsedCVs} parsed successfully</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg. Compatibility Score</CardTitle>
              <Progress value={avgCompatibilityScore} className="h-4 w-1/2" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{avgCompatibilityScore.toFixed(1)}%</div>
              <p className="text-xs text-muted-foreground">Based on parsed CVs</p>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Interview Status Breakdown</h3>
          <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-5">
            {Object.entries(interviewStatus).map(([status, count]) => (
              <Card key={status}>
                <CardHeader className="pb-2">
                  <CardDescription className="capitalize">{status.replace(/([A-Z])/g, " $1")}</CardDescription>
                  <CardTitle className="text-xl">{count}</CardTitle>
                </CardHeader>
              </Card>
            ))}
          </div>
          <div className="h-48 w-full rounded-md border bg-muted flex items-center justify-center text-muted-foreground">
            {/* Placeholder for Interview Status Chart (Pie or Bar) */}
            Interview Status Chart Placeholder
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Top Candidates</h3>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Compatibility</TableHead>
                <TableHead>Interview Score</TableHead>
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {topCandidates.map((candidate, index) => (
                <TableRow key={index}>
                  <TableCell className="font-medium">{candidate.name}</TableCell>
                  <TableCell>{candidate.compatibilityScore}%</TableCell>
                  <TableCell>{candidate.interviewScore !== null ? `${candidate.interviewScore}%` : "N/A"}</TableCell>
                  <TableCell>{candidate.status}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={handleExportReport}>
            <Download className="mr-2 h-4 w-4" />
            Export Report
          </Button>
          <Button variant="destructive" onClick={handleCloseProject}>
            <XCircle className="mr-2 h-4 w-4" />
            Close Project
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
