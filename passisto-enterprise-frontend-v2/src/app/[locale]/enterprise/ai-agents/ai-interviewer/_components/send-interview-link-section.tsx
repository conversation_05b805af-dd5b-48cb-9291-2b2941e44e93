"use client"

import * as React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { Mail, Send } from "lucide-react"

type Candidate = {
  id: string
  name: string
  compatibilityScore: number
  contact: string
  status: "Not Interviewed" | "Sent" | "Done" | "Rejected" | "Accepted"
}

// Simulate candidates that have been "parsed" from CVs
const parsedCandidates: Candidate[] = [
  {
    id: "cand-1",
    name: "<PERSON>",
    compatibilityScore: 92,
    contact: "<EMAIL>",
    status: "Not Interviewed",
  },
  {
    id: "cand-2",
    name: "<PERSON>",
    compatibilityScore: 88,
    contact: "<EMAIL>",
    status: "Not Interviewed",
  },
  {
    id: "cand-3",
    name: "<PERSON>",
    compatibilityScore: 75,
    contact: "<EMAIL>",
    status: "Not Interviewed",
  },
  {
    id: "cand-4",
    name: "<PERSON> <PERSON>",
    compatibilityScore: 95,
    contact: "<EMAIL>",
    status: "Not Interviewed",
  },
  { id: "cand-5", name: "Eve Adams", compatibilityScore: 80, contact: "<EMAIL>", status: "Not Interviewed" },
  {
    id: "cand-6",
    name: "Frank Green",
    compatibilityScore: 60,
    contact: "<EMAIL>",
    status: "Not Interviewed",
  },
  {
    id: "cand-7",
    name: "Grace Hall",
    compatibilityScore: 70,
    contact: "<EMAIL>",
    status: "Not Interviewed",
  },
]

type SendInterviewLinkSectionProps = {
  projectId: string
  onNext: () => void
}

export function SendInterviewLinkSection({ projectId, onNext }: SendInterviewLinkSectionProps) {
  const [minCompatibility, setMinCompatibility] = React.useState<number[]>([70])
  const [searchName, setSearchName] = React.useState("")
  const [selectedCandidates, setSelectedCandidates] = React.useState<string[]>([])

  const filteredCandidates = React.useMemo(() => {
    return parsedCandidates.filter(
      (candidate) =>
        candidate.compatibilityScore >= minCompatibility[0] &&
        candidate.name.toLowerCase().includes(searchName.toLowerCase()) &&
        candidate.status === "Not Interviewed", // Only show candidates not yet interviewed
    )
  }, [minCompatibility, searchName])

  const handleSelectCandidate = (candidateId: string, checked: boolean) => {
    setSelectedCandidates((prev) => (checked ? [...prev, candidateId] : prev.filter((id) => id !== candidateId)))
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedCandidates(filteredCandidates.map((c) => c.id))
    } else {
      setSelectedCandidates([])
    }
  }

  const handleSendLinks = () => {
    console.log(`Sending interview links to selected candidates for project ${projectId}:`, selectedCandidates)
    // In a real app, you'd trigger an API call to send emails/links
    alert(`Interview links sent to ${selectedCandidates.length} candidates!`)
    onNext() // Move to the next step after sending links
  }

  const allFilteredCandidatesSelected =
    filteredCandidates.length > 0 && selectedCandidates.length === filteredCandidates.length

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle>Send Interview Links</CardTitle>
        <CardDescription>
          Review parsed candidates and send interview invitations to those who meet your criteria.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div className="grid gap-2">
            <Label htmlFor="min-compatibility">Minimum Compatibility Score: {minCompatibility[0]}%</Label>
            <Slider
              id="min-compatibility"
              min={0}
              max={100}
              step={1}
              value={minCompatibility}
              onValueChange={(val) => setMinCompatibility(val as number[])}
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="search-name">Search by Name</Label>
            <Input
              id="search-name"
              placeholder="Search candidate name..."
              value={searchName}
              onChange={(e) => setSearchName(e.target.value)}
            />
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Candidates Ready for Interview</h3>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[50px]">
                  <input
                    type="checkbox"
                    className="form-checkbox h-4 w-4 text-primary"
                    checked={allFilteredCandidatesSelected}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                  />
                </TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Compatibility Score</TableHead>
                <TableHead>Contact</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredCandidates.length > 0 ? (
                filteredCandidates.map((candidate) => (
                  <TableRow key={candidate.id}>
                    <TableCell>
                      <input
                        type="checkbox"
                        className="form-checkbox h-4 w-4 text-primary"
                        checked={selectedCandidates.includes(candidate.id)}
                        onChange={(e) => handleSelectCandidate(candidate.id, e.target.checked)}
                      />
                    </TableCell>
                    <TableCell className="font-medium">{candidate.name}</TableCell>
                    <TableCell>
                      <Badge variant={candidate.compatibilityScore >= 80 ? "default" : "secondary"}>
                        {candidate.compatibilityScore}%
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Mail className="mr-1 inline-block h-3 w-3" />
                      {candidate.contact}
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={4} className="h-24 text-center text-muted-foreground">
                    No candidates found matching your criteria.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        <Button onClick={handleSendLinks} className="w-full" disabled={selectedCandidates.length === 0}>
          <Send className="mr-2 h-4 w-4" />
          Send Interview Links ({selectedCandidates.length})
        </Button>
      </CardContent>
    </Card>
  )
}
