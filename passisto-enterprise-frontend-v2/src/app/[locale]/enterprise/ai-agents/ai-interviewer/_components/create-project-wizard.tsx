"use client"

import * as React from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { cn } from "@/lib/utils"
import { Check } from "lucide-react"

type ProjectFormData = {
  projectName: string
  jobTitle: string
  location: string
  jobDescription: string
}

const initialFormData: ProjectFormData = {
  projectName: "",
  jobTitle: "",
  location: "",
  jobDescription: "",
}

const steps = [
  { id: "basic-info", title: "Basic Information" },
  { id: "job-description", title: "Job Description" },
  { id: "review", title: "Review & Create" },
]

export function CreateProjectWizard() {
  const [currentStep, setCurrentStep] = React.useState(0)
  const [formData, setFormData] = React.useState<ProjectFormData>(initialFormData)
  const router = useRouter()

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target
    setFormData((prev) => ({ ...prev, [id]: value }))
  }

  const handleNext = () => {
    // Basic validation for current step before proceeding
    if (currentStep === 0) {
      if (!formData.projectName || !formData.jobTitle || !formData.location) {
        alert("Please fill in all basic information fields.")
        return
      }
    } else if (currentStep === 1) {
      if (!formData.jobDescription) {
        alert("Please provide a job description.")
        return
      }
    }
    setCurrentStep((prev) => prev + 1)
  }

  const handlePrevious = () => {
    setCurrentStep((prev) => prev - 1)
  }

  const handleSubmit = () => {
    console.log("Creating project with data:", formData)
    // Simulate project creation
    const newProjectId = `proj-${Date.now()}`
    alert(`Project "${formData.projectName}" created successfully!`)
    router.push(`/projects/${newProjectId}?step=job-offer`) // Redirect to the new project's workflow
  }

  const renderStepContent = () => {
    switch (steps[currentStep].id) {
      case "basic-info":
        return (
          <div className="grid gap-6">
            <div className="grid gap-2">
              <Label htmlFor="projectName">Project Name</Label>
              <Input
                id="projectName"
                placeholder="e.g., Senior Software Engineer Hiring"
                value={formData.projectName}
                onChange={handleChange}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="jobTitle">Job Title</Label>
              <Input
                id="jobTitle"
                placeholder="e.g., Senior Software Engineer"
                value={formData.jobTitle}
                onChange={handleChange}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="location">Location</Label>
              <Input
                id="location"
                placeholder="e.g., San Francisco, USA"
                value={formData.location}
                onChange={handleChange}
                required
              />
            </div>
          </div>
        )
      case "job-description":
        return (
          <div className="grid gap-6">
            <div className="grid gap-2">
              <Label htmlFor="jobDescription">Job Description</Label>
              <Textarea
                id="jobDescription"
                placeholder="Provide a detailed job description. Our AI will help extract key requirements from this."
                rows={10}
                value={formData.jobDescription}
                onChange={handleChange}
                required
              />
              <p className="text-sm text-muted-foreground">
                This description will be used by our AI to extract skills, experience, and education requirements.
              </p>
            </div>
          </div>
        )
      case "review":
        return (
          <div className="grid gap-4">
            <h3 className="text-lg font-semibold">Review Project Details</h3>
            <div className="grid gap-2">
              <p className="text-sm font-medium">Project Name:</p>
              <p className="text-muted-foreground">{formData.projectName || "N/A"}</p>
            </div>
            <div className="grid gap-2">
              <p className="text-sm font-medium">Job Title:</p>
              <p className="text-muted-foreground">{formData.jobTitle || "N/A"}</p>
            </div>
            <div className="grid gap-2">
              <p className="text-sm font-medium">Location:</p>
              <p className="text-muted-foreground">{formData.location || "N/A"}</p>
            </div>
            <div className="grid gap-2">
              <p className="text-sm font-medium">Job Description:</p>
              <p className="text-muted-foreground line-clamp-6">{formData.jobDescription || "N/A"}</p>
            </div>
          </div>
        )
      default:
        return null
    }
  }

  return (
    <Card className="w-full max-w-7xl shadow-lg">
      <CardHeader className="pb-6">
        <CardTitle className="text-2xl font-bold">Create New Project</CardTitle>
        <CardDescription>Follow the steps below to set up your new recruitment project.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-8">
        {/* Stepper Progress */}
        <div className="flex items-center justify-between space-x-4">
          {steps.map((step, index) => (
            <React.Fragment key={step.id}>
              <div className="flex flex-col items-center">
                <div
                  className={cn(
                    "flex h-10 w-10 items-center justify-center rounded-full border-2",
                    index === currentStep
                      ? "border-foreground bg-foreground text-background"
                      : index < currentStep
                        ? "border-foreground bg-foreground text-background"
                        : "border-muted-foreground text-muted-foreground",
                  )}
                >
                  {index < currentStep ? <Check className="h-5 w-5" /> : index + 1}
                </div>
                <span
                  className={cn(
                    "mt-2 whitespace-nowrap text-center text-sm",
                    index === currentStep ? "font-semibold text-foreground" : "text-muted-foreground",
                  )}
                >
                  {step.title}
                </span>
              </div>
              {index < steps.length - 1 && (
                <div
                  className={cn(
                    "h-0.5 flex-1 rounded-full",
                    index < currentStep ? "bg-foreground" : "bg-muted-foreground/30",
                  )}
                />
              )}
            </React.Fragment>
          ))}
        </div>

        {/* Step Content */}
        <div className="min-h-[250px]">{renderStepContent()}</div>

        {/* Navigation Buttons */}
        <div className="flex justify-between">
          <Button onClick={handlePrevious} disabled={currentStep === 0} variant="outline">
            Previous
          </Button>
          {currentStep < steps.length - 1 ? (
            <Button onClick={handleNext}>Next</Button>
          ) : (
            <Button onClick={handleSubmit}>Create Project</Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
