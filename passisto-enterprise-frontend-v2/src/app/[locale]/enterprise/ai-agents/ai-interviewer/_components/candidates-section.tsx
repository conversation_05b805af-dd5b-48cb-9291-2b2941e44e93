"use client"

import * as React from "react"
import { ChevronDown, Download, Mail, Send } from "lucide-react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Checkbox } from "@/components/ui/checkbox"
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card"
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { MoreHorizontal } from "lucide-react"
// import { CandidateProfileModal } from "@/components/candidate-profile-modal"
import { InterviewPhase } from "@/lib/utils"
import { CandidateProfileModal } from "./candidate-profile-modal"

type Candidate = {
  id: string
  name: string
  age: number
  location: string
  experience: number
  compatibilityScore: number
  interviewScore: number | null
  status: "Not Interviewed" | "Sent" | "Done" | "Rejected" | "Accepted"
  skills: string[]
  contact: string
  cvText: string
}

const initialCandidates: Candidate[] = [
  {
    id: "cand-1",
    name: "Alice Johnson",
    age: 28,
    location: "London, UK",
    experience: 5,
    compatibilityScore: 92,
    interviewScore: 85,
    status: "Done",
    skills: ["React", "Node.js", "AWS", "TypeScript"],
    contact: "<EMAIL>",
    cvText: "Experienced software engineer with 5 years in web development...",
  },
  {
    id: "cand-2",
    name: "Bob Williams",
    age: 35,
    location: "New York, USA",
    experience: 10,
    compatibilityScore: 88,
    interviewScore: null,
    status: "Not Interviewed",
    skills: ["Python", "Machine Learning", "Data Science"],
    contact: "<EMAIL>",
    cvText: "Data scientist with a strong background in AI and ML...",
  },
  {
    id: "cand-3",
    name: "Charlie Brown",
    age: 24,
    location: "Berlin, Germany",
    experience: 2,
    compatibilityScore: 75,
    interviewScore: 70,
    status: "Rejected",
    skills: ["JavaScript", "HTML", "CSS"],
    contact: "<EMAIL>",
    cvText: "Junior developer eager to learn and grow...",
  },
  {
    id: "cand-4",
    name: "Diana Prince",
    age: 31,
    location: "Paris, France",
    experience: 7,
    compatibilityScore: 95,
    interviewScore: null,
    status: "Sent",
    skills: ["Vue.js", "Firebase", "UI/UX Design"],
    contact: "<EMAIL>",
    cvText: "Product designer with a passion for user-centric design...",
  },
  {
    id: "cand-5",
    name: "Eve Adams",
    age: 40,
    location: "Sydney, Australia",
    experience: 15,
    compatibilityScore: 80,
    interviewScore: 90,
    status: "Accepted",
    skills: ["Project Management", "Leadership", "Agile"],
    contact: "<EMAIL>",
    cvText: "Senior leader with extensive experience in managing large teams...",
  },
]

type CandidatesSectionProps = {
  projectId: string
  phases?: InterviewPhase[]
  onNext: () => void // Add onNext prop
}

export function CandidatesSection({ projectId, phases = [], onNext }: CandidatesSectionProps) {
  // Mock phases for preview when no phases are provided
  const mockPhases: InterviewPhase[] = [
    {
      id: "mock-phase-1",
      name: "Initial Screening",
      type: "screening",
      description: "Initial phone/video screening",
      order: 1,
      isRequired: true,
      estimatedDuration: 30,
      startDate: "",
      endDate: "",
      configuration: { allowSkip: false, requiresApproval: true, autoAdvance: false, passingScore: 70, maxAttempts: 1 },
      status: "not-started",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: "mock-phase-2", 
      name: "Technical Assessment",
      type: "technical",
      description: "Coding assessment",
      order: 2,
      isRequired: true,
      estimatedDuration: 90,
      startDate: "",
      endDate: "",
      configuration: { allowSkip: false, requiresApproval: true, autoAdvance: false, passingScore: 75, maxAttempts: 1 },
      status: "not-started",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: "mock-phase-3",
      name: "Final Interview",
      type: "final-round", 
      description: "Final interview with team",
      order: 3,
      isRequired: true,
      estimatedDuration: 60,
      startDate: "",
      endDate: "",
      configuration: { allowSkip: false, requiresApproval: true, autoAdvance: false, passingScore: 70, maxAttempts: 1 },
      status: "not-started",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ]
  const [candidates, setCandidates] = React.useState<Candidate[]>(initialCandidates)
  const [ageRange, setAgeRange] = React.useState<[number, number]>([18, 60])
  const [locationFilter, setLocationFilter] = React.useState("")
  const [experienceRange, setExperienceRange] = React.useState<[number, number]>([0, 20])
  const [compatibilityScoreRange, setCompatibilityScoreRange] = React.useState<[number, number]>([0, 100])
  const [interviewScoreRange, setInterviewScoreRange] = React.useState<[number, number]>([0, 100])
  const [statusFilters, setStatusFilters] = React.useState<Candidate["status"][]>([])

  const [selectedCandidate, setSelectedCandidate] = React.useState<Candidate | null>(null)
  const [isProfileModalOpen, setIsProfileModalOpen] = React.useState(false)
  
  // State for bulk interview link sending
  const [selectedCandidatesForInterview, setSelectedCandidatesForInterview] = React.useState<string[]>([])
  const [showBulkInterviewSection, setShowBulkInterviewSection] = React.useState(false)
  const [selectedPhaseForInterview, setSelectedPhaseForInterview] = React.useState<string>("")

  const filteredCandidates = React.useMemo(() => {
    return candidates.filter((candidate) => {
      const matchesAge = candidate.age >= ageRange[0] && candidate.age <= ageRange[1]
      const matchesLocation = locationFilter
        ? candidate.location.toLowerCase().includes(locationFilter.toLowerCase())
        : true
      const matchesExperience = candidate.experience >= experienceRange[0] && candidate.experience <= experienceRange[1]
      const matchesCompatibility =
        candidate.compatibilityScore >= compatibilityScoreRange[0] &&
        candidate.compatibilityScore <= compatibilityScoreRange[1]
      const matchesInterviewScore =
        candidate.interviewScore === null ||
        (candidate.interviewScore >= interviewScoreRange[0] && candidate.interviewScore <= interviewScoreRange[1])
      const matchesStatus = statusFilters.length > 0 ? statusFilters.includes(candidate.status) : true

      return (
        matchesAge &&
        matchesLocation &&
        matchesExperience &&
        matchesCompatibility &&
        matchesInterviewScore &&
        matchesStatus
      )
    })
  }, [
    candidates,
    ageRange,
    locationFilter,
    experienceRange,
    compatibilityScoreRange,
    interviewScoreRange,
    statusFilters,
  ])

  const handleSendInterview = (candidateId: string) => {
    setCandidates((prev) => prev.map((c) => (c.id === candidateId ? { ...c, status: "Sent" } : c)))
    console.log(`Sending interview to candidate ${candidateId} for project ${projectId}`)
  }

  const handleViewProfile = (candidate: Candidate) => {
    setSelectedCandidate(candidate)
    setIsProfileModalOpen(true)
  }

  const handleUpdateCandidateStatus = (candidateId: string, newStatus: Candidate["status"]) => {
    setCandidates((prev) => prev.map((c) => (c.id === candidateId ? { ...c, status: newStatus } : c)))
    console.log(`Updated status for candidate ${candidateId} to ${newStatus}`)
  }

  const handleStatusFilterChange = (status: Candidate["status"], checked: boolean) => {
    setStatusFilters((prev) => (checked ? [...prev, status] : prev.filter((s) => s !== status)))
  }

  const allStatuses: Candidate["status"][] = ["Not Interviewed", "Sent", "Done", "Rejected", "Accepted"]

  const handleContinue = () => {
    console.log("Continuing to next step from Candidates.")
    onNext()
  }

  // Get candidates ready for interview (status: "Not Interviewed")
  const candidatesReadyForInterview = React.useMemo(() => {
    return filteredCandidates.filter(candidate => candidate.status === "Not Interviewed")
  }, [filteredCandidates])

  const handleSelectCandidateForInterview = (candidateId: string, checked: boolean) => {
    setSelectedCandidatesForInterview(prev => 
      checked ? [...prev, candidateId] : prev.filter(id => id !== candidateId)
    )
  }

  const handleSelectAllForInterview = (checked: boolean) => {
    if (checked) {
      setSelectedCandidatesForInterview(candidatesReadyForInterview.map(c => c.id))
    } else {
      setSelectedCandidatesForInterview([])
    }
  }

  const handleBulkSendInterviews = () => {
    if (!selectedPhaseForInterview) {
      alert("Please select an interview phase before sending links.")
      return
    }

    const selectedPhase = (phases.length > 0 ? phases : mockPhases).find(p => p.id === selectedPhaseForInterview)
    const phaseName = selectedPhase?.name || "Unknown Phase"

    // Update status of selected candidates to "Sent"
    setCandidates(prev => prev.map(candidate => 
      selectedCandidatesForInterview.includes(candidate.id) 
        ? { ...candidate, status: "Sent" as const }
        : candidate
    ))
    console.log(`Sending interview links for "${phaseName}" to ${selectedCandidatesForInterview.length} candidates for project ${projectId}:`, selectedCandidatesForInterview)
    alert(`Interview links for "${phaseName}" sent to ${selectedCandidatesForInterview.length} candidates!`)
    setSelectedCandidatesForInterview([])
    setSelectedPhaseForInterview("")
    setShowBulkInterviewSection(false)
  }

  const allCandidatesForInterviewSelected = candidatesReadyForInterview.length > 0 && 
    selectedCandidatesForInterview.length === candidatesReadyForInterview.length

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Candidate List</CardTitle>
        <CardDescription>Manage and filter candidates for this project.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
          <div className="grid gap-2">
            <Label htmlFor="age-range">Age Range</Label>
            <div className="flex gap-2">
              <Input
                id="age-min"
                type="number"
                placeholder="Min"
                min={18}
                max={60}
                value={ageRange[0]}
                onChange={(e) => setAgeRange([parseInt(e.target.value) || 18, ageRange[1]])}
                className="w-20"
              />
              <span className="self-center text-sm text-muted-foreground">to</span>
              <Input
                id="age-max"
                type="number"
                placeholder="Max"
                min={18}
                max={60}
                value={ageRange[1]}
                onChange={(e) => setAgeRange([ageRange[0], parseInt(e.target.value) || 60])}
                className="w-20"
              />
            </div>
          </div>
          <div className="grid gap-2">
            <Label htmlFor="location-filter">Location</Label>
            <Input
              id="location-filter"
              placeholder="e.g., London"
              value={locationFilter}
              onChange={(e) => setLocationFilter(e.target.value)}
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="experience-range">Experience Range (years)</Label>
            <div className="flex gap-2">
              <Input
                id="experience-min"
                type="number"
                placeholder="Min"
                min={0}
                max={20}
                value={experienceRange[0]}
                onChange={(e) => setExperienceRange([parseInt(e.target.value) || 0, experienceRange[1]])}
                className="w-20"
              />
              <span className="self-center text-sm text-muted-foreground">to</span>
              <Input
                id="experience-max"
                type="number"
                placeholder="Max"
                min={0}
                max={20}
                value={experienceRange[1]}
                onChange={(e) => setExperienceRange([experienceRange[0], parseInt(e.target.value) || 20])}
                className="w-20"
              />
            </div>
          </div>
          <div className="grid gap-2">
            <Label htmlFor="compatibility-score-range">Compatibility Score (%)</Label>
            <div className="flex gap-2">
              <Input
                id="compatibility-min"
                type="number"
                placeholder="Min"
                min={0}
                max={100}
                value={compatibilityScoreRange[0]}
                onChange={(e) => setCompatibilityScoreRange([parseInt(e.target.value) || 0, compatibilityScoreRange[1]])}
                className="w-20"
              />
              <span className="self-center text-sm text-muted-foreground">to</span>
              <Input
                id="compatibility-max"
                type="number"
                placeholder="Max"
                min={0}
                max={100}
                value={compatibilityScoreRange[1]}
                onChange={(e) => setCompatibilityScoreRange([compatibilityScoreRange[0], parseInt(e.target.value) || 100])}
                className="w-20"
              />
            </div>
          </div>
          <div className="grid gap-2">
            <Label htmlFor="interview-score-range">Interview Score (%)</Label>
            <div className="flex gap-2">
              <Input
                id="interview-min"
                type="number"
                placeholder="Min"
                min={0}
                max={100}
                value={interviewScoreRange[0]}
                onChange={(e) => setInterviewScoreRange([parseInt(e.target.value) || 0, interviewScoreRange[1]])}
                className="w-20"
              />
              <span className="self-center text-sm text-muted-foreground">to</span>
              <Input
                id="interview-max"
                type="number"
                placeholder="Max"
                min={0}
                max={100}
                value={interviewScoreRange[1]}
                onChange={(e) => setInterviewScoreRange([interviewScoreRange[0], parseInt(e.target.value) || 100])}
                className="w-20"
              />
            </div>
          </div>
          <div className="grid gap-2">
            <Label>Interview Status</Label>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full justify-between bg-transparent">
                  {statusFilters.length > 0 ? `Selected (${statusFilters.length})` : "Filter by Status"}
                  <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56">
                {allStatuses.map((status) => (
                  <DropdownMenuItem key={status} onSelect={(e) => e.preventDefault()}>
                    <Checkbox
                      id={`status-${status}`}
                      checked={statusFilters.includes(status)}
                      onCheckedChange={(checked) => handleStatusFilterChange(status, checked as boolean)}
                    />
                    <Label htmlFor={`status-${status}`} className="ml-2 cursor-pointer">
                      {status}
                    </Label>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        <div className="flex justify-between items-center">
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="mr-2 h-4 w-4" />
              Export to CSV
            </Button>
            {candidatesReadyForInterview.length > 0 && (
              <Button 
                variant="outline" 
                onClick={() => setShowBulkInterviewSection(!showBulkInterviewSection)}
                className="bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100"
              >
                <Mail className="mr-2 h-4 w-4" />
                Send Interview Links ({candidatesReadyForInterview.length})
              </Button>
            )}
          </div>
        </div>

        {/* Bulk Interview Link Section */}
        {showBulkInterviewSection && candidatesReadyForInterview.length > 0 && (
          <Card className="border-blue-200 bg-blue-50/50">
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Mail className="h-5 w-5 text-blue-600" />
                Send Interview Links
              </CardTitle>
              <CardDescription>
                Select candidates to send interview invitations to. Only candidates with "Not Interviewed" status are shown.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Phase Selection */}
              <div className="space-y-2">
                <Label htmlFor="interview-phase">Select Interview Phase *</Label>
                <Select 
                  value={selectedPhaseForInterview} 
                  onValueChange={setSelectedPhaseForInterview}
                >
                  <SelectTrigger id="interview-phase">
                    <SelectValue placeholder="Choose which phase to send interview links for..." />
                  </SelectTrigger>
                  <SelectContent>
                    {(phases.length > 0 ? phases : mockPhases).map(phase => (
                      <SelectItem key={phase.id} value={phase.id}>
                        <div className="flex items-center gap-2">
                          <span>Phase {phase.order}: {phase.name}</span>
                          <Badge variant="outline" className="text-xs">
                            {phase.estimatedDuration}min
                          </Badge>
                          {phase.isRequired && (
                            <Badge variant="outline" className="text-xs bg-orange-50 text-orange-700">
                              Required
                            </Badge>
                          )}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {selectedPhaseForInterview && (
                  <div className="text-sm text-muted-foreground bg-blue-50 p-2 rounded border border-blue-200">
                    <strong>Selected Phase:</strong> {(phases.length > 0 ? phases : mockPhases).find(p => p.id === selectedPhaseForInterview)?.name}
                    {(phases.length > 0 ? phases : mockPhases).find(p => p.id === selectedPhaseForInterview)?.description && (
                      <div className="mt-1">
                        <em>{(phases.length > 0 ? phases : mockPhases).find(p => p.id === selectedPhaseForInterview)?.description}</em>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Candidate Selection */}
              <div className="space-y-2">
                <Label>Select Candidates</Label>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[50px]">
                      <Checkbox
                        checked={allCandidatesForInterviewSelected}
                        onCheckedChange={handleSelectAllForInterview}
                      />
                    </TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Compatibility Score</TableHead>
                    <TableHead>Contact</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {candidatesReadyForInterview.map((candidate) => (
                    <TableRow key={candidate.id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedCandidatesForInterview.includes(candidate.id)}
                          onCheckedChange={(checked) => handleSelectCandidateForInterview(candidate.id, checked as boolean)}
                        />
                      </TableCell>
                      <TableCell className="font-medium">{candidate.name}</TableCell>
                      <TableCell>
                        <Badge variant={candidate.compatibilityScore >= 80 ? "default" : "secondary"}>
                          {candidate.compatibilityScore}%
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <span className="flex items-center gap-1">
                          <Mail className="h-3 w-3" />
                          {candidate.contact}
                        </span>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              </div>
              
              <div className="flex justify-between items-center pt-4 border-t">
                <div className="text-sm text-muted-foreground">
                  {selectedCandidatesForInterview.length} of {candidatesReadyForInterview.length} candidates selected
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" onClick={() => setShowBulkInterviewSection(false)}>
                    Cancel
                  </Button>
                  <Button 
                    onClick={handleBulkSendInterviews} 
                    disabled={selectedCandidatesForInterview.length === 0 || !selectedPhaseForInterview}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    <Send className="mr-2 h-4 w-4" />
                    Send Interview Links ({selectedCandidatesForInterview.length})
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <div className="space-y-4">
          <h3 className="text-lg font-semibold">All Candidates</h3>

        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Age</TableHead>
              <TableHead>Location</TableHead>
              <TableHead>Experience</TableHead>
              <TableHead>Compatibility Score</TableHead>
              <TableHead>Interview Score</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredCandidates.length > 0 ? (
              filteredCandidates.map((candidate) => (
                <TableRow key={candidate.id}>
                  <TableCell className="font-medium">{candidate.name}</TableCell>
                  <TableCell>{candidate.age}</TableCell>
                  <TableCell>{candidate.location}</TableCell>
                  <TableCell>{candidate.experience} yrs</TableCell>
                  <TableCell>
                    <Badge variant={candidate.compatibilityScore >= 80 ? "default" : "secondary"}>
                      {candidate.compatibilityScore}%
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {candidate.interviewScore !== null ? (
                      <Badge variant={candidate.interviewScore >= 70 ? "default" : "secondary"}>
                        {candidate.interviewScore}%
                      </Badge>
                    ) : (
                      "N/A"
                    )}
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={
                        candidate.status === "Accepted"
                          ? "default"
                          : candidate.status === "Rejected"
                            ? "destructive"
                            : "outline"
                      }
                    >
                      {candidate.status}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleViewProfile(candidate)}>View Profile</DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleSendInterview(candidate.id)}
                          disabled={candidate.status !== "Not Interviewed"}
                        >
                          Send Interview
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        {allStatuses.map((status) => (
                          <DropdownMenuItem
                            key={status}
                            onClick={() => handleUpdateCandidateStatus(candidate.id, status)}
                            disabled={candidate.status === status}
                          >
                            Set Status: {status}
                          </DropdownMenuItem>
                        ))}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={8} className="h-24 text-center text-muted-foreground">
                  No candidates found matching your filters.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
        </div>

        {selectedCandidate && (
          <CandidateProfileModal
            open={isProfileModalOpen}
            onOpenChange={setIsProfileModalOpen}
            candidate={selectedCandidate}
            onSendInterview={handleSendInterview}
            onUpdateStatus={handleUpdateCandidateStatus}
          />
        )}
        <Button onClick={handleContinue} className="w-full mt-4">
          Continue
        </Button>
      </CardContent>
    </Card>
  )
}
