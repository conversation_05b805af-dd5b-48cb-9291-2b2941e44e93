"use client"

import { Label } from "@/components/ui/label"
import * as React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { MoreHorizontal, Sparkles, Settings, CheckCircle, Clock, AlertTriangle, FileText, Mail, Code, User, Clock3, Building, Plus, Edit, Trash2, X, Save } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { cn, Interview<PERSON><PERSON>e, InterviewPhaseType, getPhaseType<PERSON>abel } from "@/lib/utils"

type InterviewQuestion = {
  id: string
  question: string
  type: "Technical" | "Behavioral" | "Problem-Solving" | "System Design"
  difficulty: "Easy" | "Medium" | "Hard"
  category: string
}

type InterviewTemplate = {
  id: string
  name: string
  type: "Technical" | "Behavioral" | "Mixed" | "Custom" | "Sales" | "Leadership"
  dateModified: string
  // New fields for creation
  role?: string
  level?: string
  techStack?: string
  questionCount?: number
  questions?: InterviewQuestion[]
  phases?: InterviewPhaseType[] // Which phases this template applies to
}

type EmailTemplate = {
  id: string
  name: string
  subject: string
  body: string
  platform: "HackerRank" | "Codility" | "LeetCode" | "Custom" | "Generic"
  dateModified: string
  // Email configuration
  isActive: boolean
  variables: string[] // Available variables for injection
}

type InterviewTemplateSectionProps = {
  projectId: string
  onNext: () => void
}

export function InterviewTemplateSection({ projectId, onNext }: InterviewTemplateSectionProps) {
  const [templates, setTemplates] = React.useState<InterviewTemplate[]>([])
  const [emailTemplates, setEmailTemplates] = React.useState<EmailTemplate[]>([])
  const [viewMode, setViewMode] = React.useState<"create" | "email">("create")

  const [newTemplateData, setNewTemplateData] = React.useState({
    role: "",
    level: "",
    type: "Technical" as InterviewTemplate["type"],
    techStack: "",
    questionCount: 0,
    questions: [] as InterviewQuestion[],
  })

  const [newEmailTemplateData, setNewEmailTemplateData] = React.useState({
    name: "",
    subject: "",
    body: "",
    platform: "Generic" as EmailTemplate["platform"],
  })

  const [isGeneratingQuestions, setIsGeneratingQuestions] = React.useState(false)
  const [showQuestions, setShowQuestions] = React.useState(false)
  const [editingQuestion, setEditingQuestion] = React.useState<InterviewQuestion | null>(null)
  const [newQuestion, setNewQuestion] = React.useState({
    question: "",
    type: "Technical" as InterviewQuestion["type"],
    difficulty: "Medium" as InterviewQuestion["difficulty"],
    category: "",
  })

  const handleNewTemplateChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target
    setNewTemplateData((prev) => ({ ...prev, [id]: value }))
  }

  const handleNewTemplateTypeChange = (value: InterviewTemplate["type"]) => {
    setNewTemplateData((prev) => ({ ...prev, type: value }))
  }

  const handleNewEmailTemplateChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target
    setNewEmailTemplateData((prev) => ({ ...prev, [id]: value }))
  }

  const handleNewEmailTemplatePlatformChange = (value: EmailTemplate["platform"]) => {
    setNewEmailTemplateData((prev) => ({ ...prev, platform: value }))
  }

  const insertVariable = (variable: string) => {
    const textarea = document.getElementById('body') as HTMLTextAreaElement
    if (textarea) {
      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      const text = textarea.value
      const before = text.substring(0, start)
      const after = text.substring(end, text.length)
      const newText = before + variable + after
      
      setNewEmailTemplateData(prev => ({ ...prev, body: newText }))
      
      // Set cursor position after inserted variable
      setTimeout(() => {
        textarea.selectionStart = textarea.selectionEnd = start + variable.length
        textarea.focus()
      }, 0)
    }
  }

  const generateMockQuestions = (role: string, level: string, techStack: string, type: string): InterviewQuestion[] => {
    const mockQuestions: InterviewQuestion[] = [
      {
        id: "q1",
        question: `Explain the difference between ${techStack.split(',')[0]?.trim() || 'React'} and vanilla JavaScript for building user interfaces.`,
        type: "Technical",
        difficulty: level.toLowerCase().includes('senior') ? "Hard" : level.toLowerCase().includes('junior') ? "Easy" : "Medium",
        category: "Frontend Development"
      },
      {
        id: "q2", 
        question: `Describe a challenging project you worked on as a ${role}. What was your approach?`,
        type: "Behavioral",
        difficulty: "Medium",
        category: "Experience"
      },
      {
        id: "q3",
        question: `How would you optimize the performance of a ${techStack.split(',')[0]?.trim() || 'web'} application?`,
        type: "Technical",
        difficulty: "Medium",
        category: "Performance"
      },
      {
        id: "q4",
        question: "Tell me about a time when you had to work with a difficult team member.",
        type: "Behavioral", 
        difficulty: "Medium",
        category: "Teamwork"
      },
      {
        id: "q5",
        question: `Design a system that can handle ${level.toLowerCase().includes('senior') ? '1 million' : '10,000'} concurrent users.`,
        type: "System Design",
        difficulty: level.toLowerCase().includes('senior') ? "Hard" : "Medium",
        category: "Architecture"
      }
    ]

    // Add more questions based on tech stack
    if (techStack.toLowerCase().includes('react')) {
      mockQuestions.push({
        id: "q6",
        question: "Explain React hooks and when you would use useState vs useEffect.",
        type: "Technical",
        difficulty: "Medium", 
        category: "React"
      })
    }

    if (techStack.toLowerCase().includes('node')) {
      mockQuestions.push({
        id: "q7",
        question: "How does Node.js handle asynchronous operations and what is the event loop?",
        type: "Technical",
        difficulty: "Medium",
        category: "Backend"
      })
    }

    return mockQuestions.slice(0, Math.floor(Math.random() * 4) + 5) // 5-8 questions
  }

  const handleGenerateQuestions = () => {
    setIsGeneratingQuestions(true)
    // Simulate AI generation
    setTimeout(() => {
      const generatedQuestions = generateMockQuestions(
        newTemplateData.role,
        newTemplateData.level, 
        newTemplateData.techStack,
        newTemplateData.type
      )
      setNewTemplateData((prev) => ({ 
        ...prev, 
        questionCount: generatedQuestions.length,
        questions: generatedQuestions
      }))
      setIsGeneratingQuestions(false)
      setShowQuestions(true)
      console.log("AI generated questions:", generatedQuestions)
    }, 1500)
  }

  const handleAddQuestion = () => {
    if (!newQuestion.question.trim() || !newQuestion.category.trim()) {
      alert("Please fill in the question and category fields.")
      return
    }

    const questionToAdd: InterviewQuestion = {
      id: `q${Date.now()}`,
      question: newQuestion.question,
      type: newQuestion.type,
      difficulty: newQuestion.difficulty,
      category: newQuestion.category,
    }

    setNewTemplateData(prev => ({
      ...prev,
      questions: [...prev.questions, questionToAdd],
      questionCount: prev.questions.length + 1
    }))

    setNewQuestion({
      question: "",
      type: "Technical",
      difficulty: "Medium",
      category: "",
    })
  }

  const handleEditQuestion = (question: InterviewQuestion) => {
    setEditingQuestion(question)
    setNewQuestion({
      question: question.question,
      type: question.type,
      difficulty: question.difficulty,
      category: question.category,
    })
  }

  const handleSaveEditedQuestion = () => {
    if (!editingQuestion) return

    setNewTemplateData(prev => ({
      ...prev,
      questions: prev.questions.map(q => 
        q.id === editingQuestion.id 
          ? {
              ...q,
              question: newQuestion.question,
              type: newQuestion.type,
              difficulty: newQuestion.difficulty,
              category: newQuestion.category,
            }
          : q
      )
    }))

    setEditingQuestion(null)
    setNewQuestion({
      question: "",
      type: "Technical",
      difficulty: "Medium",
      category: "",
    })
  }

  const handleDeleteQuestion = (questionId: string) => {
    setNewTemplateData(prev => ({
      ...prev,
      questions: prev.questions.filter(q => q.id !== questionId),
      questionCount: prev.questions.filter(q => q.id !== questionId).length
    }))
  }

  const handleCancelEdit = () => {
    setEditingQuestion(null)
    setNewQuestion({
      question: "",
      type: "Technical",
      difficulty: "Medium",
      category: "",
    })
  }

  const handleSaveNewEmailTemplate = () => {
    if (!newEmailTemplateData.name || !newEmailTemplateData.subject || !newEmailTemplateData.body) {
      alert("Please fill all required fields for the email template.")
      return
    }

    const newId = `email-${Date.now()}`
    const availableVariables = [
      "{candidate_name}", "{position_title}", "{company_name}", "{assessment_duration}", 
      "{tech_stack}", "{deadline_date}", "{assessment_link}", "{contact_email}", 
      "{recruiter_name}", "{platform_name}", "{programming_languages}", "{assessment_topics}",
      "{custom_instructions}"
    ]

    const newEmailTemplate: EmailTemplate = {
      id: newId,
      name: newEmailTemplateData.name,
      subject: newEmailTemplateData.subject,
      body: newEmailTemplateData.body,
      platform: newEmailTemplateData.platform,
      dateModified: new Date().toISOString().split("T")[0],
      isActive: true,
      variables: availableVariables
    }

    setEmailTemplates((prev) => [...prev, newEmailTemplate])
    // Reset form after saving
    setNewEmailTemplateData({
      name: "",
      subject: "",
      body: "",
      platform: "Generic",
    })
    console.log("New email template saved:", newEmailTemplate)
  }

  const handleSaveNewTemplate = () => {
    if (
      !newTemplateData.role ||
      !newTemplateData.level ||
      !newTemplateData.techStack ||
      newTemplateData.questionCount === 0
    ) {
      alert("Please fill all fields and generate question count.")
      return
    }

    const newId = `temp-${Date.now()}`
    const newName = `${newTemplateData.role} ${newTemplateData.level} - ${newTemplateData.type}`
    const newTemplate: InterviewTemplate = {
      id: newId,
      name: newName,
      type: newTemplateData.type,
      dateModified: new Date().toISOString().split("T")[0],
      role: newTemplateData.role,
      level: newTemplateData.level,
      techStack: newTemplateData.techStack,
      questionCount: newTemplateData.questionCount,
      questions: newTemplateData.questions,
    }
    setTemplates((prev) => [...prev, newTemplate])
    // Reset form after saving
    setNewTemplateData({
      role: "",
      level: "",
      type: "Technical",
      techStack: "",
      questionCount: 0,
      questions: [],
    })
    setShowQuestions(false)
    console.log("New template saved:", newTemplate)
  }

  const handleContinue = () => {
    // Since we're only creating templates for this project, we can always continue
    console.log("Continuing to next step from Interview Template.")
    onNext()
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "Easy": return "bg-green-100 text-green-800"
      case "Medium": return "bg-yellow-100 text-yellow-800"
      case "Hard": return "bg-red-100 text-red-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case "Technical": return "bg-blue-100 text-blue-800"
      case "Behavioral": return "bg-purple-100 text-purple-800"
      case "System Design": return "bg-orange-100 text-orange-800"
      case "Problem-Solving": return "bg-pink-100 text-pink-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle>Create Interview Templates</CardTitle>
        <CardDescription>
          Create a new interview template for this project or set up an email template for external platforms like HackerRank.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex space-x-2 mb-4">
          <Button
            variant={viewMode === "create" ? "default" : "outline"}
            onClick={() => setViewMode("create")}
            className="flex-1"
          >
            <Code className="h-4 w-4 mr-2" />
            Create Interview Template
          </Button>
          <Button
            variant={viewMode === "email" ? "default" : "outline"}
            onClick={() => setViewMode("email")}
            className="flex-1"
          >
            <Mail className="h-4 w-4 mr-2" />
            Create Email Template
          </Button>
        </div>

        {/* Show created templates */}
        {templates.length > 0 && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Created Interview Templates</h3>
            <div className="grid gap-3">
              {templates.map((template) => (
                <Card key={template.id} className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">{template.name}</h4>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge variant="outline">{template.type}</Badge>
                        <span className="text-sm text-muted-foreground">
                          {template.questionCount} questions
                        </span>
                        <span className="text-sm text-muted-foreground">•</span>
                        <span className="text-sm text-muted-foreground">
                          {template.dateModified}
                        </span>
                      </div>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>View Questions</DropdownMenuItem>
                        <DropdownMenuItem>Edit Template</DropdownMenuItem>
                        <DropdownMenuItem className="text-red-600">Delete</DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* Show created email templates */}
        {emailTemplates.length > 0 && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Created Email Templates</h3>
            <div className="grid gap-3">
              {emailTemplates.map((template) => (
                <Card key={template.id} className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">{template.name}</h4>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge variant="secondary">{template.platform}</Badge>
                        <span className="text-sm text-muted-foreground">
                          {template.dateModified}
                        </span>
                      </div>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>Preview Email</DropdownMenuItem>
                        <DropdownMenuItem>Edit Template</DropdownMenuItem>
                        <DropdownMenuItem className="text-red-600">Delete</DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        )}

        {viewMode === "email" ? (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Create Email Template for External Platforms</h3>
              <Badge variant="outline" className="flex items-center gap-1">
                <Mail className="h-3 w-3" />
                External Assessment
              </Badge>
            </div>
            
            <div className="space-y-6">
              <h4 className="text-lg font-semibold">Create New Email Template</h4>
              
              <div className="grid gap-4 md:grid-cols-2">
                <div className="grid gap-2">
                  <Label htmlFor="email-name">Template Name *</Label>
                  <Input
                    id="name"
                    placeholder="e.g., HackerRank Frontend Assessment"
                    value={newEmailTemplateData.name}
                    onChange={handleNewEmailTemplateChange}
                    required
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="email-platform">Platform</Label>
                  <Select onValueChange={handleNewEmailTemplatePlatformChange} value={newEmailTemplateData.platform}>
                    <SelectTrigger id="email-platform">
                      <SelectValue placeholder="Select platform" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="HackerRank">HackerRank</SelectItem>
                      <SelectItem value="Codility">Codility</SelectItem>
                      <SelectItem value="LeetCode">LeetCode</SelectItem>
                      <SelectItem value="Custom">Custom Platform</SelectItem>
                      <SelectItem value="Generic">Generic</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="email-subject">Email Subject *</Label>
                <Input
                  id="subject"
                  placeholder="e.g., Technical Assessment Invitation - {position_title}"
                  value={newEmailTemplateData.subject}
                  onChange={handleNewEmailTemplateChange}
                  required
                />
                <p className="text-xs text-muted-foreground">
                  Use variables like {"{position_title}"}, {"{candidate_name}"}, {"{company_name}"}
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="email-body">Email Body *</Label>
                <div className="space-y-2">
                  <div className="flex flex-wrap gap-1">
                    <span className="text-sm text-muted-foreground mr-2">Quick Insert:</span>
                    {[
                      "{candidate_name}", "{position_title}", "{company_name}", 
                      "{assessment_duration}", "{deadline_date}", "{assessment_link}",
                      "{contact_email}", "{recruiter_name}"
                    ].map(variable => (
                      <Button
                        key={variable}
                        variant="outline"
                        size="sm"
                        onClick={() => insertVariable(variable)}
                        className="text-xs h-6 px-2"
                      >
                        {variable}
                      </Button>
                    ))}
                  </div>
                  <Textarea
                    id="body"
                    placeholder="Write your email template here. Use variables like {candidate_name} for personalization..."
                    value={newEmailTemplateData.body}
                    onChange={handleNewEmailTemplateChange}
                    rows={12}
                    required
                  />
                </div>
                <p className="text-xs text-muted-foreground">
                  Available variables: {"{candidate_name}"}, {"{position_title}"}, {"{company_name}"}, {"{assessment_duration}"}, 
                  {"{tech_stack}"}, {"{deadline_date}"}, {"{assessment_link}"}, {"{contact_email}"}, {"{recruiter_name}"}
                </p>
              </div>

              <Button onClick={handleSaveNewEmailTemplate} className="w-full">
                <Mail className="h-4 w-4 mr-2" />
                Save Email Template
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold">Create New Interview Template</h3>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="grid gap-2">
                <Label htmlFor="role">Role</Label>
                <Input
                  id="role"
                  placeholder="e.g., Software Engineer"
                  value={newTemplateData.role}
                  onChange={handleNewTemplateChange}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="level">Level</Label>
                <Input
                  id="level"
                  placeholder="e.g., Senior, Junior"
                  value={newTemplateData.level}
                  onChange={handleNewTemplateChange}
                  required
                />
              </div>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="type">Type</Label>
              <Select onValueChange={handleNewTemplateTypeChange} value={newTemplateData.type}>
                <SelectTrigger id="type">
                  <SelectValue placeholder="Select interview type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Technical">Technical</SelectItem>
                  <SelectItem value="Behavioral">Behavioral</SelectItem>
                  <SelectItem value="Mixed">Mixed</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="techStack">Tech Stack (comma-separated)</Label>
              <Textarea
                id="techStack"
                placeholder="e.g., React, Node.js, AWS, Python"
                value={newTemplateData.techStack}
                onChange={handleNewTemplateChange}
                rows={3}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="questionCount">AI Generated Question Count</Label>
              <div className="flex items-center space-x-2">
                <Input
                  id="questionCount"
                  type="number"
                  value={newTemplateData.questionCount}
                  readOnly
                  className="flex-1"
                />
                <Button onClick={handleGenerateQuestions} disabled={isGeneratingQuestions}>
                  {isGeneratingQuestions ? (
                    "Generating..."
                  ) : (
                    <>
                      <Sparkles className="mr-2 h-4 w-4" /> Generate Questions
                    </>
                  )}
                </Button>
              </div>
              <p className="text-sm text-muted-foreground">
                Our AI will generate a recommended number of questions based on the role, level, and tech stack.
              </p>
            </div>

            {/* Questions Management Section */}
            {showQuestions && newTemplateData.questions.length > 0 && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="text-lg font-semibold">Generated Questions</h4>
                  <Badge variant="outline">{newTemplateData.questions.length} questions</Badge>
                </div>
                
                <div className="space-y-3">
                  {newTemplateData.questions.map((question, index) => (
                    <Card key={question.id} className="p-4">
                      <div className="space-y-3">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <span className="text-sm font-medium">Question {index + 1}</span>
                              <Badge className={getTypeColor(question.type)} variant="secondary">
                                {question.type}
                              </Badge>
                              <Badge className={getDifficultyColor(question.difficulty)} variant="secondary">
                                {question.difficulty}
                              </Badge>
                              <Badge variant="outline">{question.category}</Badge>
                            </div>
                            <p className="text-sm text-muted-foreground mb-2">{question.question}</p>
                          </div>
                          <div className="flex items-center gap-1 ml-4">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEditQuestion(question)}
                              className="h-8 w-8 p-0"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteQuestion(question.id)}
                              className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>

                {/* Add/Edit Question Form */}
                <Card className="p-4">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h5 className="font-medium">
                        {editingQuestion ? "Edit Question" : "Add New Question"}
                      </h5>
                      {editingQuestion && (
                        <Button variant="ghost" size="sm" onClick={handleCancelEdit}>
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                    
                    <div className="grid gap-4">
                      <div className="grid gap-2">
                        <Label htmlFor="question">Question *</Label>
                        <Textarea
                          id="question"
                          placeholder="Enter your interview question..."
                          value={newQuestion.question}
                          onChange={(e) => setNewQuestion(prev => ({ ...prev, question: e.target.value }))}
                          rows={3}
                        />
                      </div>
                      
                      <div className="grid gap-4 md:grid-cols-3">
                        <div className="grid gap-2">
                          <Label htmlFor="questionType">Type</Label>
                          <Select 
                            value={newQuestion.type} 
                            onValueChange={(value: InterviewQuestion["type"]) => 
                              setNewQuestion(prev => ({ ...prev, type: value }))
                            }
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Technical">Technical</SelectItem>
                              <SelectItem value="Behavioral">Behavioral</SelectItem>
                              <SelectItem value="Problem-Solving">Problem-Solving</SelectItem>
                              <SelectItem value="System Design">System Design</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        
                        <div className="grid gap-2">
                          <Label htmlFor="questionDifficulty">Difficulty</Label>
                          <Select 
                            value={newQuestion.difficulty} 
                            onValueChange={(value: InterviewQuestion["difficulty"]) => 
                              setNewQuestion(prev => ({ ...prev, difficulty: value }))
                            }
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Easy">Easy</SelectItem>
                              <SelectItem value="Medium">Medium</SelectItem>
                              <SelectItem value="Hard">Hard</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        
                        <div className="grid gap-2">
                          <Label htmlFor="questionCategory">Category *</Label>
                          <Input
                            id="questionCategory"
                            placeholder="e.g., React, Algorithms"
                            value={newQuestion.category}
                            onChange={(e) => setNewQuestion(prev => ({ ...prev, category: e.target.value }))}
                          />
                        </div>
                      </div>
                      
                      <Button 
                        onClick={editingQuestion ? handleSaveEditedQuestion : handleAddQuestion}
                        className="w-full"
                      >
                        {editingQuestion ? (
                          <>
                            <Save className="h-4 w-4 mr-2" />
                            Save Changes
                          </>
                        ) : (
                          <>
                            <Plus className="h-4 w-4 mr-2" />
                            Add Question
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </Card>
              </div>
            )}

            <Button onClick={handleSaveNewTemplate} className="w-full">
              Save New Template
            </Button>
          </div>
        )}

        <Button onClick={handleContinue} className="w-full">
          Continue
        </Button>
      </CardContent>
    </Card>
  )
}
