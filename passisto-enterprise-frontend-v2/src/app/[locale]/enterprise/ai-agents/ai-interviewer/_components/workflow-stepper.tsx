import * as React from "react"
import { cn } from "@/lib/utils"
import { Check } from "lucide-react"

type Step = {
  id: string
  title: string
}

type WorkflowStepperProps = {
  steps: Step[]
  currentStep: number
}

export function WorkflowStepper({ steps, currentStep }: WorkflowStepperProps) {
  return (
    <div className="flex items-center justify-between space-x-2 overflow-x-auto pb-4">
      {steps.map((step, index) => (
        <React.Fragment key={step.id}>
          <div className="flex flex-col items-center">
            <div
              className={cn(
                "flex h-8 w-8 items-center justify-center rounded-full border-2",
                index === currentStep
                  ? "border-primary bg-primary text-primary-foreground"
                  : index < currentStep
                    ? "border-primary bg-primary text-primary-foreground"
                    : "border-muted-foreground text-muted-foreground",
              )}
            >
              {index < currentStep ? <Check className="h-4 w-4" /> : index + 1}
            </div>
            <span
              className={cn(
                "mt-2 whitespace-nowrap text-center text-sm",
                index === currentStep ? "font-medium text-foreground" : "text-muted-foreground",
              )}
            >
              {step.title}
            </span>
          </div>
          {index < steps.length - 1 && (
            <div
              className={cn("h-0.5 flex-1 rounded-full", index < currentStep ? "bg-primary" : "bg-muted-foreground/30")}
            />
          )}
        </React.Fragment>
      ))}
    </div>
  )
}
