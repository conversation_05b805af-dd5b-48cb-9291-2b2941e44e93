"use client"

import * as React from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alogTitle, DialogDescription } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { DropdownMenuItem } from "@/components/ui/dropdown-menu"
import { FileText } from "lucide-react"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { ChevronDown, Mail, MapPin } from "lucide-react"

type Candidate = {
  id: string
  name: string
  age: number
  location: string
  experience: number
  compatibilityScore: number
  interviewScore: number | null
  status: "Not Interviewed" | "Sent" | "Done" | "Rejected" | "Accepted"
  skills: string[]
  contact: string
  cvText: string
}

type CandidateProfileModalProps = {
  open: boolean
  onOpenChange: (open: boolean) => void
  candidate: Candidate
  onSendInterview: (candidateId: string) => void
  onUpdateStatus: (candidateId: string, newStatus: Candidate["status"]) => void
}

export function CandidateProfileModal({
  open,
  onOpenChange,
  candidate,
  onSendInterview,
  onUpdateStatus,
}: CandidateProfileModalProps) {
  const [internalNotes, setInternalNotes] = React.useState("")

  React.useEffect(() => {
    // Simulate fetching notes if they existed
    setInternalNotes(`Notes for ${candidate.name}: Good communication skills. Needs to improve on data structures.`)
  }, [candidate])

  const allStatuses: Candidate["status"][] = ["Not Interviewed", "Sent", "Done", "Rejected", "Accepted"]

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle>{candidate.name}'s Profile</DialogTitle>
          <DialogDescription>Detailed information and actions for this candidate.</DialogDescription>
        </DialogHeader>
        <div className="grid gap-6 py-4 md:grid-cols-2">
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Candidate Information</h3>
            <div className="grid gap-2">
              <p className="text-sm text-muted-foreground">
                <Mail className="mr-2 inline-block h-4 w-4" />
                {candidate.contact}
              </p>
              <p className="text-sm text-muted-foreground">
                <MapPin className="mr-2 inline-block h-4 w-4" />
                {candidate.location}
              </p>
              <p className="text-sm text-muted-foreground">
                Age: {candidate.age} | Experience: {candidate.experience} years
              </p>
            </div>

            <div className="grid gap-2">
              <Label>Skills</Label>
              <div className="flex flex-wrap gap-2">
                {candidate.skills.map((skill, index) => (
                  <Badge key={index} variant="outline">
                    {skill}
                  </Badge>
                ))}
              </div>
            </div>

            <div className="grid gap-2">
              <Label>Current Status</Label>
              <Badge
                variant={
                  candidate.status === "Accepted"
                    ? "default"
                    : candidate.status === "Rejected"
                      ? "destructive"
                      : "outline"
                }
                className="w-fit"
              >
                {candidate.status}
              </Badge>
            </div>

            <div className="grid gap-2">
              <Label>Compatibility Score</Label>
              <Badge variant={candidate.compatibilityScore >= 80 ? "default" : "secondary"} className="w-fit">
                {candidate.compatibilityScore}%
              </Badge>
            </div>

            <div className="grid gap-2">
              <Label>Interview Score</Label>
              {candidate.interviewScore !== null ? (
                <Badge variant={candidate.interviewScore >= 70 ? "default" : "secondary"} className="w-fit">
                  {candidate.interviewScore}%
                </Badge>
              ) : (
                <span className="text-sm text-muted-foreground">Not interviewed yet.</span>
              )}
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold">CV Preview & Matching</h3>
            <div className="rounded-md border bg-muted p-4 text-sm text-muted-foreground">
              <p className="font-medium">CV Content Preview:</p>
              <p className="line-clamp-6 overflow-hidden text-ellipsis">{candidate.cvText}</p>
              <Button variant="link" className="h-auto p-0 text-primary">
                <FileText className="mr-1 h-4 w-4" /> View Full CV
              </Button>
            </div>

            <div className="grid gap-2">
              <Label>Matching Breakdown (vs. Job Offer)</Label>
              <ul className="list-disc space-y-1 pl-5 text-sm text-muted-foreground">
                <li>Skills: 4/5 matched (React, Node.js, AWS, TypeScript)</li>
                <li>Experience: Matches 5+ years requirement</li>
                <li>Education: Bachelor's Degree (matched)</li>
              </ul>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="internal-notes">Internal Notes</Label>
              <Textarea
                id="internal-notes"
                placeholder="Add internal notes about this candidate..."
                value={internalNotes}
                onChange={(e) => setInternalNotes(e.target.value)}
                rows={4}
              />
            </div>
          </div>
        </div>

        <div className="flex justify-end gap-2 pt-4">
          <Button onClick={() => onSendInterview(candidate.id)} disabled={candidate.status !== "Not Interviewed"}>
            Send Interview
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                Update Status <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {allStatuses.map((status) => (
                <DropdownMenuItem
                  key={status}
                  onClick={() => onUpdateStatus(candidate.id, status)}
                  disabled={candidate.status === status}
                >
                  {status}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </DialogContent>
    </Dialog>
  )
}
