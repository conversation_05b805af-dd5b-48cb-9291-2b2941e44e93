"use client"

import * as React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  Plus, 
  Trash2, 
  GripVertical, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  Settings,
  ArrowUp,
  ArrowDown,
  Calendar
} from "lucide-react"
import {
  InterviewPhase,
  InterviewPhaseType,
  InterviewPhaseConfiguration,
  DEFAULT_INTERVIEW_PHASES,
  generatePhaseId,
  calculateTotalDuration,
  getPhaseTypeLabel
} from "@/lib/utils"

type InterviewPhaseConfigurationProps = {
  projectId: string
  onNext: () => void
  onSave?: (configuration: InterviewPhaseConfiguration) => void
  onPhasesConfigured?: (phases: InterviewPhase[]) => void
}

export function InterviewPhaseConfigurationSection({ 
  projectId, 
  onNext, 
  onSave,
  onPhasesConfigured
}: InterviewPhaseConfigurationProps) {
  const [phases, setPhases] = React.useState<InterviewPhase[]>(() => {
    // Initialize with default phases
    return DEFAULT_INTERVIEW_PHASES.map((phase, index) => ({
      ...phase,
      id: generatePhaseId(),
      status: "not-started" as const,
      startDate: "",
      endDate: "",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }))
  })

  const [selectedPhaseId, setSelectedPhaseId] = React.useState<string | null>(null)
  const [showAdvancedSettings, setShowAdvancedSettings] = React.useState(false)

  const totalDuration = React.useMemo(() => calculateTotalDuration(phases), [phases])
  const selectedPhase = phases.find(p => p.id === selectedPhaseId)

  // Helper function to get date range info
  const getDateRangeInfo = React.useMemo(() => {
    const phasesWithDates = phases.filter(p => p.startDate && p.endDate)
    if (phasesWithDates.length === 0) return null

    const startDates = phasesWithDates.map(p => new Date(p.startDate!))
    const endDates = phasesWithDates.map(p => new Date(p.endDate!))
    
    const earliestStart = new Date(Math.min(...startDates.map(d => d.getTime())))
    const latestEnd = new Date(Math.max(...endDates.map(d => d.getTime())))

    return {
      start: earliestStart,
      end: latestEnd,
      duration: Math.ceil((latestEnd.getTime() - earliestStart.getTime()) / (1000 * 60 * 60 * 24))
    }
  }, [phases])

  const handleAddPhase = () => {
    const newPhase: InterviewPhase = {
      id: generatePhaseId(),
      name: "New Phase",
      type: "custom",
      description: "",
      order: phases.length + 1,
      isRequired: true,
      estimatedDuration: 30,
      startDate: "",
      endDate: "",
      configuration: {
        allowSkip: false,
        requiresApproval: true,
        autoAdvance: false,
        passingScore: 70,
        maxAttempts: 1
      },
      status: "not-started",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    setPhases(prev => [...prev, newPhase])
    setSelectedPhaseId(newPhase.id)
  }

  const handleDeletePhase = (phaseId: string) => {
    setPhases(prev => {
      const filtered = prev.filter(p => p.id !== phaseId)
      // Reorder remaining phases
      return filtered.map((phase, index) => ({
        ...phase,
        order: index + 1,
        updatedAt: new Date().toISOString()
      }))
    })
    if (selectedPhaseId === phaseId) {
      setSelectedPhaseId(null)
    }
  }

  const handleMovePhase = (phaseId: string, direction: 'up' | 'down') => {
    setPhases(prev => {
      const currentIndex = prev.findIndex(p => p.id === phaseId)
      if (currentIndex === -1) return prev
      
      const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1
      if (newIndex < 0 || newIndex >= prev.length) return prev

      const newPhases = [...prev]
      const [movedPhase] = newPhases.splice(currentIndex, 1)
      newPhases.splice(newIndex, 0, movedPhase)

      // Update order numbers
      return newPhases.map((phase, index) => ({
        ...phase,
        order: index + 1,
        updatedAt: new Date().toISOString()
      }))
    })
  }

  const handleUpdatePhase = (phaseId: string, updates: Partial<InterviewPhase>) => {
    setPhases(prev => prev.map(phase => 
      phase.id === phaseId 
        ? { ...phase, ...updates, updatedAt: new Date().toISOString() }
        : phase
    ))
  }

  // Helper function to auto-suggest dates for a phase
  const suggestDatesForPhase = (phaseIndex: number) => {
    const today = new Date()
    const daysOffset = phaseIndex * 7 // Each phase starts a week after the previous
    const startDate = new Date(today.getTime() + daysOffset * 24 * 60 * 60 * 1000)
    const endDate = new Date(startDate.getTime() + 7 * 24 * 60 * 60 * 1000) // 1 week duration
    
    return {
      startDate: startDate.toISOString().slice(0, 16), // Format for datetime-local
      endDate: endDate.toISOString().slice(0, 16)
    }
  }

  // Helper function to auto-schedule all phases
  const autoScheduleAllPhases = () => {
    const today = new Date()
    const updatedPhases = phases.map((phase, index) => {
      const daysOffset = index * 7 // Each phase starts a week after the previous
      const startDate = new Date(today.getTime() + daysOffset * 24 * 60 * 60 * 1000)
      const endDate = new Date(startDate.getTime() + 7 * 24 * 60 * 60 * 1000) // 1 week duration
      
      return {
        ...phase,
        startDate: startDate.toISOString().slice(0, 16),
        endDate: endDate.toISOString().slice(0, 16),
        updatedAt: new Date().toISOString()
      }
    })
    
    setPhases(updatedPhases)
  }

  const handleSaveConfiguration = () => {
    const configuration: InterviewPhaseConfiguration = {
      projectId,
      phases,
      totalEstimatedDuration: totalDuration,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    
    console.log("Saving interview phase configuration:", configuration)
    onSave?.(configuration)
    onPhasesConfigured?.(phases) // Pass the configured phases to parent
    onNext()
  }

  const phaseTypeOptions: { value: InterviewPhaseType; label: string }[] = [
    { value: "screening", label: "Screening" },
    { value: "technical", label: "Technical" },
    { value: "behavioral", label: "Behavioral" },
    { value: "cultural-fit", label: "Cultural Fit" },
    { value: "leadership", label: "Leadership" },
    { value: "final-round", label: "Final Round" },
    { value: "post-offer", label: "Post-Offer" },
    { value: "custom", label: "Custom" }
  ]

  return (
    <div className="w-full max-w-6xl space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Configure Interview Phases
          </CardTitle>
          <CardDescription>
            Set up multiple interview phases for your recruitment process. Each phase can have different 
            requirements, scoring criteria, and approval workflows.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-4 flex-wrap">
              <Badge variant="outline" className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                Total Duration: {totalDuration} minutes
              </Badge>
              <Badge variant="outline">
                {phases.length} Phase{phases.length !== 1 ? 's' : ''}
              </Badge>
              {getDateRangeInfo && (
                <Badge variant="outline" className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  {getDateRangeInfo.start.toLocaleDateString()} - {getDateRangeInfo.end.toLocaleDateString()}
                  <span className="ml-1">({getDateRangeInfo.duration} days)</span>
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={autoScheduleAllPhases}
              >
                <Calendar className="h-4 w-4 mr-1" />
                Auto-Schedule All
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAdvancedSettings(!showAdvancedSettings)}
              >
                <Settings className="h-4 w-4 mr-1" />
                {showAdvancedSettings ? 'Hide' : 'Show'} Advanced
              </Button>
              <Button onClick={handleAddPhase} size="sm">
                <Plus className="h-4 w-4 mr-1" />
                Add Phase
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Phase List */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Interview Phases</CardTitle>
              <CardDescription>Click a phase to configure its settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              {phases.map((phase, index) => (
                <div
                  key={phase.id}
                  className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                    selectedPhaseId === phase.id 
                      ? 'border-primary bg-primary/5' 
                      : 'border-border hover:border-primary/50'
                  }`}
                  onClick={() => setSelectedPhaseId(phase.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <GripVertical className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <div className="font-medium text-sm">{phase.name}</div>
                        <div className="text-xs text-muted-foreground">
                          {getPhaseTypeLabel(phase.type)} • {phase.estimatedDuration}min
                        </div>
                        {(phase.startDate || phase.endDate) && (
                          <div className="text-xs text-muted-foreground flex items-center gap-1 mt-1">
                            <Calendar className="h-3 w-3" />
                            {phase.startDate && new Date(phase.startDate).toLocaleDateString()}
                            {phase.startDate && phase.endDate && " - "}
                            {phase.endDate && new Date(phase.endDate).toLocaleDateString()}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-1">
                      {phase.isRequired && (
                        <AlertCircle className="h-3 w-3 text-orange-500" />
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleMovePhase(phase.id, 'up')
                        }}
                        disabled={index === 0}
                        className="h-6 w-6 p-0"
                      >
                        <ArrowUp className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleMovePhase(phase.id, 'down')
                        }}
                        disabled={index === phases.length - 1}
                        className="h-6 w-6 p-0"
                      >
                        <ArrowDown className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDeletePhase(phase.id)
                        }}
                        className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* Phase Configuration */}
        <div className="lg:col-span-2">
          {selectedPhase ? (
            <Card>
              <CardHeader>
                <CardTitle>Configure: {selectedPhase.name}</CardTitle>
                <CardDescription>
                  Customize the settings for this interview phase
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Basic Settings */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="phase-name">Phase Name</Label>
                    <Input
                      id="phase-name"
                      value={selectedPhase.name}
                      onChange={(e) => handleUpdatePhase(selectedPhase.id, { name: e.target.value })}
                      placeholder="Enter phase name"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phase-type">Phase Type</Label>
                    <Select
                      value={selectedPhase.type}
                      onValueChange={(value: InterviewPhaseType) => 
                        handleUpdatePhase(selectedPhase.id, { type: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {phaseTypeOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phase-description">Description</Label>
                  <Textarea
                    id="phase-description"
                    value={selectedPhase.description || ""}
                    onChange={(e) => handleUpdatePhase(selectedPhase.id, { description: e.target.value })}
                    placeholder="Describe what this phase evaluates"
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="duration">Estimated Duration (minutes)</Label>
                    <Input
                      id="duration"
                      type="number"
                      min="5"
                      max="180"
                      value={selectedPhase.estimatedDuration}
                      onChange={(e) => handleUpdatePhase(selectedPhase.id, { 
                        estimatedDuration: parseInt(e.target.value) || 30 
                      })}
                    />
                  </div>
                  <div className="flex items-center space-x-2 pt-6">
                    <Switch
                      id="required"
                      checked={selectedPhase.isRequired}
                      onCheckedChange={(checked) => handleUpdatePhase(selectedPhase.id, { isRequired: checked })}
                    />
                    <Label htmlFor="required">Required Phase</Label>
                  </div>
                </div>

                {/* Date Configuration */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-primary" />
                      <h4 className="font-medium">Schedule</h4>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const phaseIndex = phases.findIndex(p => p.id === selectedPhase.id)
                        const suggestedDates = suggestDatesForPhase(phaseIndex)
                        handleUpdatePhase(selectedPhase.id, {
                          startDate: suggestedDates.startDate,
                          endDate: suggestedDates.endDate
                        })
                      }}
                    >
                      Auto-suggest Dates
                    </Button>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="start-date">Start Date & Time</Label>
                      <Input
                        id="start-date"
                        type="datetime-local"
                        value={selectedPhase.startDate || ""}
                        onChange={(e) => handleUpdatePhase(selectedPhase.id, { startDate: e.target.value })}
                      />
                      <p className="text-xs text-muted-foreground">
                        When this phase should begin
                      </p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="end-date">End Date & Time</Label>
                      <Input
                        id="end-date"
                        type="datetime-local"
                        value={selectedPhase.endDate || ""}
                        onChange={(e) => handleUpdatePhase(selectedPhase.id, { endDate: e.target.value })}
                        min={selectedPhase.startDate || ""}
                      />
                      <p className="text-xs text-muted-foreground">
                        Deadline for completing this phase
                      </p>
                    </div>
                  </div>
                  {selectedPhase.startDate && selectedPhase.endDate && (
                    <div className="text-sm text-muted-foreground bg-muted/50 p-3 rounded-lg">
                      <div className="font-medium mb-1">Phase Duration:</div>
                      <div>
                        {Math.ceil(
                          (new Date(selectedPhase.endDate).getTime() - new Date(selectedPhase.startDate).getTime()) 
                          / (1000 * 60 * 60 * 24)
                        )} days ({Math.ceil(
                          (new Date(selectedPhase.endDate).getTime() - new Date(selectedPhase.startDate).getTime()) 
                          / (1000 * 60 * 60)
                        )} hours)
                      </div>
                    </div>
                  )}
                </div>

                {showAdvancedSettings && (
                  <>
                    <Separator />
                    <div className="space-y-4">
                      <h4 className="font-medium">Advanced Configuration</h4>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="passing-score">Passing Score (%)</Label>
                          <Input
                            id="passing-score"
                            type="number"
                            min="0"
                            max="100"
                            value={selectedPhase.configuration.passingScore || ""}
                            onChange={(e) => handleUpdatePhase(selectedPhase.id, {
                              configuration: {
                                ...selectedPhase.configuration,
                                passingScore: parseInt(e.target.value) || undefined
                              }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="max-attempts">Max Attempts</Label>
                          <Input
                            id="max-attempts"
                            type="number"
                            min="1"
                            max="5"
                            value={selectedPhase.configuration.maxAttempts || ""}
                            onChange={(e) => handleUpdatePhase(selectedPhase.id, {
                              configuration: {
                                ...selectedPhase.configuration,
                                maxAttempts: parseInt(e.target.value) || undefined
                              }
                            })}
                          />
                        </div>
                      </div>

                      <div className="space-y-3">
                        <div className="flex items-center space-x-2">
                          <Switch
                            id="allow-skip"
                            checked={selectedPhase.configuration.allowSkip}
                            onCheckedChange={(checked) => handleUpdatePhase(selectedPhase.id, {
                              configuration: { ...selectedPhase.configuration, allowSkip: checked }
                            })}
                          />
                          <Label htmlFor="allow-skip">Allow candidates to skip this phase</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Switch
                            id="requires-approval"
                            checked={selectedPhase.configuration.requiresApproval}
                            onCheckedChange={(checked) => handleUpdatePhase(selectedPhase.id, {
                              configuration: { ...selectedPhase.configuration, requiresApproval: checked }
                            })}
                          />
                          <Label htmlFor="requires-approval">Requires manual approval to proceed</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Switch
                            id="auto-advance"
                            checked={selectedPhase.configuration.autoAdvance}
                            onCheckedChange={(checked) => handleUpdatePhase(selectedPhase.id, {
                              configuration: { ...selectedPhase.configuration, autoAdvance: checked }
                            })}
                          />
                          <Label htmlFor="auto-advance">Auto-advance to next phase on completion</Label>
                        </div>
                      </div>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="flex items-center justify-center h-64">
                <div className="text-center text-muted-foreground">
                  <Settings className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Select a phase from the list to configure its settings</p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      <Card>
        <CardContent className="pt-6">
          <div className="flex justify-between items-center">
            <div className="space-y-1">
              <div className="text-sm text-muted-foreground">
                {phases.filter(p => p.isRequired).length} required phases, {phases.filter(p => !p.isRequired).length} optional phases
              </div>
              <div className="text-sm text-muted-foreground">
                {phases.filter(p => p.startDate && p.endDate).length} phases scheduled, {phases.filter(p => !p.startDate || !p.endDate).length} unscheduled
              </div>
            </div>
            <Button onClick={handleSaveConfiguration} className="px-8">
              <CheckCircle className="h-4 w-4 mr-2" />
              Save Configuration & Continue
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
