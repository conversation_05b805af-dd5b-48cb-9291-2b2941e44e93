import Link from "next/link"
// import { ThemeToggle } from "@/components/theme-toggle"
import { UserNav } from "./user-nav"
import ThemeToggle from "@/components/theme-toggle"
// import { UserNav } from "@/components/user-nav"

type MainHeaderProps = {
  title: string
  showProjectLink?: boolean
}

export function MainHeader({ title, showProjectLink = false }: MainHeaderProps) {
  return (
    <header className="sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between px-4 md:px-8">
        <div className="flex items-center space-x-4">
          <Link href="/" className="text-xl font-bold text-primary">
            AI Interviewer
          </Link>
          {showProjectLink && <span className="text-muted-foreground">/ {title}</span>}
        </div>
        <div className="flex items-center space-x-4">
          <ThemeToggle />
          <UserNav />
        </div>
      </div>
    </header>
  )
}
