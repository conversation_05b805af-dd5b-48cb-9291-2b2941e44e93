"use client"

import * as React from "react"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Separator } from "@/components/ui/separator"
import { Edit, DollarSign, MapPin, Clock, GraduationCap, Award, Heart, Calendar } from "lucide-react"

type JobOfferSectionProps = {
  projectId: string
  onNext: () => void // Add onNext prop
}

export function JobOfferSection({ projectId, onNext }: JobOfferSectionProps) {
  // Basic job information
  const [jobTitle, setJobTitle] = React.useState("")
  const [jobDescription, setJobDescription] = React.useState("")
  const [location, setLocation] = React.useState("")
  const [department, setDepartment] = React.useState("")
  const [company, setCompany] = React.useState("")
  
  // Employment details
  const [employmentType, setEmploymentType] = React.useState("")
  const [workArrangement, setWorkArrangement] = React.useState("")
  const [experienceLevel, setExperienceLevel] = React.useState("")
  
  // Compensation
  const [salaryType, setSalaryType] = React.useState("range")
  const [salaryMin, setSalaryMin] = React.useState("")
  const [salaryMax, setSalaryMax] = React.useState("")
  const [salaryFixed, setSalaryFixed] = React.useState("")
  const [currency, setCurrency] = React.useState("USD")
  const [salaryPeriod, setSalaryPeriod] = React.useState("yearly")
  
  // Benefits
  const [healthInsurance, setHealthInsurance] = React.useState(false)
  const [dentalInsurance, setDentalInsurance] = React.useState(false)
  const [visionInsurance, setVisionInsurance] = React.useState(false)
  const [retirementPlan, setRetirementPlan] = React.useState(false)
  const [paidTimeOff, setPaidTimeOff] = React.useState("")
  const [flexibleSchedule, setFlexibleSchedule] = React.useState(false)
  const [remoteWork, setRemoteWork] = React.useState(false)
  const [professionalDevelopment, setProfessionalDevelopment] = React.useState(false)
  const [stockOptions, setStockOptions] = React.useState(false)
  const [bonusStructure, setBonusStructure] = React.useState("")
  const [additionalBenefits, setAdditionalBenefits] = React.useState("")
  
  // Requirements
  const [educationLevel, setEducationLevel] = React.useState("")
  const [yearsExperience, setYearsExperience] = React.useState("")
  const [requiredSkills, setRequiredSkills] = React.useState<string[]>([])
  const [preferredSkills, setPreferredSkills] = React.useState<string[]>([])
  const [certifications, setCertifications] = React.useState("")
  const [languages, setLanguages] = React.useState("")
  
  // Application details
  const [applicationDeadline, setApplicationDeadline] = React.useState("")
  const [startDate, setStartDate] = React.useState("")
  const [contactEmail, setContactEmail] = React.useState("")
  const [applicationInstructions, setApplicationInstructions] = React.useState("")
  
  // AI extracted data (keeping existing ones)
  const [extractedSkills, setExtractedSkills] = React.useState<string[]>([])
  const [extractedExperience, setExtractedExperience] = React.useState<string>("")
  const [extractedEducation, setExtractedEducation] = React.useState<string>("")

  React.useEffect(() => {
    // Simulate fetching existing job offer data for the project
    if (projectId === "proj-1") {
      setJobTitle("Senior Software Engineer")
      setCompany("TechCorp Inc.")
      setDepartment("Engineering")
      setJobDescription(
        "We are looking for a highly skilled Senior Software Engineer to join our dynamic team. You will be responsible for designing, developing, and maintaining complex software systems. Strong proficiency in React, Node.js, and cloud platforms is required. Experience with microservices architecture and agile methodologies is a plus.",
      )
      setLocation("San Francisco, USA")
      setEmploymentType("full-time")
      setWorkArrangement("hybrid")
      setExperienceLevel("senior")
      setSalaryMin("120000")
      setSalaryMax("160000")
      setCurrency("USD")
      setSalaryPeriod("yearly")
      setHealthInsurance(true)
      setDentalInsurance(true)
      setRetirementPlan(true)
      setPaidTimeOff("25 days")
      setFlexibleSchedule(true)
      setRemoteWork(true)
      setProfessionalDevelopment(true)
      setEducationLevel("bachelor")
      setYearsExperience("5+")
      setExtractedSkills(["React", "Node.js", "AWS", "Microservices", "Agile"])
      setExtractedExperience("5+ years")
      setExtractedEducation("Bachelor's Degree in Computer Science")
    } else {
      // Reset all fields for new projects
      setJobTitle("")
      setCompany("")
      setDepartment("")
      setJobDescription("")
      setLocation("")
      setEmploymentType("")
      setWorkArrangement("")
      setExperienceLevel("")
      setSalaryMin("")
      setSalaryMax("")
      setSalaryFixed("")
      setExtractedSkills([])
      setExtractedExperience("")
      setExtractedEducation("")
    }
  }, [projectId])

  const handleSaveOffer = () => {
    const jobOfferData = {
      // Basic Information
      jobTitle,
      company,
      department,
      jobDescription,
      location,
      
      // Employment Details
      employmentType,
      workArrangement,
      experienceLevel,
      
      // Compensation
      salary: salaryType === "range" 
        ? { type: "range", min: salaryMin, max: salaryMax, currency, period: salaryPeriod }
        : { type: "fixed", amount: salaryFixed, currency, period: salaryPeriod },
      
      // Benefits
      benefits: {
        healthInsurance,
        dentalInsurance,
        visionInsurance,
        retirementPlan,
        paidTimeOff,
        flexibleSchedule,
        remoteWork,
        professionalDevelopment,
        stockOptions,
        bonusStructure,
        additionalBenefits,
      },
      
      // Requirements
      requirements: {
        educationLevel,
        yearsExperience,
        requiredSkills,
        preferredSkills,
        certifications,
        languages,
      },
      
      // Application Details
      applicationDetails: {
        deadline: applicationDeadline,
        startDate,
        contactEmail,
        instructions: applicationInstructions,
      },
      
      // AI Extracted Data
      extractedMetadata: {
        skills: extractedSkills,
        experience: extractedExperience,
        education: extractedEducation,
      },
    }
    
    console.log("Saving Comprehensive Job Offer:", jobOfferData)
    // In a real app, you'd send this data to your backend
    onNext() // Move to the next step after saving
  }

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle>Define Job Offer</CardTitle>
        <CardDescription>
          Provide details about the job position. Our AI will help extract key requirements.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-8">
        {/* Basic Job Information */}
        <div className="space-y-6">
          <div className="flex items-center gap-2">
            <Award className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-semibold">Basic Information</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="grid gap-2">
              <Label htmlFor="jobTitle">Job Title *</Label>
              <Input
                id="jobTitle"
                placeholder="e.g., Senior Software Engineer"
                value={jobTitle}
                onChange={(e) => setJobTitle(e.target.value)}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="company">Company Name *</Label>
              <Input
                id="company"
                placeholder="e.g., TechCorp Inc."
                value={company}
                onChange={(e) => setCompany(e.target.value)}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="department">Department</Label>
              <Input
                id="department"
                placeholder="e.g., Engineering, Marketing"
                value={department}
                onChange={(e) => setDepartment(e.target.value)}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="location">
                <MapPin className="h-4 w-4 inline mr-1" />
                Location *
              </Label>
              <Input
                id="location"
                placeholder="e.g., New York, USA or Remote"
                value={location}
                onChange={(e) => setLocation(e.target.value)}
                required
              />
            </div>
          </div>
          
          <div className="grid gap-2">
            <Label htmlFor="jobDescription">Job Description *</Label>
            <Textarea
              id="jobDescription"
              placeholder="Provide a detailed job description, responsibilities, and what makes this role exciting..."
              rows={6}
              value={jobDescription}
              onChange={(e) => setJobDescription(e.target.value)}
              required
            />
          </div>
        </div>

        <Separator />

        {/* Employment Details */}
        <div className="space-y-6">
          <div className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-semibold">Employment Details</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="grid gap-2">
              <Label>Employment Type *</Label>
              <Select value={employmentType} onValueChange={setEmploymentType}>
                <SelectTrigger>
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="full-time">Full-time</SelectItem>
                  <SelectItem value="part-time">Part-time</SelectItem>
                  <SelectItem value="contract">Contract</SelectItem>
                  <SelectItem value="temporary">Temporary</SelectItem>
                  <SelectItem value="internship">Internship</SelectItem>
                  <SelectItem value="freelance">Freelance</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid gap-2">
              <Label>Work Arrangement</Label>
              <Select value={workArrangement} onValueChange={setWorkArrangement}>
                <SelectTrigger>
                  <SelectValue placeholder="Select arrangement" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="on-site">On-site</SelectItem>
                  <SelectItem value="remote">Remote</SelectItem>
                  <SelectItem value="hybrid">Hybrid</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid gap-2">
              <Label>Experience Level</Label>
              <Select value={experienceLevel} onValueChange={setExperienceLevel}>
                <SelectTrigger>
                  <SelectValue placeholder="Select level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="entry">Entry Level</SelectItem>
                  <SelectItem value="junior">Junior</SelectItem>
                  <SelectItem value="mid">Mid Level</SelectItem>
                  <SelectItem value="senior">Senior</SelectItem>
                  <SelectItem value="lead">Lead</SelectItem>
                  <SelectItem value="principal">Principal</SelectItem>
                  <SelectItem value="director">Director</SelectItem>
                  <SelectItem value="executive">Executive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <Separator />

        {/* Compensation */}
        <div className="space-y-6">
          <div className="flex items-center gap-2">
            <DollarSign className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-semibold">Compensation</h3>
          </div>
          
          <div className="grid gap-4">
            <div className="flex items-center space-x-4">
              <Label>Salary Type:</Label>
              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  id="salary-range"
                  name="salaryType"
                  value="range"
                  checked={salaryType === "range"}
                  onChange={(e) => setSalaryType(e.target.value)}
                />
                <Label htmlFor="salary-range">Range</Label>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  id="salary-fixed"
                  name="salaryType"
                  value="fixed"
                  checked={salaryType === "fixed"}
                  onChange={(e) => setSalaryType(e.target.value)}
                />
                <Label htmlFor="salary-fixed">Fixed Amount</Label>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {salaryType === "range" ? (
                <>
                  <div className="grid gap-2">
                    <Label htmlFor="salaryMin">Minimum Salary</Label>
                    <Input
                      id="salaryMin"
                      type="number"
                      placeholder="50000"
                      value={salaryMin}
                      onChange={(e) => setSalaryMin(e.target.value)}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="salaryMax">Maximum Salary</Label>
                    <Input
                      id="salaryMax"
                      type="number"
                      placeholder="80000"
                      value={salaryMax}
                      onChange={(e) => setSalaryMax(e.target.value)}
                    />
                  </div>
                </>
              ) : (
                <div className="grid gap-2">
                  <Label htmlFor="salaryFixed">Salary Amount</Label>
                  <Input
                    id="salaryFixed"
                    type="number"
                    placeholder="65000"
                    value={salaryFixed}
                    onChange={(e) => setSalaryFixed(e.target.value)}
                  />
                </div>
              )}
              
              <div className="grid gap-2">
                <Label>Currency</Label>
                <Select value={currency} onValueChange={setCurrency}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="USD">USD</SelectItem>
                    <SelectItem value="EUR">EUR</SelectItem>
                    <SelectItem value="GBP">GBP</SelectItem>
                    <SelectItem value="CAD">CAD</SelectItem>
                    <SelectItem value="AUD">AUD</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="grid gap-2">
                <Label>Period</Label>
                <Select value={salaryPeriod} onValueChange={setSalaryPeriod}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="yearly">Yearly</SelectItem>
                    <SelectItem value="monthly">Monthly</SelectItem>
                    <SelectItem value="weekly">Weekly</SelectItem>
                    <SelectItem value="hourly">Hourly</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="bonusStructure">Bonus Structure</Label>
              <Input
                id="bonusStructure"
                placeholder="e.g., Annual performance bonus up to 20%"
                value={bonusStructure}
                onChange={(e) => setBonusStructure(e.target.value)}
              />
            </div>
          </div>
        </div>

        <Separator />

        {/* Benefits */}
        <div className="space-y-6">
          <div className="flex items-center gap-2">
            <Heart className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-semibold">Benefits & Perks</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h4 className="font-medium">Health & Wellness</h4>
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="health-insurance"
                    checked={healthInsurance}
                    onCheckedChange={(checked) => setHealthInsurance(checked === true)}
                  />
                  <Label htmlFor="health-insurance">Health Insurance</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="dental-insurance"
                    checked={dentalInsurance}
                    onCheckedChange={(checked) => setDentalInsurance(checked === true)}
                  />
                  <Label htmlFor="dental-insurance">Dental Insurance</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="vision-insurance"
                    checked={visionInsurance}
                    onCheckedChange={(checked) => setVisionInsurance(checked === true)}
                  />
                  <Label htmlFor="vision-insurance">Vision Insurance</Label>
                </div>
              </div>
            </div>
            
            <div className="space-y-4">
              <h4 className="font-medium">Financial</h4>
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="retirement-plan"
                    checked={retirementPlan}
                    onCheckedChange={(checked) => setRetirementPlan(checked === true)}
                  />
                  <Label htmlFor="retirement-plan">401(k) / Retirement Plan</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="stock-options"
                    checked={stockOptions}
                    onCheckedChange={(checked) => setStockOptions(checked === true)}
                  />
                  <Label htmlFor="stock-options">Stock Options / Equity</Label>
                </div>
              </div>
            </div>
            
            <div className="space-y-4">
              <h4 className="font-medium">Work-Life Balance</h4>
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="flexible-schedule"
                    checked={flexibleSchedule}
                    onCheckedChange={(checked) => setFlexibleSchedule(checked === true)}
                  />
                  <Label htmlFor="flexible-schedule">Flexible Schedule</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="remote-work-benefit"
                    checked={remoteWork}
                    onCheckedChange={(checked) => setRemoteWork(checked === true)}
                  />
                  <Label htmlFor="remote-work-benefit">Remote Work Options</Label>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="paid-time-off">Paid Time Off</Label>
                  <Input
                    id="paid-time-off"
                    placeholder="e.g., 25 days annually"
                    value={paidTimeOff}
                    onChange={(e) => setPaidTimeOff(e.target.value)}
                  />
                </div>
              </div>
            </div>
            
            <div className="space-y-4">
              <h4 className="font-medium">Development</h4>
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="professional-development"
                    checked={professionalDevelopment}
                    onCheckedChange={(checked) => setProfessionalDevelopment(checked === true)}
                  />
                  <Label htmlFor="professional-development">Learning & Development Budget</Label>
                </div>
              </div>
            </div>
          </div>
          
          <div className="grid gap-2">
            <Label htmlFor="additional-benefits">Additional Benefits</Label>
            <Textarea
              id="additional-benefits"
              placeholder="List any other benefits like gym membership, food allowance, transportation, etc."
              rows={3}
              value={additionalBenefits}
              onChange={(e) => setAdditionalBenefits(e.target.value)}
            />
          </div>
        </div>

        <Separator />

        {/* Requirements */}
        <div className="space-y-6">
          <div className="flex items-center gap-2">
            <GraduationCap className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-semibold">Requirements</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="grid gap-2">
              <Label>Education Level</Label>
              <Select value={educationLevel} onValueChange={setEducationLevel}>
                <SelectTrigger>
                  <SelectValue placeholder="Select education level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="high-school">High School</SelectItem>
                  <SelectItem value="associate">Associate Degree</SelectItem>
                  <SelectItem value="bachelor">Bachelor's Degree</SelectItem>
                  <SelectItem value="master">Master's Degree</SelectItem>
                  <SelectItem value="phd">PhD</SelectItem>
                  <SelectItem value="none">No Formal Education Required</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="years-experience">Years of Experience</Label>
              <Input
                id="years-experience"
                placeholder="e.g., 3-5 years, 5+ years"
                value={yearsExperience}
                onChange={(e) => setYearsExperience(e.target.value)}
              />
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="certifications">Required Certifications</Label>
              <Input
                id="certifications"
                placeholder="e.g., AWS Certified, PMP, etc."
                value={certifications}
                onChange={(e) => setCertifications(e.target.value)}
              />
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="languages">Languages</Label>
              <Input
                id="languages"
                placeholder="e.g., English (fluent), Spanish (conversational)"
                value={languages}
                onChange={(e) => setLanguages(e.target.value)}
              />
            </div>
          </div>
        </div>

        <Separator />

        {/* Application Details */}
        <div className="space-y-6">
          <div className="flex items-center gap-2">
            <Calendar className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-semibold">Application Details</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="grid gap-2">
              <Label htmlFor="application-deadline">Application Deadline</Label>
              <Input
                id="application-deadline"
                type="date"
                value={applicationDeadline}
                onChange={(e) => setApplicationDeadline(e.target.value)}
              />
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="start-date">Expected Start Date</Label>
              <Input
                id="start-date"
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
              />
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="contact-email">Contact Email</Label>
              <Input
                id="contact-email"
                type="email"
                placeholder="<EMAIL>"
                value={contactEmail}
                onChange={(e) => setContactEmail(e.target.value)}
              />
            </div>
          </div>
          
          <div className="grid gap-2">
            <Label htmlFor="application-instructions">Application Instructions</Label>
            <Textarea
              id="application-instructions"
              placeholder="Special instructions for applicants, documents to include, etc."
              rows={3}
              value={applicationInstructions}
              onChange={(e) => setApplicationInstructions(e.target.value)}
            />
          </div>
        </div>

        <Separator />

        {/* AI Extracted Metadata */}
        <div className="space-y-4 rounded-md border p-4">
          <h3 className="text-lg font-semibold">AI Extracted Metadata</h3>
          <div className="grid gap-2">
            <Label>Skills</Label>
            <div className="flex flex-wrap gap-2">
              {extractedSkills.length > 0 ? (
                extractedSkills.map((skill, index) => (
                  <Badge key={index} variant="secondary" className="flex items-center gap-1">
                    {skill} <Edit className="h-3 w-3 cursor-pointer" />
                  </Badge>
                ))
              ) : (
                <span className="text-sm text-muted-foreground">No skills extracted yet.</span>
              )}
            </div>
          </div>
          <div className="grid gap-2">
            <Label>Experience</Label>
            <div className="flex items-center gap-2">
              <Badge variant="secondary">{extractedExperience || "N/A"}</Badge>
              <Edit className="h-3 w-3 cursor-pointer" />
            </div>
          </div>
          <div className="grid gap-2">
            <Label>Education Level</Label>
            <div className="flex items-center gap-2">
              <Badge variant="secondary">{extractedEducation || "N/A"}</Badge>
              <Edit className="h-3 w-3 cursor-pointer" />
            </div>
          </div>
          <p className="text-sm text-muted-foreground">
            These fields are auto-extracted from the job description. You can edit them if needed.
          </p>
        </div>

        <Button onClick={handleSaveOffer} className="w-full" size="lg">
          Save Complete Job Offer & Continue
        </Button>
      </CardContent>
    </Card>
  )
}
