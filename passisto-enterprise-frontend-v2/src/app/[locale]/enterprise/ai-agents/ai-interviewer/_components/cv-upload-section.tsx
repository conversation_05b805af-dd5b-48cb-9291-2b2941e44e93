"use client"

import * as React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { CheckCircle, FileText, XCircle } from "lucide-react"
import { Badge } from "@/components/ui/badge"

type UploadedFile = {
  id: string
  name: string
  status: "Pending" | "Parsing" | "Parsed" | "Failed"
  extractedData?: {
    name: string
    skills: string[]
    experience: string
  }
}

type CvUploadSectionProps = {
  projectId: string
  onNext: () => void // Add onNext prop
}

export function CvUploadSection({ projectId, onNext }: CvUploadSectionProps) {
  const [uploadedFiles, setUploadedFiles] = React.useState<UploadedFile[]>([])
  const [isDragging, setIsDragging] = React.useState(false)

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (files) {
      handleFiles(Array.from(files))
    }
  }

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = () => {
    setIsDragging(false)
  }

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    setIsDragging(false)
    const files = event.dataTransfer.files
    if (files) {
      handleFiles(Array.from(files))
    }
  }

  const handleFiles = (files: File[]) => {
    const newFiles: UploadedFile[] = files.map((file) => ({
      id: `${file.name}-${Date.now()}`,
      name: file.name,
      status: "Pending",
    }))
    setUploadedFiles((prev) => [...prev, ...newFiles])

    // Simulate parsing
    newFiles.forEach((file) => {
      setTimeout(
        () => {
          setUploadedFiles((prev) =>
            prev.map((f) =>
              f.id === file.id
                ? {
                    ...f,
                    status: "Parsed",
                    extractedData: {
                      name: file.name.split(".")[0].replace(/_/g, " "),
                      skills: ["React", "Node.js", "SQL"],
                      experience: "3 years",
                    },
                  }
                : f,
            ),
          )
        },
        2000 + Math.random() * 1000,
      ) // Simulate varying parsing times
    })
  }

  const handleConfirmImport = () => {
    const parsedFiles = uploadedFiles.filter((file) => file.status === "Parsed")
    console.log(`Confirming import of ${parsedFiles.length} parsed CVs for project ${projectId}`)
    // In a real app, send parsed data to backend
    onNext() // Move to the next step after confirming import
  }

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle>Upload Candidate CVs</CardTitle>
        <CardDescription>Drag and drop your candidate CVs (PDF, DOC, DOCX) here for parsing.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div
          className={`flex flex-col items-center justify-center rounded-lg border-2 border-dashed p-8 text-center transition-colors ${
            isDragging ? "border-primary bg-primary/5" : "border-muted-foreground/20"
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <FileText className="mb-4 h-12 w-12 text-muted-foreground" />
          <p className="text-lg font-medium text-muted-foreground">Drag & Drop your CVs here</p>
          <p className="text-sm text-muted-foreground">or</p>
          <Label htmlFor="file-upload" className="cursor-pointer text-primary hover:underline">
            Browse files
          </Label>
          <Input
            id="file-upload"
            type="file"
            multiple
            accept=".pdf,.doc,.docx"
            className="hidden"
            onChange={handleFileChange}
          />
        </div>

        {uploadedFiles.length > 0 && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Uploaded Files</h3>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>File Name</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Extracted Name</TableHead>
                  <TableHead>Extracted Skills</TableHead>
                  <TableHead>Extracted Experience</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {uploadedFiles.map((file) => (
                  <TableRow key={file.id}>
                    <TableCell className="font-medium">{file.name}</TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          file.status === "Parsed" ? "default" : file.status === "Failed" ? "destructive" : "secondary"
                        }
                      >
                        {file.status === "Parsed" && <CheckCircle className="mr-1 h-3 w-3" />}
                        {file.status === "Failed" && <XCircle className="mr-1 h-3 w-3" />}
                        {file.status}
                      </Badge>
                    </TableCell>
                    <TableCell>{file.extractedData?.name || "N/A"}</TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {file.extractedData?.skills.map((skill, i) => (
                          <Badge key={i} variant="outline">
                            {skill}
                          </Badge>
                        )) || "N/A"}
                      </div>
                    </TableCell>
                    <TableCell>{file.extractedData?.experience || "N/A"}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            <Button
              onClick={handleConfirmImport}
              className="w-full"
              disabled={uploadedFiles.filter((f) => f.status === "Parsed").length === 0}
            >
              Confirm Import ({uploadedFiles.filter((f) => f.status === "Parsed").length} parsed) & Continue
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
