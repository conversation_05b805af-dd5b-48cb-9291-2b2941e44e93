"use client"

import Link from "next/link" // Import Link
import { ArrowLeft } from "lucide-react" // Import ArrowLeft icon

// import { MainHeader } from "@/components/main-header"
// import { CreateProjectWizard } from "@/components/create-project-wizard"
import { <PERSON><PERSON> } from "@/components/ui/button" // Import Button
import { MainHeader } from "../../_components/main-header"
import { CreateProjectWizard } from "../../_components/create-project-wizard"

export default function CreateProjectPage() {
  return (
    <div className="w-full flex-col bg-background">
      {/* <MainHeader title="Create New Project" showProjectLink={false} /> */}
      <main className="flex flex-1 flex-col items-center justify-center p-4 md:p-8">
        <div className="w-full max-w-7xl mb-4 flex justify-start">
          {" "}
          {/* Adjust positioning for wizard */}
          <Button variant="ghost" asChild>
            <Link href="/">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Projects
            </Link>
          </Button>
        </div>
        <CreateProjectWizard />
      </main>
    </div>
  )
}
