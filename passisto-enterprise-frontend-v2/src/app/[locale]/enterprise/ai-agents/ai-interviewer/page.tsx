"use client"
import Link from "next/link" // Import Link
import { PlusCircle } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { MainHeader } from "./_components/main-header"
import { ProjectCard } from "./_components/project-card"
// Removed CreateProjectDialog

type Project = {
  id: string
  name: string
  jobTitle?: string
  dateCreated: string
  numCandidates: number
  progress: number
  status: "In Progress" | "Complete" | "Draft"
}

const projects: Project[] = [
  {
    id: "proj-1",
    name: "Senior Software Engineer Hiring",
    jobTitle: "Senior Software Engineer",
    dateCreated: "2023-10-26",
    numCandidates: 45,
    progress: 75,
    status: "In Progress",
  },
  {
    id: "proj-2",
    name: "Sales Manager Recruitment",
    jobTitle: "Sales Manager",
    dateCreated: "2023-11-15",
    numCandidates: 20,
    progress: 50,
    status: "In Progress",
  },
  {
    id: "proj-3",
    name: "Product Designer Search",
    jobTitle: "Product Designer",
    dateCreated: "2023-09-01",
    numCandidates: 12,
    progress: 100,
    status: "Complete",
  },
  {
    id: "proj-4",
    name: "Marketing Specialist",
    jobTitle: "Marketing Specialist",
    dateCreated: "2024-01-20",
    numCandidates: 5,
    progress: 20,
    status: "Draft",
  },
]

export default function ProjectSelectionPage() {
  // Removed isCreateProjectDialogOpen state

  return (
    <div className="flex min-h-screen w-full flex-col bg-background">
      {/* <MainHeader title="Your Recruitment Projects" /> */}
      <div className="flex flex-1 flex-col p-4 md:p-8">
        <header className="mb-8 flex items-center justify-between">
          <h1 className="text-3xl font-bold text-foreground">Your Recruitment Projects</h1>
          <Button asChild>
            <Link href="ai-interviewer/projects/create">
              {" "}
              {/* Link to the new creation page */}
              <PlusCircle className="mr-2 h-4 w-4" />
              Create New Project
            </Link>
          </Button>
        </header>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {projects.map((project) => (
            <ProjectCard key={project.id} project={project} />
          ))}
        </div>

        {/* Removed CreateProjectDialog */}
      </div>
    </div>
  )
}
