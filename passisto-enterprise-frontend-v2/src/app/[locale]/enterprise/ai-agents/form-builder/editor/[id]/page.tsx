"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { FormBuilder } from "../../_components/form-builder"
import { Button } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import type { StoredForm } from "../../_lib/types"
import { useAuth } from "@clerk/nextjs"
import { getFormById } from "../../_lib/actions" // <-- Ajoute cet import
import { formBuilderPermissions } from "@/utils/ACTION_PERMISSIONS"
import { useBackendUser } from "@/hooks/useBackendUser"
import { toast } from "sonner"

export default function EditorPage({
  params,
}: {
  params: { id: string; companyId: string }
}) {
  const router = useRouter()
  const [form, setForm] = useState<StoredForm | null>(null)
  const [loading, setLoading] = useState(true)
  const { getToken } = useAuth()
  const formId = params.id
  const { backendUser, loading: backendUserLoading } = useBackendUser()

  useEffect(() => {
    // Only check permissions after backend<PERSON><PERSON> has loaded
    if (!backendUserLoading && backendUser) {
      const permissions = backendUser?.permissions ?? []
      setLoading(true)

      // Check if user has at least one form builder-related permission
      const hasAnyFormBuilderPermission =
        formBuilderPermissions.canCreateForm(permissions) ||
        formBuilderPermissions.canUpdateForm(permissions) ||
        formBuilderPermissions.canDeleteForm(permissions) ||
        formBuilderPermissions.canExportFormData(permissions) ||
        formBuilderPermissions.canPublishForm(permissions) ||
        formBuilderPermissions.canExportFormData(permissions) ||
        formBuilderPermissions.canManageFormBuilder(permissions)

      // Redirect if user doesn't have any form builder-related permissions
      if (!hasAnyFormBuilderPermission) {
        toast.error("Permission denied", {
          description: "You don't have permission to access the form builder page.",
        })

        router.back()
      }
      setLoading(false)
    }
  }, [backendUser, backendUserLoading, router])

  useEffect(() => {
    const loadForm = async () => {
      try {
        setLoading(true)
        const token = await getToken()
        if (!token) {
          throw new Error("No authentication token found")
        }
        // Passe une fonction qui retourne une Promise<string>
        const data = await getFormById(formId, params.companyId, () => Promise.resolve(token))
        setForm(data)
      } catch (err) {
        console.error("Failed to load form:", err)

        // Fallback to localStorage if API fails
        try {
          const storedForms = localStorage.getItem("forms")
          if (storedForms) {
            const forms = JSON.parse(storedForms) as StoredForm[]
            const foundForm = forms.find((f) => f.id === formId)
            if (foundForm) {
              console.log("Found form in localStorage:", foundForm)
              setForm(foundForm)
            } else {
              console.error("Form not found in localStorage")
              setForm(null)
            }
          }
        } catch (localStorageError) {
          console.error("Error accessing localStorage:", localStorageError)
          setForm(null)
        }
      } finally {
        setLoading(false)
      }
    }

    loadForm()
  }, [formId, getToken, params.companyId])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    )
  }

  if (!form) {
    return (
      <div className="min-h-screen p-4 md:p-8 bg-background">
        <div className="max-w-3xl mx-auto text-center">
          <h1 className="text-2xl font-bold mb-4 text-foreground">Form Not Found</h1>
          <p className="mb-6 text-muted-foreground">The form you're looking for doesn't exist or has been deleted.</p>
          <Button onClick={() => router.push("/enterprise/ai-agents/form-builder/dashboard")}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Dashboard
          </Button>
        </div>
      </div>
    )
  }

  return (
    <main className="p-4 md:p-8 bg-background">
      <div className="max-w-7xl mx-auto">
        <div className="mb-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="mb-3 hover:bg-gray-50 transition-colors"
            size="sm"
          >
            <ArrowLeft className="mr-1 h-3 w-3" />
            Back
          </Button>
        </div>
        <FormBuilder form={form} />
      </div>
    </main>
  )
}
