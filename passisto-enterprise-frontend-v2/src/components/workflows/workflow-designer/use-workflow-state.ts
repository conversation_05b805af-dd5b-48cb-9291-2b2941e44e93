"use client";

import { useState, useCallback } from "react";
import {
  useNodesState,
  useEdgesState,
  addEdge,
  type Node,
  type Edge,
  type Connection
} from "@xyflow/react";
import { toast } from "@/hooks/use-toast";
import { Workflow } from "../workflows-table";
import { NodeUpdateData } from "./types";

// Initial nodes and edges
const initialNodes: Node[] = [
  {
    id: "start",
    type: "start",
    data: { label: "Start" },
    position: { x: 250, y: 5 },
  },
];

const initialEdges: Edge[] = [];

export function useWorkflowState(currentWorkflow: Workflow | null) {
  // Nodes and edges state
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

  const [viewport, setViewport] = useState({
    x: 0,
    y: 0,
    zoom: 1,
  });

  // Selection state
  const [selectedNodes, setSelectedNodes] = useState<Node[]>([]);
  const [selectedEdge, setSelectedEdge] = useState<Edge | null>(null);

  // Workflow metadata state
  const [workflowTitle, setWorkflowTitle] = useState(currentWorkflow?.name || "New Workflow");
  const [workflowDescription, setWorkflowDescription] = useState(currentWorkflow?.description || "");
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [isSaveDialogOpen, setIsSaveDialogOpen] = useState(false);

  // UI state
  const [showMiniMap, setShowMiniMap] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  // Drag state
  const [draggedNodeId, setDraggedNodeId] = useState<string | null>(null);
  const [dragStartPositions, setDragStartPositions] = useState<{ [key: string]: { x: number; y: number } }>({});

  // Handle connections between nodes
  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  // Handle node selection
  const onNodeClick = useCallback(
    (event: React.MouseEvent, node: Node) => {
      event.stopPropagation(); // Prevent event bubbling

      if (event.shiftKey || event.ctrlKey || event.metaKey) {
        // Add to selection if shift/ctrl/cmd is pressed
        setSelectedNodes((prev) => {
          if (prev.find((n) => n.id === node.id)) {
            // Remove if already selected
            return prev.filter((n) => n.id !== node.id);
          }
          // Add if not selected
          return [...prev, node];
        });
      } else {
        // Replace selection if no modifier key is pressed
        setSelectedNodes([node]);
      }
      setSelectedEdge(null); // Deselect any selected edge
    },
    []
  );

  // Handle edge selection
  const onEdgeClick = useCallback(
    (event: React.MouseEvent, edge: Edge) => {
      event.stopPropagation(); // Prevent event bubbling
      setSelectedEdge(edge);
      setSelectedNodes([]); // Deselect all selected nodes
    },
    []
  );

  // Handle background click to deselect
  const onPaneClick = useCallback(() => {
    setSelectedNodes([]);
    setSelectedEdge(null);
  }, []);

  // Update node properties
  const updateNodeProperties = useCallback(
    (id: string, data: NodeUpdateData) => {
      setNodes((nds) =>
        nds.map((node) => {
          if (node.id === id) {
            return { ...node, data: { ...node.data, ...data } };
          }
          return node;
        })
      );
    },
    [setNodes]
  );

  // Delete selected edge
  const deleteSelectedEdge = useCallback(() => {
    if (selectedEdge) {
      setEdges((eds) => eds.filter((edge) => edge.id !== selectedEdge.id));
      setSelectedEdge(null);

      toast({
        title: "Edge deleted",
        description: "Connection has been removed.",
      });
    }
  }, [selectedEdge, setEdges]);

  // Delete selected nodes
  const deleteSelectedNodes = useCallback(() => {
    if (selectedNodes.length > 0) {
      // Prevent deletion of the start node
      if (selectedNodes.some(node => node.type === "start")) {
        toast({
          title: "Cannot delete start node",
          description: "The start node is required for workflow execution.",
          variant: "destructive",
        });
        return;
      }

      // Delete all connected edges first
      const nodeIds = selectedNodes.map(node => node.id);
      setEdges((eds) => eds.filter((edge) =>
        !nodeIds.includes(edge.source) && !nodeIds.includes(edge.target)
      ));

      // Delete the nodes
      setNodes((nds) => nds.filter((node) => !nodeIds.includes(node.id)));
      setSelectedNodes([]);

      toast({
        title: "Nodes deleted",
        description: "Selected nodes have been removed from the workflow.",
      });
    }
  }, [selectedNodes, setNodes, setEdges]);

  // Handle node drag start
  const onNodeDragStart = useCallback((event: React.MouseEvent, node: Node) => {
    setDraggedNodeId(node.id);
    // Store initial positions of all selected nodes plus the dragged node
    const positions: { [key: string]: { x: number; y: number } } = {};
    // Store the dragged node's position
    positions[node.id] = { ...node.position };
    // Store positions of other selected nodes
    selectedNodes.forEach(selectedNode => {
      if (selectedNode.id !== node.id) {
        positions[selectedNode.id] = { ...selectedNode.position };
      }
    });
    setDragStartPositions(positions);
  }, [selectedNodes]);

  // Handle node drag
  const onNodeDrag = useCallback((event: React.MouseEvent, node: Node) => {
    if (draggedNodeId === node.id) {
      // Calculate the total movement from the start position
      const startPos = dragStartPositions[node.id];
      if (!startPos) return; // Guard against undefined startPos

      const dx = node.position.x - startPos.x;
      const dy = node.position.y - startPos.y;

      // Move all selected nodes by the same amount
      setNodes((nds) =>
        nds.map((n) => {
          if (selectedNodes.find(selected => selected.id === n.id)) {
            const startPos = dragStartPositions[n.id];
            if (!startPos) return n; // Guard against undefined startPos
            return {
              ...n,
              position: {
                x: startPos.x + dx,
                y: startPos.y + dy,
              },
            };
          }
          return n;
        })
      );
    }
  }, [draggedNodeId, selectedNodes, setNodes, dragStartPositions]);

  // Handle node drag end
  const onNodeDragEnd = useCallback(() => {
    setDraggedNodeId(null);
    setDragStartPositions({});
  }, []);

  // Reset workflow state
  const resetWorkflow = useCallback(() => {
    setNodes(initialNodes);
    setEdges(initialEdges);
    setWorkflowTitle("New Workflow");
    setWorkflowDescription("");
  }, [setNodes, setEdges]);

  // Load workflow from current workflow prop
  const loadWorkflowFromProp = useCallback(() => {
    if (currentWorkflow) {
      if (currentWorkflow.nodes && currentWorkflow.nodes.length > 0) {
        // Reset node states when loading a workflow
        const cleanedNodes = currentWorkflow.nodes.map(node => {
          // Log AskAI node model for debugging
          if (node.type === 'ask-ai') {
            console.log(`Loading AskAI node ${node.id} with model:`, node.data?.model);
          }

          // Remove any selection styling
          const { style, selected, ...cleanNode } = node;

          // If the node had custom styling (not selection-related), preserve it
          let newStyle = undefined;
          if (style) {
            const { boxShadow, border, borderRadius, ...restStyle } = style;
            if (Object.keys(restStyle).length > 0) {
              newStyle = restStyle;
            }
          }

          // Clean runtime state flags from node data
          const cleanData = { ...node.data };
          if (cleanData) {
            delete cleanData.isActive;
            delete cleanData.isWaiting;
            delete cleanData.isCompleted;
            delete cleanData.isSkipped;
          }

          return {
            ...cleanNode,
            style: newStyle,
            data: cleanData
          };
        });
        setNodes(cleanedNodes);
      } else {
        setNodes(initialNodes);
      }

      setEdges(currentWorkflow.edges || []);
      setViewport(currentWorkflow.viewport);
      setWorkflowTitle(currentWorkflow.name || "New Workflow");
      setWorkflowDescription(currentWorkflow.description || "");

      // Return the viewport information for use in ReactFlow
      return currentWorkflow.viewport;
    } else {
      resetWorkflow();
    }
  }, [currentWorkflow, setNodes, setEdges, resetWorkflow]);

  return {
    // State
    nodes,
    edges,
    viewport,
    selectedNodes,
    selectedEdge,
    workflowTitle,
    workflowDescription,
    isEditingTitle,
    isSaveDialogOpen,
    showMiniMap,
    isInitialized,

    // State setters
    setNodes,
    setEdges,
    setSelectedNodes,
    setSelectedEdge,
    setWorkflowTitle,
    setWorkflowDescription,
    setIsEditingTitle,
    setIsSaveDialogOpen,
    setShowMiniMap,
    setIsInitialized,

    // Event handlers
    onNodesChange,
    onEdgesChange,
    onConnect,
    onNodeClick,
    onEdgeClick,
    onPaneClick,
    onNodeDragStart,
    onNodeDrag,
    onNodeDragEnd,

    // Actions
    updateNodeProperties,
    deleteSelectedEdge,
    deleteSelectedNodes,
    loadWorkflowFromProp,
    resetWorkflow,
  };
}
