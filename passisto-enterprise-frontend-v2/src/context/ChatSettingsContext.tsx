"use client";

import React, { createContext, useContext, ReactNode, useState, useEffect, useCallback, useMemo } from 'react';
import axiosInstance from '@/config/axios';
import { ALL_INTEGRATIONS_GROUPS } from '@/utils/routes';
import { useAuth } from '@clerk/nextjs';

interface Alias {
  id: string;
  name: string;
}

interface Integration {
  id: string;
  name: string;
  collectionName: string;
  providerType: string;
  status: string;
  updateTime: number;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

// Define provider types
export type ProviderType = 'all' | 'ftp' | 'jira' | 'web';

interface ChatSettingsContextType {
  aliases: Alias[];
  selectedAliases: Alias[];
  toggleAlias: (alias: Alias) => void;
  isAliasSelected: (aliasId: string) => boolean;
  getSelectedAliasIds: () => string[];
  isLoading: boolean;
  error: string | null;
  providerFilter: ProviderType;
  setProviderFilter: (filter: ProviderType) => void;
  filteredAliases: Alias[];
}

const ChatSettingsContext = createContext<ChatSettingsContextType | undefined>(undefined);

// Get default selected alias based on environment variable
const getDefaultSelectedAlias = (aliases: Alias[]): Alias[] => {
  if (aliases.length === 0) return [];

  // const envAlias = process.env.NEXT_PUBLIC_CHATBOT_ALIAS;
  // if (envAlias) {
  //   const found = aliases.find((a: Alias) => a.id === envAlias);
  //   if (found) return [found];
  // }

  // Default to first alias if no environment variable is set or not found
  return [aliases[0]];
};

export function ChatSettingsProvider({ children }: { readonly children: ReactNode }) {
  const [allIntegrations, setAllIntegrations] = useState<Integration[]>([]);
  const [aliases, setAliases] = useState<Alias[]>([]);
  const [selectedAliases, setSelectedAliases] = useState<Alias[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [providerFilter, setProviderFilter] = useState<ProviderType>('all');
  const { getToken } = useAuth();

  // Compute filtered aliases based on provider filter
  const filteredAliases = useMemo(() => {
    if (providerFilter === 'all') {
      return aliases;
    }

    // Filter integrations by provider type and map to aliases format
    return allIntegrations
      .filter((integration: Integration) => integration.providerType === providerFilter)
      .map((integration: Integration) => ({
        id: integration.collectionName,
        name: integration.name
      }));
  }, [aliases, allIntegrations, providerFilter]);

  // Fetch integrations from the API
  useEffect(() => {
    const fetchIntegrations = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const token = await getToken();
        const headers = {
          headers: {
            Authorization: `Bearer ${token}`
          }
        };
        //TODO: build an endpoint where user can fetch data all indices which can belong to.
        const response = await axiosInstance.get(ALL_INTEGRATIONS_GROUPS, headers);

        // Store the full integration data
        setAllIntegrations(response.data);

        // Map integrations to aliases format
        const fetchedAliases = response.data.map((integration: Integration) => ({
          id: integration.collectionName,
          name: integration.name
        }));

        setAliases(fetchedAliases);

        // Set default selected aliases
        if (fetchedAliases.length > 0 && selectedAliases.length === 0) {
          setSelectedAliases(getDefaultSelectedAlias(fetchedAliases));
        }
      } catch (err) {
        console.error('Error fetching integrations:', err);
        setError('Failed to load integrations');

        // Fallback to empty arrays if API fails
        setAllIntegrations([]);
        setAliases([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchIntegrations();
  }, [getToken, selectedAliases.length]);

  // Toggle an alias selection
  const toggleAlias = useCallback((alias: Alias) => {
    setSelectedAliases((prev: Alias[]) => {
      const isCurrentlySelected = prev.some((a: Alias) => a.id === alias.id);

      if (isCurrentlySelected) {
        // Don't allow deselecting the last alias
        if (prev.length === 1) return prev;
        // Remove the alias
        return prev.filter((a: Alias) => a.id !== alias.id);
      } else {
        // Add the alias
        return [...prev, alias];
      }
    });
  }, []);

  // Check if an alias is selected
  const isAliasSelected = useCallback((aliasId: string) => {
    return selectedAliases.some((a: Alias) => a.id === aliasId);
  }, [selectedAliases]);

  // Get all selected alias IDs as a comma-separated string
  const getSelectedAliasIds = useCallback(() => {
    return selectedAliases.map((a: Alias) => a.id);
  }, [selectedAliases]);

  // Function to set the provider filter
  const handleSetProviderFilter = useCallback((filter: ProviderType) => {
    setProviderFilter(filter);
  }, []);

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    aliases,
    selectedAliases,
    toggleAlias,
    isAliasSelected,
    getSelectedAliasIds,
    isLoading,
    error,
    providerFilter,
    setProviderFilter: handleSetProviderFilter,
    filteredAliases
  }), [
    aliases,
    selectedAliases,
    toggleAlias,
    isAliasSelected,
    getSelectedAliasIds,
    isLoading,
    error,
    providerFilter,
    handleSetProviderFilter,
    filteredAliases
  ]);

  return (
    <ChatSettingsContext.Provider value={contextValue}>
      {children}
    </ChatSettingsContext.Provider>
  );
}

export function useChatSettings() {
  const context = useContext(ChatSettingsContext);
  if (context === undefined) {
    throw new Error('useChatSettings must be used within a ChatSettingsProvider');
  }
  return context;
}
