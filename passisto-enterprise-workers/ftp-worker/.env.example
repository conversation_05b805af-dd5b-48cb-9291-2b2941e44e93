# PostgreSQL Database Configuration (for PGVector)
DB_HOST=localhost
DB_PORT=5432
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=your_db_name

# Azure OpenAI Configuration (for embeddings)
AZURE_OPENAI_API_KEY=your_azure_openai_api_key
AZURE_OPENAI_ENDPOINT=https://your-instance.openai.azure.com/
AZURE_OPENAI_API_VERSION=2024-02-01
EMBEDDING_MODEL=text-embedding-3-small
EMBEDDING_DIMENSION=1024
AZUR_OPENAI_EMBEDDING_DEPLOYMENT_NAME=your_embedding_deployment_name

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Document Processing Configuration
MAX_BULK_SIZE=1000
BATCH_SIZE=100

# Legacy OpenSearch Configuration (no longer used but kept for compatibility)
# OPENSEARCH_URL=https://localhost:9200
# OPENSEARCH_USERNAME=admin
# OPENSEARCH_PASSWORD=admin
# OPENSEARCH_USE_SSL=true
# OPENSEARCH_VERIFY_CERT=false
# OPENSEARCH_NUM_SHARDS=1
# OPENSEARCH_NUM_REPLICAS=0
