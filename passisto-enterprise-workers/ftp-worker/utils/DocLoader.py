import os
import warnings
from typing import List
from .setLogger import logger
from .pgvector_service import pg_vector_service
from langchain_core.documents import Document

MAX_BULK_SIZE = int(os.getenv("MAX_BULK_SIZE", "1000"))
BATCH_SIZE = int(os.getenv("BATCH_SIZE", "100"))

warnings.filterwarnings('ignore')


class Load2Database:
    """
    Database loader class that uses PGVector for document storage
    Maintains the same interface as the original OpenSearch implementation
    """

    def __init__(self, collection_name: str):
        """
        Initialize the database loader with a collection name

        Args:
            collection_name: Name of the collection (replaces opensearch_index)
        """
        self.collection_name = collection_name
        self._initialize_service()

    def _initialize_service(self):
        """Initialize the PGVector service"""
        logger.log.info('Initializing PGVector service...')
        try:
            # Initialize the pgvector service
            pg_vector_service._do_initialize()
            logger.log.info('Successfully initialized PGVector service')
        except Exception as e:
            logger.log.error(f'Failed to initialize PGVector service: {e}')
            raise


    def load_document(self, docs: List[Document]):
        """
        Load documents into the PGVector collection

        Args:
            docs: List of LangChain Document objects
        """
        if not docs:
            logger.log.warning('No documents to load')
            return

        try:
            if len(docs) > MAX_BULK_SIZE:
                logger.log.debug('Too many documents, batching...')
                batched_docs = self._batch_documents(docs)
                for i, batch in enumerate(batched_docs):
                    self._add_documents(batch, i)
            else:
                self._add_documents(docs)
        except Exception as e:
            logger.log.error(f'Failed to load documents: {e}')
            raise

    def _batch_documents(self, docs: List[Document]) -> List[List[Document]]:
        """Split documents into batches"""
        return [docs[i:i + BATCH_SIZE] for i in range(0, len(docs), BATCH_SIZE)]

    def _add_documents(self, docs: List[Document], batch_index: int = None):
        """Add documents to the individual table"""
        try:
            pg_vector_service.add_documents(self.collection_name, docs)
            if batch_index is not None:
                logger.log.debug(f'Batch {batch_index} was loaded successfully')
            else:
                logger.log.info('Documents were loaded successfully')
        except Exception as e:
            logger.log.error(f'Failed to add documents to table {self.collection_name}: {e}')
            raise

    def truncate_source_documents(self, source: str):
        """
        Delete documents from a specific source
        This method maintains compatibility with the original interface

        Args:
            source: The source URL/path to delete documents for
        """
        try:
            # For now, we'll implement a simple approach
            # In a production environment, you might want to implement
            # more sophisticated filtering based on metadata
            logger.log.info(f'Truncating documents from source: {source}')

            # Note: PGVector doesn't have a direct equivalent to OpenSearch's delete_by_query
            # This would require implementing custom logic to find and delete documents
            # by source metadata. For now, we'll log this operation.
            logger.log.warning('Source document truncation not yet implemented for PGVector')

        except Exception as e:
            logger.log.error(f'Failed to truncate source documents: {e}')
            # Don't raise here to maintain compatibility with existing code
            pass
