import os
import warnings
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv
from langchain_postgres import PGVector
from langchain_openai import AzureOpenAIEmbeddings
from langchain_core.documents import Document
import psycopg
from .setLogger import logger

# Load environment variables
load_dotenv()

# Suppress warnings
warnings.filterwarnings('ignore')

class PgVectorService:
    """
    PGVectorStore service using LangChain implementation for Python
    This follows the same patterns as the JavaScript pgvector.js service
    """
    
    def __init__(self):
        self.embeddings = None
        self.connection_string = None
        self.initialization_promise = None
        self._initialized = False
        
    def initialize(self):
        """Initialize the service with embeddings and database connection"""
        if self._initialized:
            return

        return self._do_initialize()

    def _do_initialize(self):
        """Internal initialization method"""
        if self._initialized:
            return
            
        try:
            # Initialize Azure OpenAI embeddings
            self.embeddings = AzureOpenAIEmbeddings(
                model=os.getenv("EMBEDDING_MODEL", "text-embedding-3-small"),
                dimensions=int(os.getenv("EMBEDDING_DIMENSION", "1024")),
                azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
                api_key=os.getenv("AZURE_OPENAI_API_KEY"),
                openai_api_version=os.getenv("AZURE_OPENAI_API_VERSION"),
                azure_deployment=os.getenv("AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME")
            )

            # Build connection string - use the correct format for psycopg3
            self.connection_string = (
                f"postgresql://{os.getenv('DB_USER')}:"
                f"{os.getenv('DB_PASSWORD')}@{os.getenv('DB_HOST')}:"
                f"{os.getenv('DB_PORT', '5432')}/{os.getenv('DB_NAME')}"
            )
            
            self._initialized = True
            logger.log.info("PGVectorService initialized successfully")
            
        except Exception as e:
            logger.log.error(f"Failed to initialize PGVectorService: {e}")
            raise
    
    def create_vector_store(self, collection_name: str, table_name: str = "langchain_pg_embedding") -> PGVector:
        """
        Create a PGVectorStore instance for a specific collection
        
        Args:
            collection_name: Collection name (replaces indexName)
            table_name: Table name (default: "langchain_pg_embedding")
            
        Returns:
            PGVector instance
        """
        if not self._initialized:
            self._do_initialize()
            
        try:
            vector_store = PGVector(
                embeddings=self.embeddings,
                collection_name=collection_name,
                connection=self.connection_string,
                use_jsonb=True,
            )
            
            logger.log.info(f"Created PGVectorStore for collection: {collection_name}")
            return vector_store
            
        except Exception as e:
            logger.log.error(f"Failed to create vector store for collection {collection_name}: {e}")
            raise
    
    def get_vector_store(self, collection_name: str, table_name: str = "langchain_pg_embedding") -> PGVector:
        """
        Create or get a PGVectorStore instance without initialization (for reuse)
        
        Args:
            collection_name: Collection name
            table_name: Table name (default: "langchain_pg_embedding")
            
        Returns:
            PGVector instance
        """
        if not self._initialized:
            self._do_initialize()
            
        return PGVector(
            embeddings=self.embeddings,
            collection_name=collection_name,
            connection=self.connection_string,
            use_jsonb=True,
        )
    
    def add_documents(self, collection_name: str, documents: List[Document], options: Dict[str, Any] = None) -> List[str]:
        """
        Add documents to a vector store collection
        
        Args:
            collection_name: Collection name
            documents: List of LangChain Document objects with page_content and metadata
            options: Options including ids array
            
        Returns:
            List of document IDs
        """
        vector_store = self.create_vector_store(collection_name)
        
        try:
            if options and 'ids' in options:
                return vector_store.add_documents(documents, ids=options['ids'])
            else:
                return vector_store.add_documents(documents)
        except Exception as e:
            logger.log.error(f"Failed to add documents to collection {collection_name}: {e}")
            raise
    
    def similarity_search(self, collection_name: str, query: str, k: int = 4, filter_dict: Dict[str, Any] = None) -> List[Document]:
        """
        Search for similar documents
        
        Args:
            collection_name: Collection name
            query: Search query
            k: Number of results (default: 4)
            filter_dict: Optional metadata filter
            
        Returns:
            List of similar documents
        """
        vector_store = self.get_vector_store(collection_name)
        
        try:
            return vector_store.similarity_search(query, k=k, filter=filter_dict or {})
        except Exception as e:
            logger.log.error(f"Failed to perform similarity search in collection {collection_name}: {e}")
            raise
    
    def similarity_search_with_score(self, collection_name: str, query: str, k: int = 4, filter_dict: Dict[str, Any] = None) -> List[tuple]:
        """
        Search for similar documents with scores
        
        Args:
            collection_name: Collection name
            query: Search query
            k: Number of results (default: 4)
            filter_dict: Optional metadata filter
            
        Returns:
            List of (document, score) tuples
        """
        vector_store = self.get_vector_store(collection_name)
        
        try:
            return vector_store.similarity_search_with_score(query, k=k, filter=filter_dict or {})
        except Exception as e:
            logger.log.error(f"Failed to perform similarity search with score in collection {collection_name}: {e}")
            raise
    
    def delete_documents(self, collection_name: str, options: Dict[str, Any] = None):
        """
        Delete documents from a collection
        
        Args:
            collection_name: Collection name
            options: Delete options (e.g., {'ids': [...]})
        """
        vector_store = self.get_vector_store(collection_name)
        
        try:
            if options and 'ids' in options:
                vector_store.delete(ids=options['ids'])
            else:
                # Delete all documents if no specific IDs provided
                logger.log.warning(f"Deleting all documents from collection {collection_name}")
                vector_store.delete()
        except Exception as e:
            logger.log.error(f"Failed to delete documents from collection {collection_name}: {e}")
            raise
    
    def create_retriever(self, collection_name: str, config: Dict[str, Any] = None):
        """
        Create a retriever for a collection
        
        Args:
            collection_name: Collection name
            config: Retriever configuration
            
        Returns:
            LangChain retriever
        """
        vector_store = self.get_vector_store(collection_name)
        
        try:
            return vector_store.as_retriever(**(config or {}))
        except Exception as e:
            logger.log.error(f"Failed to create retriever for collection {collection_name}: {e}")
            raise
    
    def get_collection_info(self, collection_name: str) -> Optional[Dict[str, Any]]:
        """
        Get collection information
        
        Args:
            collection_name: Collection name
            
        Returns:
            Collection info dictionary or None if not found
        """
        if not self._initialized:
            self._do_initialize()
            
        try:
            with psycopg.connect(self.connection_string) as conn:
                with conn.cursor() as cur:
                    # Query the collection table to get collection info
                    cur.execute(
                        "SELECT name, cmetadata, uuid FROM langchain_pg_collection WHERE name = %s",
                        (collection_name,)
                    )
                    result = cur.fetchone()
                    
                    if not result:
                        return None
                    
                    name, metadata, uuid = result
                    
                    # Get document count
                    cur.execute(
                        "SELECT COUNT(*) FROM langchain_pg_embedding WHERE collection_id = %s",
                        (uuid,)
                    )
                    count_result = cur.fetchone()
                    
                    return {
                        'name': name,
                        'metadata': metadata,
                        'documentCount': count_result[0] if count_result else 0,
                        'id': str(uuid)
                    }
                    
        except Exception as e:
            logger.log.error(f"Failed to get collection info for {collection_name}: {e}")
            return None
    
    def list_collections(self) -> List[str]:
        """
        List all collections
        
        Returns:
            List of collection names
        """
        if not self._initialized:
            self._do_initialize()
            
        try:
            with psycopg.connect(self.connection_string) as conn:
                with conn.cursor() as cur:
                    cur.execute("SELECT name FROM langchain_pg_collection ORDER BY name")
                    results = cur.fetchall()
                    return [row[0] for row in results]
                    
        except Exception as e:
            logger.log.error(f"Failed to list collections: {e}")
            return []
    
    def delete_collection(self, collection_name: str) -> bool:
        """
        Delete a collection and all its documents
        
        Args:
            collection_name: Collection name
            
        Returns:
            Success status
        """
        try:
            vector_store = self.get_vector_store(collection_name)
            vector_store.delete()
            logger.log.info(f"Deleted collection: {collection_name}")
            return True
            
        except Exception as e:
            logger.log.error(f"Error deleting collection {collection_name}: {e}")
            return False


# Export singleton instance
pg_vector_service = PgVectorService()
