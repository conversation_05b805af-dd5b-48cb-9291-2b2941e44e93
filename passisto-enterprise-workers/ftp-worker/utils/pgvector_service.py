import os
import warnings
import psycopg2
import uuid
import json
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv
from langchain_openai import AzureOpenAIEmbeddings
from langchain_core.documents import Document
from .setLogger import logger

# Load environment variables
load_dotenv()

# Suppress warnings
warnings.filterwarnings('ignore')

# Database configuration
DB_HOST = os.getenv("DB_HOST")
DB_PORT = os.getenv("DB_PORT", "5432")
DB_USER = os.getenv("DB_USER")
DB_PASSWORD = os.getenv("DB_PASSWORD")
DB_NAME = os.getenv("DB_NAME")
EMBEDDING_DIMENSION = int(os.getenv("EMBEDDING_DIMENSION", "1024"))

class PgVectorService:
    """
    PGVectorStore service that creates individual tables per collection
    This follows the same pattern as the web worker implementation
    """

    def __init__(self):
        self.embeddings = None
        self._conn = None
        self._initialized = False

    def _do_initialize(self):
        """Internal initialization method"""
        if self._initialized:
            return

        try:
            # Initialize Azure OpenAI embeddings
            self.embeddings = AzureOpenAIEmbeddings(
                model=os.getenv("EMBEDDING_MODEL", "text-embedding-3-small"),
                dimensions=int(os.getenv("EMBEDDING_DIMENSION", "1024")),
                azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
                api_key=os.getenv("AZURE_OPENAI_API_KEY"),
                openai_api_version=os.getenv("AZURE_OPENAI_API_VERSION"),
                azure_deployment=os.getenv("AZUR_OPENAI_EMBEDDING_DEPLOYMENT_NAME")
            )

            # Build connection string for psycopg2
            conn_str = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
            self._conn = psycopg2.connect(conn_str)
            self._conn.autocommit = True

            self._initialized = True
            logger.log.info("PGVectorService initialized successfully")

        except Exception as e:
            logger.log.error(f"Failed to initialize PGVectorService: {e}")
            raise

    def create_individual_table(self, table_name: str):
        """Create an individual table for this collection"""
        if not self._initialized:
            self._do_initialize()

        try:
            with self._conn.cursor() as cur:
                # Enable vector extension
                cur.execute("CREATE EXTENSION IF NOT EXISTS vector;")

                # Create the individual table with the existing schema
                safe_table_name = f'"{table_name}"'
                create_table_sql = f"""
                CREATE TABLE IF NOT EXISTS {safe_table_name} (
                    langchain_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    content TEXT NOT NULL,
                    embedding vector({EMBEDDING_DIMENSION}) NOT NULL,
                    langchain_metadata JSON
                );
                """
                cur.execute(create_table_sql)

                # Create index for better performance
                index_name = f"idx_{table_name.replace('-', '_')}_embedding"[:63]
                cur.execute(f"""
                CREATE INDEX IF NOT EXISTS "{index_name}"
                ON {safe_table_name} USING hnsw (embedding vector_cosine_ops);
                """)

                logger.log.info(f"Created/verified individual table: {table_name}")
        except Exception as e:
            logger.log.error(f"Error creating individual table: {e}")
            raise

    def table_exists(self, table_name: str) -> bool:
        """Check if a table exists"""
        if not self._initialized:
            self._do_initialize()

        try:
            with self._conn.cursor() as cur:
                cur.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_schema = 'public'
                        AND table_name = %s
                    );
                """, (table_name,))
                return cur.fetchone()[0]
        except Exception as e:
            logger.log.error(f"Error checking if table exists: {e}")
            return False

    def add_documents(self, table_name: str, documents: List[Document]) -> List[str]:
        """Add documents to the individual table"""
        if not self._initialized:
            self._do_initialize()

        # Ensure table exists
        self.create_individual_table(table_name)

        try:
            # Generate embeddings for all documents
            texts = [doc.page_content for doc in documents]
            embeddings = self.embeddings.embed_documents(texts)

            # Insert documents into the table
            doc_ids = []
            with self._conn.cursor() as cur:
                safe_table_name = f'"{table_name}"'
                for doc, embedding in zip(documents, embeddings):
                    doc_id = str(uuid.uuid4())

                    # Convert embedding to the format expected by pgvector
                    embedding_str = '[' + ','.join(map(str, embedding)) + ']'

                    cur.execute(f"""
                        INSERT INTO {safe_table_name}
                        (langchain_id, content, embedding, langchain_metadata)
                        VALUES (%s, %s, %s, %s)
                    """, (
                        doc_id,
                        doc.page_content,
                        embedding_str,
                        json.dumps(doc.metadata) if doc.metadata else '{}'
                    ))
                    doc_ids.append(doc_id)

            logger.log.info(f"Added {len(documents)} documents to table {table_name}")
            return doc_ids

        except Exception as e:
            logger.log.error(f"Failed to add documents to table {table_name}: {e}")
            raise

    def similarity_search(self, table_name: str, query: str, k: int = 4) -> List[Document]:
        """Search for similar documents in the table"""
        if not self._initialized:
            self._do_initialize()

        try:
            # Generate embedding for the query
            query_embedding = self.embeddings.embed_query(query)
            embedding_str = '[' + ','.join(map(str, query_embedding)) + ']'

            with self._conn.cursor() as cur:
                safe_table_name = f'"{table_name}"'
                cur.execute(f"""
                    SELECT content, langchain_metadata,
                           embedding <=> %s as distance
                    FROM {safe_table_name}
                    ORDER BY distance
                    LIMIT %s
                """, (embedding_str, k))

                results = cur.fetchall()

                documents = []
                for content, metadata, distance in results:
                    # Parse metadata if it's a string
                    if isinstance(metadata, str):
                        try:
                            metadata = json.loads(metadata)
                        except:
                            metadata = {}

                    doc = Document(
                        page_content=content,
                        metadata=metadata or {}
                    )
                    documents.append(doc)

                return documents

        except Exception as e:
            logger.log.error(f"Failed to perform similarity search in table {table_name}: {e}")
            raise


# Export singleton instance
pg_vector_service = PgVectorService()