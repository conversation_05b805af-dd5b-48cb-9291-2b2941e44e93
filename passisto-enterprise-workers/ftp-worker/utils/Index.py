from .setLogger import logger
from .pgvector_service import pg_vector_service
from os import getenv
from typing import Optional

EMBEDDING_DIMENSION = int(getenv("EMBEDDING_DIMENSION", "1024"))

class Index():
    """
    Index management class for PGVector collections
    Maintains compatibility with the original OpenSearch interface
    """

    def __init__(self) -> None:
        """Initialize the Index manager with PGVector service"""
        self.pg_service = pg_vector_service

    def create_index(self, collection_name: str):
        """
        Create a collection in PGVector (equivalent to creating an index in OpenSearch)

        Args:
            collection_name: Name of the collection to create
        """
        try:
            # Initialize the service if not already done
            if not self.pg_service._initialized:
                self.pg_service._do_initialize()

            # Create a vector store for the collection
            # This will automatically create the necessary tables if they don't exist
            self.pg_service.create_vector_store(collection_name)

            logger.log.info(f'Collection {collection_name} created/verified successfully')

        except Exception as e:
            logger.log.error(f'Failed to create collection {collection_name}: {e}')
            raise

    def truncate_source_documents(self, source: str, collection_name: str):
        """
        Delete documents from a specific source within a collection

        Args:
            source: The source URL/path to delete documents for
            collection_name: The target collection name
        """
        try:
            logger.log.info(f'Truncating documents from source: {source} in collection: {collection_name}')

            # Note: PGVector doesn't have a direct equivalent to OpenSearch's delete_by_query
            # For now, we'll implement a placeholder that logs the operation
            # In a production environment, you would need to implement custom logic
            # to find and delete documents by source metadata

            # This could be implemented by:
            # 1. Querying documents with the specific source metadata
            # 2. Extracting their IDs
            # 3. Deleting them using the delete_documents method

            logger.log.warning(f'Source document truncation not fully implemented for PGVector. Source: {source}')
            logger.log.debug("Related docs truncation logged")

        except Exception as e:
            logger.log.error(f"Failed to truncate source documents: {e}")
            # Don't raise to maintain compatibility with existing code
            pass

    def collection_exists(self, collection_name: str) -> bool:
        """
        Check if a collection exists

        Args:
            collection_name: Name of the collection to check

        Returns:
            True if collection exists, False otherwise
        """
        try:
            collections = self.pg_service.list_collections()
            return collection_name in collections
        except Exception as e:
            logger.log.error(f'Failed to check if collection {collection_name} exists: {e}')
            return False

    def get_collection_info(self, collection_name: str) -> Optional[dict]:
        """
        Get information about a collection

        Args:
            collection_name: Name of the collection

        Returns:
            Collection information dictionary or None
        """
        try:
            return self.pg_service.get_collection_info(collection_name)
        except Exception as e:
            logger.log.error(f'Failed to get collection info for {collection_name}: {e}')
            return None

    def list_collections(self) -> list:
        """
        List all available collections

        Returns:
            List of collection names
        """
        try:
            return self.pg_service.list_collections()
        except Exception as e:
            logger.log.error(f'Failed to list collections: {e}')
            return []