from .setLogger import logger
from .pgvector_service import pg_vector_service
from os import getenv
from typing import Optional

EMBEDDING_DIMENSION = int(getenv("EMBEDDING_DIMENSION", "1024"))

class Index():
    """
    Index management class for PGVector collections
    Maintains compatibility with the original OpenSearch interface
    """

    def __init__(self) -> None:
        """Initialize the Index manager with PGVector service"""
        self.pg_service = pg_vector_service

    def create_index(self, table_name: str):
        """
        Create an individual table for this collection

        Args:
            table_name: Name of the table to create
        """
        try:
            # Initialize the service if not already done
            if not self.pg_service._initialized:
                self.pg_service._do_initialize()

            # Create the individual table
            self.pg_service.create_individual_table(table_name)

            logger.log.info(f'Table {table_name} created/verified successfully')

        except Exception as e:
            logger.log.error(f'Failed to create table {table_name}: {e}')
            raise

    def truncate_source_documents(self, source: str, table_name: str):
        """
        Delete documents from a specific source within a table

        Args:
            source: The source URL/path to delete documents for
            table_name: The target table name
        """
        try:
            logger.log.info(f'Truncating documents from source: {source} in table: {table_name}')

            # Note: For individual tables, we could implement source-based deletion
            # For now, we'll implement a placeholder that logs the operation
            # In a production environment, you would need to implement custom logic
            # to find and delete documents by source metadata

            # This could be implemented by:
            # 1. Querying documents with the specific source metadata
            # 2. Extracting their IDs
            # 3. Deleting them using the delete_documents method

            logger.log.warning(f'Source document truncation not fully implemented for individual tables. Source: {source}')
            logger.log.debug("Related docs truncation logged")

        except Exception as e:
            logger.log.error(f"Failed to truncate source documents: {e}")
            # Don't raise to maintain compatibility with existing code
            pass

    def table_exists(self, table_name: str) -> bool:
        """
        Check if a table exists

        Args:
            table_name: Name of the table to check

        Returns:
            True if table exists, False otherwise
        """
        try:
            return self.pg_service.table_exists(table_name)
        except Exception as e:
            logger.log.error(f'Failed to check if table {table_name} exists: {e}')
            return False

    def get_table_info(self, table_name: str) -> Optional[dict]:
        """
        Get information about a table

        Args:
            table_name: Name of the table

        Returns:
            Table information dictionary or None
        """
        try:
            # For individual tables, we don't have a get_table_info method yet
            # Return basic info if table exists
            if self.pg_service.table_exists(table_name):
                return {
                    'name': table_name,
                    'documentCount': 0,  # Would need to implement count query
                }
            return None
        except Exception as e:
            logger.log.error(f'Failed to get table info for {table_name}: {e}')
            return None

    def list_tables(self) -> list:
        """
        List all available tables

        Returns:
            List of table names
        """
        try:
            # For individual tables, we would need to implement a list method
            # For now, return empty list
            logger.log.warning('List tables not implemented for individual table approach')
            return []
        except Exception as e:
            logger.log.error(f'Failed to list tables: {e}')
            return []