from celery import Celery
from uni import FTP<PERSON>lient, SFTPClient, FileTypes 
import os
from utils.decrypt import decrypt


brocker_uri = os.getenv('CELERY_BROKER_URL')
backend_uri = os.getenv('CELERY_RESULT_BACKEND')
db_uri = os.getenv('DATABASE_URL')

celeryapp = Celery('ftp_load', broker=brocker_uri, backend=backend_uri)
celeryapp.config_from_object('celeryconfig')


@celeryapp.task(name='celery.ftp_loader')
def ftp_load(is_secure, host, port, username, encrypted_password,
             collection_name):

    password = decrypt(encrypted_password)
    file_types_enum = FileTypes(FileTypes.ALL)
    max_data_size = 10

    if not is_secure:
        remote_ftp = FTPClient(
            host=host,
            port=int(port),
            username=username,
            password=password,
            file_types=file_types_enum,
            max_size=max_data_size,
            collection_name=collection_name
        )
        remote_ftp.traverse_directory()
        return "Hello from FTP"

    else:
        remote_ftp = SFTPClient(
            host=host,
            port=int(port),
            username=username,
            password=password,
            file_types=file_types_enum,
            max_size=max_data_size,
            collection_name=collection_name
        )
        remote_ftp.traverse_directory()
        return "Hello from SFTP"