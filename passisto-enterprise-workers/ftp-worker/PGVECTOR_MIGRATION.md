# FTP Worker PGVector Migration

This document describes the migration of the FTP worker from OpenSearch to PGVector for vector storage.

## Overview

The FTP worker has been updated to use PostgreSQL with the pgvector extension instead of OpenSearch for storing and retrieving document embeddings. This change aligns with the backend-v2 implementation and provides better integration with the existing PostgreSQL infrastructure.

## Changes Made

### 1. Dependencies Updated

- Added `langchain-postgres` for PGVector integration
- Added `psycopg[binary]` for PostgreSQL connectivity
- Removed dependency on OpenSearch-specific packages

### 2. New Components

#### `utils/pgvector_service.py`
- Python equivalent of the JavaScript `pgvector.js` service
- Provides methods for:
  - Creating and managing vector stores
  - Adding and searching documents
  - Managing collections
  - Similarity search with scoring

#### Updated Components

#### `utils/DocLoader.py`
- Modified to use PGVector instead of OpenSearch
- Maintains the same interface for backward compatibility
- Uses collection names instead of index names

#### `utils/Index.py`
- Updated to work with PostgreSQL collections
- Provides collection management functionality
- Maintains compatibility with existing code

#### `uni.py` and `tasks.py`
- Updated to use `collection_name` parameter instead of `opensearch_index`
- Modified FTP and SFTP client constructors

## Configuration

### Environment Variables

The following environment variables are required:

```bash
# PostgreSQL Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=your_db_name

# Azure OpenAI Configuration
AZURE_OPENAI_API_KEY=your_azure_openai_api_key
AZURE_OPENAI_ENDPOINT=https://your-instance.openai.azure.com/
AZURE_OPENAI_API_VERSION=2024-02-01
EMBEDDING_MODEL=text-embedding-3-small
EMBEDDING_DIMENSION=1024
AZUR_OPENAI_EMBEDDING_DEPLOYMENT_NAME=your_embedding_deployment_name

# Document Processing
MAX_BULK_SIZE=1000
BATCH_SIZE=100
```

### Database Setup

Ensure your PostgreSQL database has the pgvector extension installed:

```sql
CREATE EXTENSION IF NOT EXISTS vector;
```

## Testing

Run the integration test to verify the setup:

```bash
python test_pgvector_integration.py
```

This test will:
1. Verify PGVector service initialization
2. Test collection creation
3. Test document addition and retrieval
4. Validate similarity search functionality

## Migration Notes

### Backward Compatibility

- The API interface remains largely the same
- Collection names replace OpenSearch index names
- Document metadata structure is preserved

### Key Differences

1. **Storage Backend**: PostgreSQL with pgvector instead of OpenSearch
2. **Collection Management**: Uses PostgreSQL tables instead of OpenSearch indices
3. **Search Capabilities**: LangChain's PGVector implementation for similarity search

### Limitations

1. **Source Document Truncation**: The `truncate_source_documents` functionality is not fully implemented yet. This would require custom logic to find and delete documents by source metadata.

2. **Advanced Search Features**: Some OpenSearch-specific features may not have direct equivalents in PGVector.

## Usage

The FTP worker can now be called with a collection name instead of an OpenSearch index:

```python
# Celery task call
ftp_load(
    is_secure=False,
    host="ftp.example.com",
    port=21,
    username="user",
    encrypted_password="encrypted_pass",
    collection_name="my_ftp_collection"  # Instead of opensearch_index
)
```

## Benefits

1. **Unified Infrastructure**: Uses the same PostgreSQL database as the main application
2. **Better Integration**: Aligns with the backend-v2 pgvector implementation
3. **Simplified Deployment**: Reduces the number of external services required
4. **Cost Efficiency**: Eliminates the need for a separate OpenSearch cluster

## Future Improvements

1. Implement full source document truncation functionality
2. Add support for advanced filtering and search capabilities
3. Optimize batch processing for large document sets
4. Add monitoring and metrics for vector operations
