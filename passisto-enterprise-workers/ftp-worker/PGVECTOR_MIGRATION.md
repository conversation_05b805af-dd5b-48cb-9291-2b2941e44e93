# FTP Worker PGVector Migration

This document describes the migration of the FTP worker from OpenSearch to PGVector for vector storage.

## Overview

The FTP worker has been updated to use PostgreSQL with the pgvector extension instead of OpenSearch for storing and retrieving document embeddings. This implementation creates individual tables per collection, following the same pattern as the web worker, rather than using the standard Lang<PERSON>hain approach with shared tables.

## Changes Made

### 1. Dependencies Updated

- Added `psycopg2-binary` for PostgreSQL connectivity
- Removed dependency on OpenSearch-specific packages
- Note: Uses direct psycopg2 instead of LangChain's PGVector for individual table approach

### 2. New Components

#### `utils/pgvector_service.py`
- Custom implementation that creates individual tables per collection
- Provides methods for:
  - Creating individual tables with pgvector schema
  - Adding and searching documents in specific tables
  - Managing table existence and structure
  - Similarity search with cosine distance

#### Updated Components

#### `utils/DocLoader.py`
- Modified to use individual table approach instead of OpenSearch
- Maintains the same interface for backward compatibility
- Uses collection names that map to individual table names

#### `utils/Index.py`
- Updated to work with individual PostgreSQL tables
- Provides table management functionality
- Maintains compatibility with existing code

#### `uni.py` and `tasks.py`
- Updated to use `collection_name` parameter instead of `opensearch_index`
- Modified FTP and SFTP client constructors

## Configuration

### Environment Variables

The following environment variables are required:

```bash
# PostgreSQL Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=your_db_name

# Azure OpenAI Configuration
AZURE_OPENAI_API_KEY=your_azure_openai_api_key
AZURE_OPENAI_ENDPOINT=https://your-instance.openai.azure.com/
AZURE_OPENAI_API_VERSION=2024-02-01
EMBEDDING_MODEL=text-embedding-3-small
EMBEDDING_DIMENSION=1024
AZUR_OPENAI_EMBEDDING_DEPLOYMENT_NAME=your_embedding_deployment_name

# Document Processing
MAX_BULK_SIZE=1000
BATCH_SIZE=100
```

### Database Setup

Ensure your PostgreSQL database has the pgvector extension installed:

```sql
CREATE EXTENSION IF NOT EXISTS vector;
```

## Testing

Run the integration test to verify the setup:

```bash
python test_pgvector_integration.py
```

This test will:
1. Verify PGVector service initialization
2. Test collection creation
3. Test document addition and retrieval
4. Validate similarity search functionality

## Migration Notes

### Backward Compatibility

- The API interface remains largely the same
- Collection names replace OpenSearch index names
- Document metadata structure is preserved

### Key Differences

1. **Storage Backend**: PostgreSQL with pgvector instead of OpenSearch
2. **Collection Management**: Uses PostgreSQL tables instead of OpenSearch indices
3. **Search Capabilities**: LangChain's PGVector implementation for similarity search

### Limitations

1. **Source Document Truncation**: The `truncate_source_documents` functionality is not fully implemented yet. This would require custom logic to find and delete documents by source metadata.

2. **Advanced Search Features**: Some OpenSearch-specific features may not have direct equivalents in PGVector.

## Usage

The FTP worker can now be called with a collection name instead of an OpenSearch index:

```python
# Celery task call
ftp_load(
    is_secure=False,
    host="ftp.example.com",
    port=21,
    username="user",
    encrypted_password="encrypted_pass",
    collection_name="my_ftp_collection"  # Instead of opensearch_index
)
```

## Benefits

1. **Unified Infrastructure**: Uses the same PostgreSQL database as the main application
2. **Better Integration**: Aligns with the backend-v2 pgvector implementation
3. **Simplified Deployment**: Reduces the number of external services required
4. **Cost Efficiency**: Eliminates the need for a separate OpenSearch cluster

## Future Improvements

1. Implement full source document truncation functionality
2. Add support for advanced filtering and search capabilities
3. Optimize batch processing for large document sets
4. Add monitoring and metrics for vector operations
