#!/usr/bin/env python3
"""
Test script for PGVector integration in FTP worker
This script validates the basic functionality of the pgvector service
"""

import os
import sys
from dotenv import load_dotenv
from langchain_core.documents import Document

# Add the current directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

def test_pgvector_service():
    """Test the basic functionality of the PGVector service"""
    print("Testing PGVector Service...")

    try:
        from utils.pgvector_service import pg_vector_service

        # Test initialization
        print("1. Testing service initialization...")
        pg_vector_service._do_initialize()
        print("✓ Service initialized successfully")

        # Test table creation
        print("2. Testing table creation...")
        test_table = "test_ftp_table"
        pg_vector_service.create_individual_table(test_table)
        print(f"✓ Table '{test_table}' created successfully")

        # Test document addition
        print("3. Testing document addition...")
        test_docs = [
            Document(
                page_content="This is a test document for FTP worker integration",
                metadata={"source": "test_file.txt", "type": "test"}
            ),
            Document(
                page_content="Another test document to verify pgvector functionality",
                metadata={"source": "test_file2.txt", "type": "test"}
            )
        ]

        doc_ids = pg_vector_service.add_documents(test_table, test_docs)
        print(f"✓ Added {len(doc_ids)} documents successfully")

        # Test similarity search
        print("4. Testing similarity search...")
        results = pg_vector_service.similarity_search(test_table, "test document", k=2)
        print(f"✓ Found {len(results)} similar documents")

        # Test table existence
        print("5. Testing table existence...")
        exists = pg_vector_service.table_exists(test_table)
        print(f"✓ Table exists: {exists}")

        print("\n✅ All PGVector service tests passed!")
        return True

    except Exception as e:
        print(f"❌ PGVector service test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_docloader():
    """Test the DocLoader functionality"""
    print("\nTesting DocLoader...")

    try:
        from utils.DocLoader import Load2Database

        # Test DocLoader initialization
        print("1. Testing DocLoader initialization...")
        test_table = "test_docloader_table"
        loader = Load2Database(test_table)
        print("✓ DocLoader initialized successfully")

        # Test document loading
        print("2. Testing document loading...")
        test_docs = [
            Document(
                page_content="Test document for DocLoader",
                metadata={"source": "docloader_test.txt", "title": "Test Document"}
            )
        ]

        loader.load_document(test_docs)
        print("✓ Documents loaded successfully")

        print("\n✅ DocLoader tests passed!")
        return True

    except Exception as e:
        print(f"❌ DocLoader test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_index_manager():
    """Test the Index manager functionality"""
    print("\nTesting Index Manager...")

    try:
        from utils.Index import Index

        # Test Index initialization
        print("1. Testing Index initialization...")
        index_manager = Index()
        print("✓ Index manager initialized successfully")

        # Test table creation
        print("2. Testing table creation...")
        test_table = "test_index_table"
        index_manager.create_index(test_table)
        print(f"✓ Table '{test_table}' created/verified successfully")

        # Test table existence check
        print("3. Testing table existence check...")
        exists = index_manager.table_exists(test_table)
        print(f"✓ Table exists: {exists}")

        print("\n✅ Index manager tests passed!")
        return True

    except Exception as e:
        print(f"❌ Index manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_environment():
    """Check if required environment variables are set"""
    print("Checking environment variables...")
    
    required_vars = [
        'DB_HOST', 'DB_PORT', 'DB_USER', 'DB_PASSWORD', 'DB_NAME',
        'AZURE_OPENAI_API_KEY', 'AZURE_OPENAI_ENDPOINT', 'AZURE_OPENAI_API_VERSION',
        'EMBEDDING_MODEL', 'EMBEDDING_DIMENSION'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing environment variables: {missing_vars}")
        return False
    
    print("✓ All required environment variables are set")
    return True

def main():
    """Main test function"""
    print("🧪 FTP Worker PGVector Integration Test")
    print("=" * 50)
    
    # Check environment
    if not check_environment():
        print("\n❌ Environment check failed. Please set required environment variables.")
        return False
    
    # Run tests
    tests = [
        test_pgvector_service,
        test_docloader,
        test_index_manager
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! PGVector integration is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
