from .pgvector_client import get_pgvector_client
from dotenv import load_dotenv
from os import getenv
from .setLogger import logger
import os
from langchain_openai import AzureOpenAIEmbeddings


load_dotenv()
EMBEDDING_MODEL = getenv('EMBEDDING_MODEL')
BATCH_SIZE = int(getenv('BATCH_SIZE', 30))
MAX_BULK_SIZE = int(getenv('MAX_BULK_SIZE', 50))

AZURE_OPENAI_API_KEY = os.getenv("AZURE_OPENAI_API_KEY")
AZURE_OPENAI_ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT")
AZURE_OPENAI_API_VERSION = os.getenv("AZURE_OPENAI_API_VERSION")
EMBEDDING_MODEL = os.getenv("EMBEDDING_MODEL")
EMBEDDING_DIMENSION = int(os.getenv("EMBEDDING_DIMENSION", 1024))


class Load2Database:

    def __init__(self, table_name):
        logger.log.debug('Initializing pgvector loader...')

        # Embedding model for transforming docs to vectors
        self.embeddings = AzureOpenAIEmbeddings(
            model=EMBEDDING_MODEL,
            dimensions=EMBEDDING_DIMENSION,
            azure_endpoint=AZURE_OPENAI_ENDPOINT,
            api_key=AZURE_OPENAI_API_KEY,
            openai_api_version=AZURE_OPENAI_API_VERSION
        )

        logger.log.debug('Establishing pgvector connection')
        self.pgvector_client = get_pgvector_client()
        self.table_name = table_name

        # Ensure individual table exists
        self.pgvector_client.ensure_individual_table_exists(table_name)
        logger.log.info('Successfully established pgvector connection')

   
    def load_document(self, docs):
        """Load documents into pgvector database"""
        try:
            if len(docs) > MAX_BULK_SIZE:
                logger.log.debug(f'Processing {len(docs)} docs in batches')
                batched_docs = batching_content(docs, BATCH_SIZE)
                
                for i, doc_batch in enumerate(batched_docs):
                    self._load_batch(doc_batch)
                    logger.log.info(f'Batch {i+1}/{len(batched_docs)} loaded successfully')
            else:
                self._load_batch(docs)
                logger.log.info(f'Loaded {len(docs)} documents successfully')
                
        except Exception as e:
            logger.log.error(f'Error loading documents: {e}')
            raise
    
    def _load_batch(self, docs):
        """Load a batch of documents"""
        # Convert langchain documents to pgvector format
        pgvector_docs = []
        
        for doc in docs:
            # Generate embedding for the document
            embedding = self.embeddings.embed_query(doc.page_content)
            
            # Create document structure for individual table
            pgvector_doc = {
                'vector_field': embedding,
                'text': doc.page_content,
                'content': doc.page_content,
                'metadata': doc.metadata
            }
            pgvector_docs.append(pgvector_doc)

        # Index documents in individual table
        self.pgvector_client.index_documents(self.table_name, pgvector_docs)


def batching_content(docs, batch_size=None):
    """
    Batch documents into smaller chunks for processing
    """
    if batch_size is None:
        batch_size = BATCH_SIZE
        
    chunked_list = [docs[i:i + batch_size] for i in range(0, len(docs), batch_size)]
    return chunked_list