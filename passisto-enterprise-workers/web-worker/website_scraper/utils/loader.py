import asyncio
import logging
import os
from dotenv import load_dotenv
from langchain_openai import AzureOpenAIEmbeddings
import psycopg2
from psycopg2.extras import RealDictCursor
import json
import uuid
from .table_utils import generate_safe_table_name, validate_table_name

load_dotenv()
EMBEDDING_MODEL = os.getenv("EMBEDDING_MODEL")
EMBEDDING_DIMENSION = int(os.getenv("EMBEDDING_DIMENSION", "1024"))
BATCH_SIZE = int(os.getenv("BATCH_SIZE", "30"))
MAX_BULK_SIZE = int(os.getenv("MAX_BULK_SIZE", "50"))

# Azure OpenAI configuration
AZURE_OPENAI_API_KEY = os.getenv("AZURE_OPENAI_API_KEY")
AZURE_OPENAI_ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT")
AZURE_OPENAI_API_VERSION = os.getenv("AZURE_OPENAI_API_VERSION")

# Postgres configuration
DATABASE_URL = os.getenv("DATABASE_URL")
DB_HOST = os.getenv("DB_HOST", "pe-db")
DB_PORT = os.getenv("DB_PORT", "5432")
DB_NAME = os.getenv("DB_NAME", "passisto")
DB_USER = os.getenv("DB_USER", "postgres")
DB_PASSWORD = os.getenv("DB_PASSWORD", "")


class Load2Database:

    def __init__(self, table_name: str):
        # Store the table name for the individual collection table
        self.table_name = table_name
        logging.info(f"Initializing Load2Database for table: {table_name}")

        self.embeddings = AzureOpenAIEmbeddings(
            model=EMBEDDING_MODEL,
            dimensions=EMBEDDING_DIMENSION,
            azure_endpoint=AZURE_OPENAI_ENDPOINT,
            api_key=AZURE_OPENAI_API_KEY,
            openai_api_version=AZURE_OPENAI_API_VERSION,
        )
        logging.info(f"Loaded embeddings model {EMBEDDING_MODEL}")

        # Initialize attributes but defer async initialization
        self._conn = None
        self._initialized = False

    def _build_connection_string(self) -> str:
        # Build connection string for psycopg2 (not asyncpg)
        conn_str = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
        logging.info(f"Using connection string: postgresql://{DB_USER}:***@{DB_HOST}:{DB_PORT}/{DB_NAME}")
        return conn_str

    async def _ainit(self):
        if self._initialized:
            return

        try:
            conn_str = self._build_connection_string()
            self._conn = psycopg2.connect(conn_str)
            self._conn.autocommit = True

            # Create the individual table for this collection
            self._create_individual_table()

            self._initialized = True
            logging.info(f"Connected to Postgres vector store for table: {self.table_name}")
        except Exception as e:
            logging.error(f"Failed to initialize Postgres vector store: {e}")
            raise

    def _create_individual_table(self):
        """Create an individual table for this collection (existing approach)"""
        try:
            with self._conn.cursor() as cur:
                # Enable vector extension
                cur.execute("CREATE EXTENSION IF NOT EXISTS vector;")

                # Create the individual table with the existing schema
                safe_table_name = f'"{self.table_name}"'
                create_table_sql = f"""
                CREATE TABLE IF NOT EXISTS {safe_table_name} (
                    langchain_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    content TEXT NOT NULL,
                    embedding vector({EMBEDDING_DIMENSION}) NOT NULL,
                    langchain_metadata JSON
                );
                """
                cur.execute(create_table_sql)

                # Create index for better performance
                index_name = f"idx_{self.table_name.replace('-', '_')}_embedding"[:63]
                cur.execute(f"""
                CREATE INDEX IF NOT EXISTS "{index_name}"
                ON {safe_table_name} USING hnsw (embedding vector_cosine_ops);
                """)

                logging.info(f"Created/verified individual table: {self.table_name}")
        except Exception as e:
            logging.error(f"Error creating individual table: {e}")
            raise

    async def load_document_async(self, docs):
        """Async method for loading documents"""
        try:
            # Ensure initialization
            if not self._initialized:
                await self._ainit()

            if len(docs) > MAX_BULK_SIZE:
                for batch in batching_content(docs):
                    await self._add_documents_batch(batch)
            else:
                await self._add_documents_batch(docs)
            logging.info(f"{len(docs)} documents added to the table {self.index_name}")
        except Exception as e:
            logging.error(f"Error adding documents: {e}")
            raise

    async def _add_documents_batch(self, docs):
        """Add a batch of documents to the individual table"""
        try:
            # Generate embeddings for all documents
            texts = [doc.page_content for doc in docs]
            loop = asyncio.get_event_loop()
            embeddings = await loop.run_in_executor(
                None, self.embeddings.embed_documents, texts
            )

            # Insert documents into the individual table
            safe_table_name = f'"{self.table_name}"'
            insert_sql = f"""
            INSERT INTO {safe_table_name} (content, embedding, langchain_metadata)
            VALUES (%s, %s, %s)
            """

            with self._conn.cursor() as cur:
                for doc, embedding in zip(docs, embeddings):
                    metadata_json = json.dumps(doc.metadata) if doc.metadata else '{}'
                    cur.execute(insert_sql, (
                        doc.page_content,
                        embedding,
                        metadata_json
                    ))

            logging.info(f"Successfully added {len(docs)} documents to table {self.table_name}")
        except Exception as e:
            logging.error(f"Error in _add_documents_batch: {e}")
            raise

    def load_document(self, docs):
        """Synchronous wrapper for loading documents"""
        try:
            # Run the async method in a new event loop
            asyncio.run(self.load_document_async(docs))
            logging.info(f"Successfully loaded {len(docs)} documents to table {self.index_name}")
        except Exception as e:
            logging.error(f"Error in load_document: {e}")
            raise
        

def batching_content(docs):
    """
    Batch a large list of docs into smaller chunks to control payload size
    """
    batch_size = BATCH_SIZE
    return [docs[i:i + batch_size] for i in range(0, len(docs), batch_size)]
