import logging
import os
import uuid
from dotenv import load_dotenv
import psycopg2
from psycopg2.extras import RealDictCursor

load_dotenv()

EMBEDDING_DIMENSION = int(os.getenv("EMBEDDING_DIMENSION", "1024"))
DATABASE_URL = os.getenv("DATABASE_URL")
DB_HOST = os.getenv("DB_HOST", "pe-db")
DB_PORT = os.getenv("DB_PORT", "5432")
DB_NAME = os.getenv("DB_NAME", "passisto")
DB_USER = os.getenv("DB_USER", "postgres")
DB_PASSWORD = os.getenv("DB_PASSWORD", "")


class Index:

    def __init__(self):
        self._pg_engine = None
        self._conn = None
        self._connect()

    def _build_sync_conn_str(self) -> str:
        # Build connection string for psycopg2
        conn_str = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
        return conn_str

    def _connect(self):
        try:
            # Sync connection for administrative SQL
            conn_str = self._build_sync_conn_str()
            logging.info(f"Connecting to: postgresql://{DB_USER}:***@{DB_HOST}:{DB_PORT}/{DB_NAME}")
            self._conn = psycopg2.connect(conn_str)
            self._conn.autocommit = True
            with self._conn.cursor() as cur:
                cur.execute("CREATE EXTENSION IF NOT EXISTS vector;")
            logging.info("Connected to Postgres")
        except Exception as e:
            logging.error(f"Failed to connect to Postgres: {e}")
            raise

    async def verify_index_existance(self, table_name: str):
        """Ensure the individual table exists for this collection."""
        try:
            # Check if table already exists
            if self._table_exists(table_name):
                logging.info(f"Table {table_name} already exists, skipping creation")
                return

            # Create the individual table
            self._create_individual_table(table_name)

            logging.info(f"Verified table exists: {table_name}")
        except Exception as e:
            logging.error(f"Error ensuring vector store table: {e}")
            raise

    def _create_individual_table(self, table_name: str):
        """Create an individual table for this collection"""
        try:
            with self._conn.cursor() as cur:
                # Enable vector extension
                cur.execute("CREATE EXTENSION IF NOT EXISTS vector;")

                # Create the individual table
                safe_table_name = f'"{table_name}"'
                create_table_sql = f"""
                CREATE TABLE IF NOT EXISTS {safe_table_name} (
                    langchain_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    content TEXT NOT NULL,
                    embedding vector({EMBEDDING_DIMENSION}) NOT NULL,
                    langchain_metadata JSON
                );
                """
                cur.execute(create_table_sql)

                # Create index for better performance
                index_name = f"idx_{table_name.replace('-', '_')}_embedding"[:63]
                cur.execute(f"""
                CREATE INDEX IF NOT EXISTS "{index_name}"
                ON {safe_table_name} USING hnsw (embedding vector_cosine_ops);
                """)

                logging.info(f"Created individual table: {table_name}")
        except Exception as e:
            logging.error(f"Error creating individual table: {e}")
            raise

    def _table_exists(self, table_name: str) -> bool:
        """Check if a table exists in the database."""
        try:
            with self._conn.cursor() as cur:
                cur.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_schema = 'public'
                        AND table_name = %s
                    );
                """, (table_name,))
                return cur.fetchone()[0]
        except Exception as e:
            logging.error(f"Error checking table existence: {e}")
            return False


    def truncate_source_documents(self, source: str, table_name: str):
        """
        Delete documents belonging to a specific source from the individual table.
        """
        try:
            safe_table = '"' + table_name.replace('"', '""') + '"'
            with self._conn.cursor() as cur:
                # Delete documents with matching source from the individual table
                cur.execute(f"DELETE FROM {safe_table} WHERE (langchain_metadata ->> 'source') = %s", (source,))
                deleted_count = cur.rowcount
                logging.info(f"Truncated {deleted_count} documents with source '{source}' from table '{table_name}'")
        except Exception as e:
            logging.error(f"Error truncating source documents: {e}")
