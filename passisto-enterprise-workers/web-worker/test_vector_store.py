#!/usr/bin/env python3
"""
Test script to verify the vector store fixes work correctly
"""

import asyncio
import logging
import os
import sys
from dotenv import load_dotenv
from langchain_core.documents import Document

# Add the website_scraper directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'website_scraper'))

from website_scraper.utils.loader import Load2Database
from website_scraper.utils.index import Index
from website_scraper.utils.table_utils import generate_safe_table_name, validate_table_name

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

async def test_table_name_generation():
    """Test the table name generation function"""
    print("\n=== Testing Table Name Generation ===")
    
    # Test with a long company ID and UUID (like the one causing issues)
    company_id = "1fa04585-286d-4cae-8179-fe69d8e1e101"
    uuid = "770775b2-68a5-4ccd-a408-63824260a13f"
    
    # Generate safe table name
    safe_name = generate_safe_table_name("web", company_id, uuid)
    print(f"Original would be: web-{company_id}-{uuid} (length: {len(f'web-{company_id}-{uuid}')})")
    print(f"Safe name: {safe_name} (length: {len(safe_name)})")
    
    # Validate the name
    is_valid = validate_table_name(safe_name)
    print(f"Is valid: {is_valid}")
    
    assert len(safe_name) <= 63, f"Table name too long: {len(safe_name)}"
    assert is_valid, "Generated table name is not valid"
    print("✓ Table name generation test passed")

async def test_vector_store_operations():
    """Test vector store operations"""
    print("\n=== Testing Vector Store Operations ===")
    
    # Generate a test table name
    test_table_name = generate_safe_table_name("test", "company123", "uuid456")
    print(f"Using test table: {test_table_name}")
    
    try:
        # Test index creation
        index = Index()
        await index.verify_index_existance(test_table_name)
        print("✓ Index creation test passed")
        
        # Test document loading
        loader = Load2Database(test_table_name)
        
        # Create test documents
        test_docs = [
            Document(
                page_content="This is a test document about AI and machine learning.",
                metadata={"source": "https://test.com/page1", "title": "Test Page 1"}
            ),
            Document(
                page_content="Another test document discussing vector databases.",
                metadata={"source": "https://test.com/page2", "title": "Test Page 2"}
            )
        ]
        
        # Load documents
        await loader.load_document_async(test_docs)
        print("✓ Document loading test passed")
        
        # Test truncation
        index.truncate_source_documents("https://test.com/page1", test_table_name)
        print("✓ Document truncation test passed")
        
    except Exception as e:
        print(f"✗ Vector store test failed: {e}")
        raise
    
    print("✓ All vector store operations tests passed")

async def test_table_existence_check():
    """Test table existence checking"""
    print("\n=== Testing Table Existence Check ===")
    
    test_table_name = generate_safe_table_name("exist_test", "company789", "uuid101")
    
    try:
        index = Index()
        
        # First call should create the table
        await index.verify_index_existance(test_table_name)
        print("✓ First table creation passed")
        
        # Second call should detect existing table and not recreate
        await index.verify_index_existance(test_table_name)
        print("✓ Table existence check passed")
        
    except Exception as e:
        print(f"✗ Table existence test failed: {e}")
        raise

async def main():
    """Run all tests"""
    print("Starting Vector Store Tests...")
    
    try:
        await test_table_name_generation()
        await test_vector_store_operations()
        await test_table_existence_check()
        
        print("\n🎉 All tests passed successfully!")
        
    except Exception as e:
        print(f"\n❌ Tests failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
