#!/usr/bin/env python3
"""
Test script to verify the web worker works with the LangChain schema
"""

import asyncio
import logging
import os
import sys
from dotenv import load_dotenv
from langchain_core.documents import Document

# Add the website_scraper directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'website_scraper'))

from website_scraper.utils.loader import Load2Database
from website_scraper.utils.index import Index

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

async def test_langchain_schema():
    """Test the LangChain schema integration"""
    print("\n=== Testing LangChain Schema Integration ===")
    
    # Use a test collection name
    test_collection = "test-collection-langchain"
    
    try:
        # Test index creation
        print("1. Testing collection creation...")
        index = Index()
        await index.verify_index_existance(test_collection)
        print("✓ Collection creation test passed")
        
        # Test document loading
        print("2. Testing document loading...")
        loader = Load2Database(test_collection)
        
        # Create test documents with metadata
        test_docs = [
            Document(
                page_content="Passisto is an enterprise AI platform that helps teams collaborate and automate workflows.",
                metadata={
                    "source": "https://passisto.com/about", 
                    "title": "About Passisto",
                    "type": "webpage"
                }
            ),
            Document(
                page_content="The platform includes AI agents for email generation, form building, and interview automation.",
                metadata={
                    "source": "https://passisto.com/features", 
                    "title": "Passisto Features",
                    "type": "webpage"
                }
            ),
            Document(
                page_content="Passisto Enterprise offers advanced security features and custom integrations for large organizations.",
                metadata={
                    "source": "https://passisto.com/enterprise", 
                    "title": "Enterprise Edition",
                    "type": "webpage"
                }
            )
        ]
        
        # Load documents
        await loader.load_document_async(test_docs)
        print("✓ Document loading test passed")
        
        # Test truncation
        print("3. Testing document truncation...")
        index.truncate_source_documents("https://passisto.com/about", test_collection)
        print("✓ Document truncation test passed")
        
        # Load documents again to test after truncation
        print("4. Testing document loading after truncation...")
        await loader.load_document_async(test_docs)
        print("✓ Document loading after truncation test passed")
        
    except Exception as e:
        print(f"✗ LangChain schema test failed: {e}")
        raise
    
    print("✓ All LangChain schema tests passed")

async def test_collection_management():
    """Test collection management functionality"""
    print("\n=== Testing Collection Management ===")
    
    test_collections = [
        "web-company1-uuid1",
        "web-company2-uuid2", 
        "ftp-company1-uuid3"
    ]
    
    try:
        index = Index()
        
        for collection_name in test_collections:
            print(f"Creating collection: {collection_name}")
            await index.verify_index_existance(collection_name)
            
        print("✓ Multiple collection creation test passed")
        
    except Exception as e:
        print(f"✗ Collection management test failed: {e}")
        raise

async def main():
    """Run all tests"""
    print("Starting LangChain Schema Tests...")
    
    try:
        await test_langchain_schema()
        await test_collection_management()
        
        print("\n🎉 All tests passed successfully!")
        print("\nThe web worker is now compatible with the backend's LangChain schema!")
        
    except Exception as e:
        print(f"\n❌ Tests failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
