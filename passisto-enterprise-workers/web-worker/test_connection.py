#!/usr/bin/env python3
"""
Simple test to verify database connection works
"""

import os
import sys
import psycopg2
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database configuration
DB_HOST = os.getenv("DB_HOST", "pe-db")
DB_PORT = os.getenv("DB_PORT", "5432")
DB_NAME = os.getenv("DB_NAME", "passisto")
DB_USER = os.getenv("DB_USER", "postgres")
DB_PASSWORD = os.getenv("DB_PASSWORD", "")

def test_connection():
    """Test basic database connection"""
    try:
        conn_str = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
        print(f"Connecting to: postgresql://{DB_USER}:***@{DB_HOST}:{DB_PORT}/{DB_NAME}")
        
        conn = psycopg2.connect(conn_str)
        conn.autocommit = True
        
        with conn.cursor() as cur:
            # Test basic query
            cur.execute("SELECT version();")
            version = cur.fetchone()[0]
            print(f"✓ Connected successfully!")
            print(f"PostgreSQL version: {version}")
            
            # Test vector extension
            cur.execute("CREATE EXTENSION IF NOT EXISTS vector;")
            print("✓ Vector extension enabled")
            
            # Test table creation
            test_table = "test_connection_table"
            cur.execute(f'DROP TABLE IF EXISTS "{test_table}";')
            cur.execute(f'''
                CREATE TABLE "{test_table}" (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    content TEXT NOT NULL,
                    embedding vector(1024),
                    metadata JSON
                );
            ''')
            print("✓ Test table created successfully")
            
            # Clean up
            cur.execute(f'DROP TABLE "{test_table}";')
            print("✓ Test table cleaned up")
            
        conn.close()
        print("✓ Connection test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Connection test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_connection()
    sys.exit(0 if success else 1)
