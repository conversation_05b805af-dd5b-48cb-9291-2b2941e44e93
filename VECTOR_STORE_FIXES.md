# Vector Store Fixes - Source Metadata Issue Resolution

## Problem Summary

The chatbot was returning empty sources because of several issues:

1. **<PERSON>hema Mismatch**: Web worker was creating individual tables per collection, but backend expected standard LangChain schema
2. **Table Name Length**: PostgreSQL 63-character limit was causing table name truncation
3. **Column Naming**: Inconsistency between `metadata` and `langchain_metadata` columns
4. **Missing Source Metadata**: Documents weren't being properly stored with source information

## Changes Made

### 1. Backend Changes

#### `src/controllers/integrations/web.controller.js`
- Added `generateSafeTableName()` function to handle PostgreSQL identifier limits
- Updated collection name generation to use safe naming strategy
- Collection names now follow pattern: `web-{shortCompanyId}-{shortUuid}-{hash}`

#### `src/utils/chatbot-utils/chatbot-flow.js`
- Enhanced `retrieveDocuments()` function with better logging and configuration
- Increased document retrieval count (k=10) for better context
- Improved source extraction in `generateAnswer()` function
- Added debugging logs for source tracking

### 2. Web Worker Changes

#### `website_scraper/utils/loader.py`
- **Complete rewrite** to use standard LangChain schema
- Now uses `langchain_pg_embedding` and `langchain_pg_collection` tables
- Proper collection management with UUID-based relationships
- Compatible document insertion with correct metadata structure

#### `website_scraper/utils/index.py`
- Updated to work with LangChain schema instead of individual tables
- Proper collection creation and management
- Fixed truncation to work with collection-based approach

#### `website_scraper/utils/table_utils.py` (New)
- Utility functions for safe table name generation
- PostgreSQL identifier validation

#### `website_scraper/pipelines.py`
- Updated to use collection names instead of table names
- Consistent with new schema approach

## Database Schema

The system now uses the standard LangChain PostgreSQL schema:

### `langchain_pg_collection`
```sql
CREATE TABLE langchain_pg_collection (
    name VARCHAR,
    cmetadata JSON,
    uuid UUID PRIMARY KEY DEFAULT gen_random_uuid()
);
```

### `langchain_pg_embedding`
```sql
CREATE TABLE langchain_pg_embedding (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    text TEXT,
    metadata JSONB,
    embedding vector(1024),
    collection_id UUID,
    FOREIGN KEY (collection_id) REFERENCES langchain_pg_collection(uuid) ON DELETE CASCADE
);
```

## Testing

### 1. Test Web Worker Schema Compatibility
```bash
cd passisto-enterprise-workers/web-worker
python test_langchain_schema.py
```

### 2. Test Backend Source Retrieval
```bash
cd passisto-enterprise-backend-v2
node test-chatbot-sources.js
```

### 3. Test End-to-End Flow
1. Create a new web integration through the frontend
2. Let it scrape some pages
3. Ask questions in the chatbot
4. Verify sources are returned in the response

## Expected Results

After these fixes:

1. **No more table name truncation errors**
2. **No more duplicate table creation errors** 
3. **No more schema mismatch errors**
4. **Chatbot responses include proper source metadata**
5. **Web worker and backend use consistent schema**

## Migration Notes

- Existing individual collection tables (like `web-1fa04585-...`) will remain but won't be used for new data
- New documents will be stored in the standard LangChain tables
- Old data can be migrated if needed by reading from old tables and inserting into new schema

## Verification Commands

Check that the standard tables exist:
```sql
\dt langchain_pg_*
```

Check collection data:
```sql
SELECT name, uuid FROM langchain_pg_collection;
```

Check document count per collection:
```sql
SELECT c.name, COUNT(e.id) as doc_count 
FROM langchain_pg_collection c 
LEFT JOIN langchain_pg_embedding e ON c.uuid = e.collection_id 
GROUP BY c.name, c.uuid;
```

## Next Steps

1. Run the test scripts to verify fixes
2. Test with a real web integration
3. Monitor logs for any remaining issues
4. Consider migrating existing data if needed
