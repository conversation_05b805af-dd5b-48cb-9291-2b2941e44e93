#!/usr/bin/env python3
"""
Test script to verify FTP worker database connection fix
"""

import os
import sys
from dotenv import load_dotenv

# Add the FTP worker utils to the path
sys.path.append('passisto-enterprise-workers/ftp-worker')

def test_database_connection():
    """Test the database connection with the fixed pgvector client"""
    print("🔍 Testing FTP worker database connection...")
    
    # Load environment variables
    load_dotenv()
    
    # Print current DATABASE_URL (masked for security)
    database_url = os.getenv('DATABASE_URL', 'Not set')
    if database_url != 'Not set':
        # Mask password for security
        masked_url = database_url
        if '@' in masked_url:
            parts = masked_url.split('@')
            if ':' in parts[0]:
                user_pass = parts[0].split(':')
                if len(user_pass) >= 2:
                    masked_url = f"{user_pass[0]}:***@{parts[1]}"
        print(f"DATABASE_URL: {masked_url}")
        print(f"Contains query params: {'?' in database_url}")
        
        # Check for schema parameter specifically
        if 'schema=' in database_url:
            print("⚠️  Found 'schema=' parameter in DATABASE_URL")
        else:
            print("✅ No 'schema=' parameter found")
    else:
        print("DATABASE_URL: Not set (will use individual env vars)")
    
    try:
        # Import and test the pgvector client
        from utils.pgvector_client import get_pgvector_client
        
        print("\n🔌 Attempting to create pgvector client...")
        client = get_pgvector_client()
        print("✅ PgVector client created successfully!")
        
        # Test connection
        conn = client.get_connection()
        print("✅ Database connection established!")
        
        # Test basic query
        with conn.cursor() as cur:
            cur.execute("SELECT version();")
            version = cur.fetchone()[0]
            print(f"✅ PostgreSQL version: {version[:50]}...")
            
            # Check if vector extension is available
            cur.execute("SELECT EXISTS(SELECT 1 FROM pg_extension WHERE extname = 'vector');")
            has_vector = cur.fetchone()[0]
            print(f"✅ Vector extension available: {has_vector}")
        
        print("\n🎉 All database connection tests passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Database connection test failed: {e}")
        print(f"Error type: {type(e).__name__}")
        
        # Check if it's the specific schema error
        if "invalid URI query parameter" in str(e) and "schema" in str(e):
            print("\n🔧 This appears to be the schema parameter issue.")
            print("The fix should handle this, but the DATABASE_URL might have other issues.")
        
        return False

def test_table_creation():
    """Test creating an individual table"""
    print("\n🔍 Testing individual table creation...")
    
    try:
        from utils.pgvector_client import get_pgvector_client
        
        client = get_pgvector_client()
        test_table_name = "test-ftp-collection-123"
        
        print(f"📋 Creating test table: {test_table_name}")
        client.ensure_individual_table_exists(test_table_name)
        print("✅ Test table created successfully!")
        
        # Verify table exists
        conn = client.get_connection()
        with conn.cursor() as cur:
            cur.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = %s
                );
            """, (test_table_name,))
            
            table_exists = cur.fetchone()[0]
            print(f"✅ Table exists in database: {table_exists}")
            
            if table_exists:
                # Check table structure
                cur.execute("""
                    SELECT column_name, data_type 
                    FROM information_schema.columns 
                    WHERE table_name = %s 
                    ORDER BY ordinal_position;
                """, (test_table_name,))
                
                columns = cur.fetchall()
                print("📋 Table structure:")
                for col_name, col_type in columns:
                    print(f"  - {col_name}: {col_type}")
        
        return True
        
    except Exception as e:
        print(f"❌ Table creation test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("🧪 FTP WORKER DATABASE CONNECTION TESTS")
    print("=" * 60)
    
    # Test 1: Database connection
    connection_ok = test_database_connection()
    
    if connection_ok:
        # Test 2: Table creation
        table_ok = test_table_creation()
        
        if table_ok:
            print("\n" + "=" * 60)
            print("🎉 ALL TESTS PASSED!")
            print("✅ FTP worker database connection is working correctly")
            print("✅ Individual table approach is functional")
            print("=" * 60)
        else:
            print("\n" + "=" * 60)
            print("⚠️  CONNECTION OK, BUT TABLE CREATION FAILED")
            print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("❌ DATABASE CONNECTION FAILED")
        print("Please check your DATABASE_URL and database configuration")
        print("=" * 60)

if __name__ == "__main__":
    main()
