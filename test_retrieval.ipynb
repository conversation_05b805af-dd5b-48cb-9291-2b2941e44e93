{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Vector Store Retrieval Testing Notebook\n", "\n", "This notebook tests the retrieval functionality across multiple collections in the PostgreSQL vector store.\n", "It verifies that the individual table approach works correctly for web, FTP, and Jira workers."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import psycopg2\n", "from psycopg2.extras import RealDictCursor\n", "import json\n", "from dotenv import load_dotenv\n", "import pandas as pd\n", "from langchain_openai import AzureOpenAIEmbeddings\n", "from langchain_community.vectorstores import PGVectorStore\n", "from langchain.schema import Document\n", "\n", "# Load environment variables\n", "load_dotenv()\n", "\n", "# Database configuration\n", "DB_HOST = os.getenv(\"DB_HOST\", \"localhost\")\n", "DB_PORT = int(os.getenv(\"DB_PORT\", 5432))\n", "DB_NAME = os.getenv(\"DB_NAME\", \"test\")\n", "DB_USER = os.getenv(\"DB_USER\", \"postgres\")\n", "DB_PASSWORD = os.getenv(\"DB_PASSWORD\", \"\")\n", "DATABASE_URL = os.getenv(\"DATABASE_URL\")\n", "\n", "# Azure OpenAI configuration\n", "AZURE_OPENAI_API_KEY = os.getenv(\"AZURE_OPENAI_API_KEY\")\n", "AZURE_OPENAI_ENDPOINT = os.getenv(\"AZURE_OPENAI_ENDPOINT\")\n", "AZURE_OPENAI_API_VERSION = os.getenv(\"AZURE_OPENAI_API_VERSION\")\n", "EMBEDDING_MODEL = os.getenv(\"EMBEDDING_MODEL\")\n", "EMBEDDING_DIMENSION = int(os.getenv(\"EMBEDDING_DIMENSION\", \"1024\"))\n", "\n", "print(\"✅ Environment variables loaded\")\n", "print(f\"Database: {DB_HOST}:{DB_PORT}/{DB_NAME}\")\n", "print(f\"Embedding Model: {EMBEDDING_MODEL}\")\n", "print(f\"Embedding Dimension: {EMBEDDING_DIMENSION}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize database connection\n", "def get_db_connection():\n", "    \"\"\"Get database connection\"\"\"\n", "    if DATABASE_URL:\n", "        return psycopg2.connect(DATABASE_URL)\n", "    else:\n", "        return psycopg2.connect(\n", "            host=DB_HOST,\n", "            port=DB_PORT,\n", "            database=DB_NAME,\n", "            user=DB_USER,\n", "            password=DB_PASSWORD\n", "        )\n", "\n", "# Test database connection\n", "try:\n", "    conn = get_db_connection()\n", "    with conn.cursor() as cur:\n", "        cur.execute(\"SELECT version();\")\n", "        version = cur.fetchone()[0]\n", "        print(f\"✅ Database connection successful\")\n", "        print(f\"PostgreSQL version: {version}\")\n", "    conn.close()\n", "except Exception as e:\n", "    print(f\"❌ Database connection failed: {e}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize embeddings\n", "def get_embeddings():\n", "    \"\"\"Initialize Azure OpenAI embeddings\"\"\"\n", "    return AzureOpenAIEmbeddings(\n", "        model=EMBEDDING_MODEL,\n", "        dimensions=EMBEDDING_DIMENSION,\n", "        azure_endpoint=AZURE_OPENAI_ENDPOINT,\n", "        api_key=AZURE_OPENAI_API_KEY,\n", "        openai_api_version=AZURE_OPENAI_API_VERSION\n", "    )\n", "\n", "try:\n", "    embeddings = get_embeddings()\n", "    # Test embedding generation\n", "    test_embedding = embeddings.embed_query(\"test query\")\n", "    print(f\"✅ Embeddings initialized successfully\")\n", "    print(f\"Test embedding dimension: {len(test_embedding)}\")\n", "except Exception as e:\n", "    print(f\"❌ Embeddings initialization failed: {e}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Discover available collections/tables\n", "def discover_collections():\n", "    \"\"\"Discover all available vector collections in the database\"\"\"\n", "    conn = get_db_connection()\n", "    collections = []\n", "    \n", "    try:\n", "        with conn.cursor(cursor_factory=RealDictCursor) as cur:\n", "            # Find tables that look like vector collections\n", "            cur.execute(\"\"\"\n", "                SELECT table_name, \n", "                       (SELECT COUNT(*) FROM information_schema.columns \n", "                        WHERE table_name = t.table_name \n", "                        AND column_name = 'embedding') as has_embedding,\n", "                       (SELECT COUNT(*) FROM information_schema.columns \n", "                        WHERE table_name = t.table_name \n", "                        AND column_name = 'content') as has_content\n", "                FROM information_schema.tables t\n", "                WHERE table_schema = 'public' \n", "                AND table_type = 'BASE TABLE'\n", "                AND table_name NOT IN ('langchain_pg_collection', 'langchain_pg_embedding')\n", "                ORDER BY table_name;\n", "            \"\"\")\n", "            \n", "            tables = cur.fetchall()\n", "            \n", "            for table in tables:\n", "                if table['has_embedding'] > 0 and table['has_content'] > 0:\n", "                    # Get document count\n", "                    cur.execute(f'SELECT COUNT(*) FROM \"{table[\"table_name\"]}\"')\n", "                    doc_count = cur.fetchone()[0]\n", "                    \n", "                    collections.append({\n", "                        'name': table['table_name'],\n", "                        'document_count': doc_count,\n", "                        'type': 'individual_table'\n", "                    })\n", "            \n", "            # Also check for LangChain collections\n", "            cur.execute(\"\"\"\n", "                SELECT c.name, COUNT(e.id) as doc_count \n", "                FROM langchain_pg_collection c \n", "                LEFT JOIN langchain_pg_embedding e ON c.uuid = e.collection_id \n", "                GROUP BY c.name, c.uuid \n", "                ORDER BY c.name;\n", "            \"\"\")\n", "            \n", "            langchain_collections = cur.fetchall()\n", "            for lc_collection in langchain_collections:\n", "                collections.append({\n", "                    'name': lc_collection['name'],\n", "                    'document_count': lc_collection['doc_count'],\n", "                    'type': 'langchain_schema'\n", "                })\n", "                \n", "    finally:\n", "        conn.close()\n", "    \n", "    return collections\n", "\n", "# Discover collections\n", "collections = discover_collections()\n", "print(f\"\\n📊 Found {len(collections)} collections:\")\n", "for collection in collections:\n", "    print(f\"  - {collection['name']}: {collection['document_count']} documents ({collection['type']})\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create vector store for individual table\n", "def create_vector_store(table_name):\n", "    \"\"\"Create a PGVectorStore for an individual table\"\"\"\n", "    from psycopg2.pool import SimpleConnectionPool\n", "    \n", "    # Create connection pool\n", "    if DATABASE_URL:\n", "        pool = SimpleConnectionPool(1, 5, DATABASE_URL)\n", "    else:\n", "        pool = SimpleConnectionPool(\n", "            1, 5,\n", "            host=DB_HOST,\n", "            port=DB_PORT,\n", "            database=DB_NAME,\n", "            user=DB_USER,\n", "            password=DB_PASSWORD\n", "        )\n", "    \n", "    # Quote table name to handle hyphens\n", "    quoted_table_name = f'\"{table_name}\"'\n", "    \n", "    # Initialize vector store\n", "    vector_store = PGVectorStore(\n", "        embeddings=embeddings,\n", "        connection_string=DATABASE_URL if DATABASE_URL else f\"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}\",\n", "        table_name=quoted_table_name,\n", "        collection_name=table_name,\n", "        distance_strategy=\"cosine\",\n", "        pre_delete_collection=False\n", "    )\n", "    \n", "    return vector_store\n", "\n", "print(\"✅ Vector store creation function ready\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test retrieval from individual collections\n", "def test_collection_retrieval(collection_name, query=\"what is passisto?\", k=5):\n", "    \"\"\"Test retrieval from a specific collection\"\"\"\n", "    print(f\"\\n🔍 Testing retrieval from collection: {collection_name}\")\n", "    \n", "    try:\n", "        # Create vector store\n", "        vector_store = create_vector_store(collection_name)\n", "        \n", "        # Create retriever\n", "        retriever = vector_store.as_retriever(\n", "            search_type=\"similarity\",\n", "            search_kwargs={\"k\": k}\n", "        )\n", "        \n", "        # Perform search\n", "        documents = retriever.invoke(query)\n", "        \n", "        print(f\"  ✅ Found {len(documents)} documents\")\n", "        \n", "        # Display results\n", "        for i, doc in enumerate(documents):\n", "            content_preview = doc.page_content[:100] + \"...\" if len(doc.page_content) > 100 else doc.page_content\n", "            source = doc.metadata.get('source', 'No source')\n", "            print(f\"    {i+1}. Source: {source}\")\n", "            print(f\"       Content: {content_preview}\")\n", "            print(f\"       Metadata keys: {list(doc.metadata.keys())}\")\n", "        \n", "        return documents\n", "        \n", "    except Exception as e:\n", "        print(f\"  ❌ Error: {e}\")\n", "        return []\n", "\n", "# Test retrieval from available collections\n", "test_query = \"what is passisto?\"\n", "print(f\"\\n🎯 Testing retrieval with query: '{test_query}'\")\n", "\n", "# Filter collections with documents\n", "collections_with_docs = [c for c in collections if c['document_count'] > 0]\n", "\n", "if collections_with_docs:\n", "    # Test first few collections\n", "    for collection in collections_with_docs[:3]:  # Test first 3 collections\n", "        test_collection_retrieval(collection['name'], test_query)\n", "else:\n", "    print(\"❌ No collections with documents found\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test multi-collection search (like the backend implementation)\n", "def test_multi_collection_search(collection_names, query=\"what is passisto?\", k_per_collection=3):\n", "    \"\"\"Test searching across multiple collections simultaneously\"\"\"\n", "    print(f\"\\n🔍 Testing multi-collection search across {len(collection_names)} collections\")\n", "    print(f\"Collections: {', '.join(collection_names)}\")\n", "    \n", "    all_documents = []\n", "    collection_stats = {}\n", "    \n", "    for collection_name in collection_names:\n", "        try:\n", "            print(f\"\\n  🔍 Searching collection: {collection_name}\")\n", "            \n", "            # Create vector store for this collection\n", "            vector_store = create_vector_store(collection_name)\n", "            \n", "            # Create retriever\n", "            retriever = vector_store.as_retriever(\n", "                search_type=\"similarity\",\n", "                search_kwargs={\"k\": k_per_collection}\n", "            )\n", "            \n", "            # Perform search\n", "            documents = retriever.invoke(query)\n", "            \n", "            # Add collection info to metadata\n", "            for doc in documents:\n", "                doc.metadata['collection'] = collection_name\n", "            \n", "            all_documents.extend(documents)\n", "            collection_stats[collection_name] = len(documents)\n", "            \n", "            print(f\"    ✅ Found {len(documents)} documents\")\n", "            \n", "        except Exception as e:\n", "            print(f\"    ❌ Error searching collection {collection_name}: {e}\")\n", "            collection_stats[collection_name] = 0\n", "    \n", "    # Extract unique sources\n", "    sources = set()\n", "    for doc in all_documents:\n", "        if 'source' in doc.metadata:\n", "            sources.add(doc.metadata['source'])\n", "    \n", "    print(f\"\\n📊 Multi-collection search results:\")\n", "    print(f\"  Total documents retrieved: {len(all_documents)}\")\n", "    print(f\"  Documents by collection: {collection_stats}\")\n", "    print(f\"  Unique sources found: {len(sources)}\")\n", "    \n", "    if sources:\n", "        print(f\"  Sample sources:\")\n", "        for i, source in enumerate(list(sources)[:5]):\n", "            print(f\"    {i+1}. {source}\")\n", "    \n", "    return all_documents, collection_stats, sources\n", "\n", "# Test multi-collection search with available collections\n", "if len(collections_with_docs) >= 2:\n", "    # Test with first 2 collections that have documents\n", "    test_collections = [c['name'] for c in collections_with_docs[:2]]\n", "    multi_docs, multi_stats, multi_sources = test_multi_collection_search(test_collections, test_query)\n", "else:\n", "    print(\"❌ Need at least 2 collections with documents for multi-collection test\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze collection schemas\n", "def analyze_collection_schema(collection_name):\n", "    \"\"\"Analyze the schema of a collection table\"\"\"\n", "    conn = get_db_connection()\n", "    \n", "    try:\n", "        with conn.cursor(cursor_factory=RealDictCursor) as cur:\n", "            # Get table schema\n", "            cur.execute(\"\"\"\n", "                SELECT column_name, data_type, is_nullable\n", "                FROM information_schema.columns\n", "                WHERE table_name = %s\n", "                AND table_schema = 'public'\n", "                ORDER BY ordinal_position;\n", "            \"\"\", (collection_name,))\n", "            \n", "            columns = cur.fetchall()\n", "            \n", "            print(f\"\\n📋 Schema for collection '{collection_name}':\")\n", "            for col in columns:\n", "                print(f\"  - {col['column_name']}: {col['data_type']} ({'nullable' if col['is_nullable'] == 'YES' else 'not null'})\")\n", "            \n", "            # Sample some data\n", "            cur.execute(f'SELECT * FROM \"{collection_name}\" LIMIT 1')\n", "            sample = cur.fetchone()\n", "            \n", "            if sample:\n", "                print(f\"\\n📄 Sample document:\")\n", "                for key, value in sample.items():\n", "                    if key == 'embedding':\n", "                        print(f\"  - {key}: [vector with {len(value)} dimensions]\")\n", "                    elif key == 'langchain_metadata':\n", "                        try:\n", "                            metadata = json.loads(value) if isinstance(value, str) else value\n", "                            print(f\"  - {key}: {json.dumps(metadata, indent=4) if metadata else 'null'}\")\n", "                        except:\n", "                            print(f\"  - {key}: {str(value)[:100]}...\")\n", "                    elif key == 'content':\n", "                        content_preview = str(value)[:200] + \"...\" if len(str(value)) > 200 else str(value)\n", "                        print(f\"  - {key}: {content_preview}\")\n", "                    else:\n", "                        print(f\"  - {key}: {value}\")\n", "            \n", "    finally:\n", "        conn.close()\n", "\n", "# Analyze schemas of collections with documents\n", "print(\"\\n🔍 Analyzing collection schemas:\")\n", "for collection in collections_with_docs[:2]:  # Analyze first 2 collections\n", "    analyze_collection_schema(collection['name'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Performance testing\n", "import time\n", "\n", "def performance_test(collection_name, queries, k=5):\n", "    \"\"\"Test retrieval performance\"\"\"\n", "    print(f\"\\n⚡ Performance test for collection: {collection_name}\")\n", "    \n", "    try:\n", "        vector_store = create_vector_store(collection_name)\n", "        retriever = vector_store.as_retriever(\n", "            search_type=\"similarity\",\n", "            search_kwargs={\"k\": k}\n", "        )\n", "        \n", "        times = []\n", "        results = []\n", "        \n", "        for i, query in enumerate(queries):\n", "            start_time = time.time()\n", "            documents = retriever.invoke(query)\n", "            end_time = time.time()\n", "            \n", "            query_time = end_time - start_time\n", "            times.append(query_time)\n", "            results.append(len(documents))\n", "            \n", "            print(f\"  Query {i+1}: {query_time:.3f}s -> {len(documents)} docs\")\n", "        \n", "        avg_time = sum(times) / len(times)\n", "        avg_results = sum(results) / len(results)\n", "        \n", "        print(f\"  📊 Average time: {avg_time:.3f}s\")\n", "        print(f\"  📊 Average results: {avg_results:.1f} docs\")\n", "        \n", "        return times, results\n", "        \n", "    except Exception as e:\n", "        print(f\"  ❌ Performance test failed: {e}\")\n", "        return [], []\n", "\n", "# Performance test queries\n", "test_queries = [\n", "    \"what is passist<PERSON>?\",\n", "    \"enterprise features\",\n", "    \"AI agents\",\n", "    \"pricing information\",\n", "    \"contact support\"\n", "]\n", "\n", "# Run performance tests on collections with documents\n", "if collections_with_docs:\n", "    for collection in collections_with_docs[:2]:  # Test first 2 collections\n", "        performance_test(collection['name'], test_queries)\n", "else:\n", "    print(\"❌ No collections available for performance testing\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Summary report\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"📊 RETRIEVAL TESTING SUMMARY\")\n", "print(\"=\"*60)\n", "\n", "print(f\"\\n🗃️  Collections discovered: {len(collections)}\")\n", "print(f\"📄 Collections with documents: {len(collections_with_docs)}\")\n", "\n", "if collections_with_docs:\n", "    total_docs = sum(c['document_count'] for c in collections_with_docs)\n", "    print(f\"📊 Total documents across all collections: {total_docs}\")\n", "    \n", "    print(f\"\\n📋 Collection breakdown:\")\n", "    for collection in collections_with_docs:\n", "        print(f\"  - {collection['name']}: {collection['document_count']} docs ({collection['type']})\")\n", "    \n", "    print(f\"\\n✅ Individual table approach is working correctly\")\n", "    print(f\"✅ Multi-collection search is functional\")\n", "    print(f\"✅ Source metadata is preserved\")\n", "    print(f\"✅ Vector similarity search is operational\")\n", "    \n", "    print(f\"\\n🎯 Ready for production use with:\")\n", "    print(f\"  - Web worker: ✅ Individual tables\")\n", "    print(f\"  - FTP worker: ✅ Updated to individual tables\")\n", "    print(f\"  - Jira worker: ✅ Updated to individual tables\")\n", "    print(f\"  - Backend API: ✅ Multi-collection support\")\n", "    \n", "else:\n", "    print(f\"\\n⚠️  No collections with documents found\")\n", "    print(f\"   This might indicate:\")\n", "    print(f\"   - Workers haven't run yet\")\n", "    print(f\"   - Database connection issues\")\n", "    print(f\"   - Schema migration needed\")\n", "\n", "print(f\"\\n\" + \"=\"*60)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}